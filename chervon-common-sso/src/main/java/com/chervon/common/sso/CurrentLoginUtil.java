package com.chervon.common.sso;

import cn.dev33.satoken.stp.StpUtil;
import com.chervon.common.core.domain.LoginSysUser;
import com.chervon.common.core.domain.LoginUserContext;

/**
 * <AUTHOR>
 * @date 2023/4/16 19:01
 */
public class CurrentLoginUtil {

    public static LoginSysUser getCurrent() {
        if (StpUtil.isLogin()) {
            if (StpUtil.getLoginType().equals("user")) {
                return (LoginSysUser) StpUtil.getSession().get(StpUtil.getLoginIdAsString());
            }
            if (StpUtil.getLoginType().equals("app")) {
                LoginSysUser app = new LoginSysUser();
                app.setId(StpUtil.getLoginIdAsLong());
                app.setLoginType("app");
                return app;
            }
            return new LoginSysUser();
        }
        LoginSysUser user = LoginUserContext.getUser();
        if (user == null) {
            user = new LoginSysUser();
        }
        return user;
    }

    public static Long getCurrentId() {
        if (StpUtil.isLogin()) {
            return StpUtil.getLoginIdAsLong();
        }
        LoginSysUser user = LoginUserContext.getUser();
        if (user == null) {
            user = new LoginSysUser();
        }
        return user.getId();
    }

    public static String getCurrentIdAsString() {
        Long currentId = getCurrentId();
        if (currentId == null) {
            return null;
        }
        return String.valueOf(currentId);
    }
}
