package com.chervon.common.sso;

import cn.dev33.satoken.sso.SaSsoProcessor;
import cn.dev33.satoken.sso.SaSsoUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.chervon.common.core.domain.LoginSysUser;
import com.chervon.common.core.domain.LoginUserContext;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 前后台分离架构下集成SSO所需的代码
 *
 * <AUTHOR>
 */
@RestController
public class H5Controller {

    // 返回SSO认证中心登录地址 （前后台分离环境下专用）
    @RequestMapping("/sso/getSsoAuthUrl")
    public SaResult getSsoAuthUrl(String clientLoginUrl) {
        String serverAuthUrl = SaSsoUtil.buildServerAuthUrl(clientLoginUrl, "");
        return SaResult.data(serverAuthUrl);
    }

    // 根据ticket进行登录（前后台分离环境下专用）
    @RequestMapping("/sso/doLoginByTicket")
    public SaResult doLoginByTicket(String ticket) {
        Object loginId = SaSsoProcessor.instance.checkTicket(ticket, "/sso/doLoginByTicket");
        if (loginId != null) {
            StpUtil.login(loginId);
            LoginSysUser loginSysUser = SsoMethodUtil.getCurrUser();
            loginSysUser.setAccessToken(StpUtil.getTokenValue());
            loginSysUser.setLoginType("user");
            // 设置login context
            LoginUserContext.setUser(loginSysUser);
            StpUtil.getSession().set(StpUtil.getLoginIdAsString(), loginSysUser);
            return SaResult.data(StpUtil.getTokenValue());
        }
        return SaResult.error("无效ticket：" + ticket);
    }

}
