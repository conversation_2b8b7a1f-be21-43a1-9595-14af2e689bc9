package com.chervon.iot.app.api.enums;

import java.util.Arrays;

/**
* <AUTHOR> 2024/7/30
*/
public enum BusinessTypeEnum {
    EGO_CONNECT(1, "ego"),
    FLEET(2, "fleet");

    private int type;
    private String desc;

    private BusinessTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return this.type;
    }

    public String getDesc() {
        return this.desc;
    }

    public static String getDesc(int type) {
        return (String) Arrays.stream(values()).filter((x) -> {
            return x.getType() == type;
        }).map(BusinessTypeEnum::getDesc).findFirst().orElse(null);
    }

    public static BusinessTypeEnum getEnum(int type) {
        return (BusinessTypeEnum) Arrays.stream(values()).filter((x) -> {
            return x.getType() == type;
        }).findFirst().orElse(null);
    }
}