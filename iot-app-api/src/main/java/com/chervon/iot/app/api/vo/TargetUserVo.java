package com.chervon.iot.app.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 目标用户
 */
@Data
public class TargetUserVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户Id
     */
    private Long userId;
    /**
     * firstName
     */
    @ApiModelProperty("firstName")
    private String firstName;
    /**
     *lastName
     */
    @ApiModelProperty("lastName")
    private String lastName;
    /**
     *国家编码
     */
    @ApiModelProperty("国家编码")
    private String countryCode;
    /**
     *电话号码
     */
    @ApiModelProperty("电话号码")
    private String telNumber;
    /**
     *是否电话通知：1通知  0不通知
     */
    @ApiModelProperty("是否电话通知：1通知  0不通知")
    private Integer notificationByCall;
    /**
     *是否短信通知：1通知  0不通知
     */
    @ApiModelProperty("是否短信通知：1通知  0不通知")
    private Integer notificationByMsg;

    /**
     * 语音呼叫语言
     */
    @ApiModelProperty("语音呼叫语言")
    private String language;
}
