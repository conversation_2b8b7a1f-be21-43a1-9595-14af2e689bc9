package com.chervon.common.core.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ClassUtils工具类测试
 * 
 * <AUTHOR> Assistant
 */
@DisplayName("ClassUtils工具类测试")
class ClassUtilsTest {

    @Nested
    @DisplayName("类存在性检查测试")
    class ExistTests {

        @Test
        @DisplayName("应该正确识别存在的JDK核心类")
        void shouldReturnTrueForExistingJdkClasses() {
            // Given & When & Then
            assertTrue(ClassUtils.exist("java.lang.String"), "String类应该存在");
            assertTrue(ClassUtils.exist("java.lang.Object"), "Object类应该存在");
            assertTrue(ClassUtils.exist("java.util.List"), "List接口应该存在");
            assertTrue(ClassUtils.exist("java.util.ArrayList"), "ArrayList类应该存在");
            assertTrue(ClassUtils.exist("java.util.HashMap"), "HashMap类应该存在");
            assertTrue(ClassUtils.exist("java.lang.Integer"), "Integer类应该存在");
        }

        @Test
        @DisplayName("应该正确识别存在的测试类")
        void shouldReturnTrueForExistingTestClasses() {
            // Given & When & Then
            assertTrue(ClassUtils.exist("com.chervon.common.core.utils.ClassUtils"), 
                    "ClassUtils类本身应该存在");
            assertTrue(ClassUtils.exist("org.junit.jupiter.api.Test"), 
                    "JUnit Test注解应该存在");
        }

        @Test
        @DisplayName("应该正确识别不存在的类")
        void shouldReturnFalseForNonExistentClasses() {
            // Given & When & Then
            assertFalse(ClassUtils.exist("com.nonexistent.Class"), "不存在的类应该返回false");
            assertFalse(ClassUtils.exist("java.lang.NonExistentClass"), "不存在的JDK类应该返回false");
            assertFalse(ClassUtils.exist("com.chervon.NonExistentUtils"), "不存在的工具类应该返回false");
        }

        @Test
        @DisplayName("应该处理null参数")
        void shouldHandleNullClassName() {
            // Given & When & Then
            assertFalse(ClassUtils.exist(null), "null类名应该返回false");
        }

        @Test
        @DisplayName("应该处理空字符串参数")
        void shouldHandleEmptyClassName() {
            // Given & When & Then
            assertFalse(ClassUtils.exist(""), "空字符串类名应该返回false");
            assertFalse(ClassUtils.exist("   "), "空白字符串类名应该返回false");
        }

        @Test
        @DisplayName("应该处理格式错误的类名")
        void shouldHandleMalformedClassName() {
            // Given & When & Then
            assertFalse(ClassUtils.exist("invalid.class.name."), "以点结尾的类名应该返回false");
            assertFalse(ClassUtils.exist(".invalid.class.name"), "以点开头的类名应该返回false");
            assertFalse(ClassUtils.exist("invalid..class..name"), "包含双点的类名应该返回false");
            assertFalse(ClassUtils.exist("123InvalidClassName"), "以数字开头的类名应该返回false");
        }

        @Test
        @DisplayName("应该处理内部类")
        void shouldHandleInnerClasses() {
            // Given & When & Then
            assertTrue(ClassUtils.exist("java.util.Map$Entry"), "Map.Entry内部接口应该存在");
            assertTrue(ClassUtils.exist("java.lang.Thread$State"), "Thread.State内部枚举应该存在");
        }

        @Test
        @DisplayName("应该处理数组类型")
        void shouldHandleArrayTypes() {
            // Given & When & Then
            assertTrue(ClassUtils.exist("[Ljava.lang.String;"), "String数组类型应该存在");
            assertTrue(ClassUtils.exist("[I"), "int数组类型应该存在");
            assertTrue(ClassUtils.exist("[[Ljava.lang.Object;"), "二维Object数组类型应该存在");
        }
    }

    @Nested
    @DisplayName("类加载测试")
    class LoadTests {

        @Test
        @DisplayName("应该成功加载存在的JDK核心类")
        void shouldLoadExistingJdkClasses() {
            // Given & When
            Class<?> stringClass = ClassUtils.load("java.lang.String");
            Class<?> objectClass = ClassUtils.load("java.lang.Object");
            Class<?> listClass = ClassUtils.load("java.util.List");

            // Then
            assertNotNull(stringClass, "String类应该被成功加载");
            assertEquals(String.class, stringClass, "加载的String类应该正确");
            
            assertNotNull(objectClass, "Object类应该被成功加载");
            assertEquals(Object.class, objectClass, "加载的Object类应该正确");
            
            assertNotNull(listClass, "List接口应该被成功加载");
            assertEquals(java.util.List.class, listClass, "加载的List接口应该正确");
        }

        @Test
        @DisplayName("应该成功加载存在的测试类")
        void shouldLoadExistingTestClasses() {
            // Given & When
            Class<?> classUtilsClass = ClassUtils.load("com.chervon.common.core.utils.ClassUtils");

            // Then
            assertNotNull(classUtilsClass, "ClassUtils类应该被成功加载");
            assertEquals(ClassUtils.class, classUtilsClass, "加载的ClassUtils类应该正确");
        }

        @Test
        @DisplayName("加载不存在的类应该返回null")
        void shouldReturnNullForNonExistentClasses() {
            // Given & When & Then
            assertNull(ClassUtils.load("com.nonexistent.Class"), "不存在的类应该返回null");
            assertNull(ClassUtils.load("java.lang.NonExistentClass"), "不存在的JDK类应该返回null");
            assertNull(ClassUtils.load("com.chervon.NonExistentUtils"), "不存在的工具类应该返回null");
        }

        @Test
        @DisplayName("应该处理null参数")
        void shouldHandleNullClassName() {
            // Given & When & Then
            assertNull(ClassUtils.load(null), "null类名应该返回null");
        }

        @Test
        @DisplayName("应该处理空字符串参数")
        void shouldHandleEmptyClassName() {
            // Given & When & Then
            assertNull(ClassUtils.load(""), "空字符串类名应该返回null");
            assertNull(ClassUtils.load("   "), "空白字符串类名应该返回null");
        }

        @Test
        @DisplayName("应该处理格式错误的类名")
        void shouldHandleMalformedClassName() {
            // Given & When & Then
            assertNull(ClassUtils.load("invalid.class.name."), "以点结尾的类名应该返回null");
            assertNull(ClassUtils.load(".invalid.class.name"), "以点开头的类名应该返回null");
            assertNull(ClassUtils.load("invalid..class..name"), "包含双点的类名应该返回null");
            assertNull(ClassUtils.load("123InvalidClassName"), "以数字开头的类名应该返回null");
        }

        @Test
        @DisplayName("应该成功加载内部类")
        void shouldLoadInnerClasses() {
            // Given & When
            Class<?> mapEntryClass = ClassUtils.load("java.util.Map$Entry");
            Class<?> threadStateClass = ClassUtils.load("java.lang.Thread$State");

            // Then
            assertNotNull(mapEntryClass, "Map.Entry内部接口应该被成功加载");
            assertEquals(java.util.Map.Entry.class, mapEntryClass, "加载的Map.Entry应该正确");
            
            assertNotNull(threadStateClass, "Thread.State内部枚举应该被成功加载");
            assertEquals(Thread.State.class, threadStateClass, "加载的Thread.State应该正确");
        }

        @Test
        @DisplayName("应该成功加载数组类型")
        void shouldLoadArrayTypes() {
            // Given & When
            Class<?> stringArrayClass = ClassUtils.load("[Ljava.lang.String;");
            Class<?> intArrayClass = ClassUtils.load("[I");

            // Then
            assertNotNull(stringArrayClass, "String数组类型应该被成功加载");
            assertEquals(String[].class, stringArrayClass, "加载的String数组类型应该正确");
            
            assertNotNull(intArrayClass, "int数组类型应该被成功加载");
            assertEquals(int[].class, intArrayClass, "加载的int数组类型应该正确");
        }

        @Test
        @DisplayName("应该成功加载基本类型包装类")
        void shouldLoadPrimitiveWrapperClasses() {
            // Given & When
            Class<?> integerClass = ClassUtils.load("java.lang.Integer");
            Class<?> booleanClass = ClassUtils.load("java.lang.Boolean");
            Class<?> doubleClass = ClassUtils.load("java.lang.Double");

            // Then
            assertNotNull(integerClass, "Integer类应该被成功加载");
            assertEquals(Integer.class, integerClass, "加载的Integer类应该正确");
            
            assertNotNull(booleanClass, "Boolean类应该被成功加载");
            assertEquals(Boolean.class, booleanClass, "加载的Boolean类应该正确");
            
            assertNotNull(doubleClass, "Double类应该被成功加载");
            assertEquals(Double.class, doubleClass, "加载的Double类应该正确");
        }
    }

    @Nested
    @DisplayName("方法一致性测试")
    class ConsistencyTests {

        @Test
        @DisplayName("exist()和load()方法应该保持一致性")
        void shouldMaintainConsistencyBetweenExistAndLoad() {
            // Given
            String[] existingClasses = {
                "java.lang.String",
                "java.lang.Object",
                "java.util.List",
                "java.util.ArrayList",
                "com.chervon.common.core.utils.ClassUtils"
            };
            
            String[] nonExistentClasses = {
                "com.nonexistent.Class",
                "java.lang.NonExistentClass",
                "invalid.class.name."
            };

            // When & Then - 存在的类
            for (String className : existingClasses) {
                boolean exists = ClassUtils.exist(className);
                Class<?> loadedClass = ClassUtils.load(className);
                
                assertTrue(exists, "exist()应该返回true: " + className);
                assertNotNull(loadedClass, "load()应该返回非null: " + className);
            }

            // When & Then - 不存在的类
            for (String className : nonExistentClasses) {
                boolean exists = ClassUtils.exist(className);
                Class<?> loadedClass = ClassUtils.load(className);
                
                assertFalse(exists, "exist()应该返回false: " + className);
                assertNull(loadedClass, "load()应该返回null: " + className);
            }
        }

        @Test
        @DisplayName("null参数的处理应该保持一致")
        void shouldHandleNullConsistently() {
            // Given & When & Then
            assertFalse(ClassUtils.exist(null), "exist(null)应该返回false");
            assertNull(ClassUtils.load(null), "load(null)应该返回null");
        }

        @Test
        @DisplayName("空字符串参数的处理应该保持一致")
        void shouldHandleEmptyStringConsistently() {
            // Given & When & Then
            assertFalse(ClassUtils.exist(""), "exist(\"\")应该返回false");
            assertNull(ClassUtils.load(""), "load(\"\")应该返回null");
            
            assertFalse(ClassUtils.exist("   "), "exist(\"   \")应该返回false");
            assertNull(ClassUtils.load("   "), "load(\"   \")应该返回null");
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTests {

        @Test
        @DisplayName("应该处理超长类名")
        void shouldHandleLongClassName() {
            // Given - 创建一个超长的类名
            StringBuilder longClassName = new StringBuilder("com.example.");
            for (int i = 0; i < 100; i++) {
                longClassName.append("very.long.package.name.");
            }
            longClassName.append("VeryLongClassName");

            // When & Then
            assertFalse(ClassUtils.exist(longClassName.toString()), "超长类名应该返回false");
            assertNull(ClassUtils.load(longClassName.toString()), "超长类名应该返回null");
        }

        @Test
        @DisplayName("应该处理特殊字符类名")
        void shouldHandleSpecialCharacters() {
            // Given
            String[] specialClassNames = {
                "com.example.Class$With$Dollar",
                "com.example.Class_With_Underscore",
                "com.example.Class123WithNumbers",
                "com.example.ClassName中文", // 包含中文字符
                "com.example.Class-With-Dash", // 包含连字符（无效）
                "com.example.Class With Space" // 包含空格（无效）
            };

            // When & Then
            for (String className : specialClassNames) {
                boolean exists = ClassUtils.exist(className);
                Class<?> loadedClass = ClassUtils.load(className);
                
                // 这些类名都不存在，但方法应该能正常处理而不抛异常
                assertFalse(exists, "特殊字符类名应该返回false: " + className);
                assertNull(loadedClass, "特殊字符类名应该返回null: " + className);
            }
        }

        @Test
        @DisplayName("应该处理Unicode字符类名")
        void shouldHandleUnicodeClassName() {
            // Given
            String unicodeClassName = "com.example.类名测试";

            // When & Then
            assertFalse(ClassUtils.exist(unicodeClassName), "Unicode类名应该返回false");
            assertNull(ClassUtils.load(unicodeClassName), "Unicode类名应该返回null");
        }
    }

    @Nested
    @DisplayName("性能测试")
    class PerformanceTests {

        @Test
        @DisplayName("批量检查类存在性的性能测试")
        void shouldPerformWellForBatchExistChecks() {
            // Given
            String[] classNames = {
                "java.lang.String", "java.lang.Object", "java.util.List",
                "java.util.ArrayList", "java.util.HashMap", "java.lang.Integer",
                "com.nonexistent.Class1", "com.nonexistent.Class2", "com.nonexistent.Class3"
            };

            // When
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < 1000; i++) {
                for (String className : classNames) {
                    ClassUtils.exist(className);
                }
            }
            long endTime = System.currentTimeMillis();

            // Then
            long duration = endTime - startTime;
            assertTrue(duration < 5000, "批量检查应该在5秒内完成，实际耗时: " + duration + "ms");
        }

        @Test
        @DisplayName("批量加载类的性能测试")
        void shouldPerformWellForBatchLoadOperations() {
            // Given
            String[] classNames = {
                "java.lang.String", "java.lang.Object", "java.util.List",
                "java.util.ArrayList", "java.util.HashMap", "java.lang.Integer"
            };

            // When
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < 1000; i++) {
                for (String className : classNames) {
                    ClassUtils.load(className);
                }
            }
            long endTime = System.currentTimeMillis();

            // Then
            long duration = endTime - startTime;
            assertTrue(duration < 5000, "批量加载应该在5秒内完成，实际耗时: " + duration + "ms");
        }
    }
}