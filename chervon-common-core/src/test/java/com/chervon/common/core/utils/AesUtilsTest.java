package com.chervon.common.core.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AES加密工具类单元测试
 * 
 * <AUTHOR>
 */
@DisplayName("AES加密工具类测试")
class AesUtilsTest {

    private static final String TEST_CONTENT = "Hello World!";
    private static final String TEST_PASSWORD = "testPassword123";
    private static final String LONG_CONTENT = "这是一个很长的测试内容，用于测试AES加密解密功能的稳定性和正确性。包含中文字符和特殊符号：!@#$%^&*()_+{}|:<>?[]\\;'\",./ **********";
    private static final String SPECIAL_CHARS = "!@#$%^&*()_+{}|:<>?[]\\;'\",./";
    private static final String CHINESE_CONTENT = "中文测试内容，包含各种中文字符：你好世界！";

    @Nested
    @DisplayName("加密功能测试")
    class EncryptTests {

        @Test
        @DisplayName("应该成功加密普通字符串")
        void shouldEncryptNormalStringSuccessfully() {
            // Given
            String content = TEST_CONTENT;
            String password = TEST_PASSWORD;

            // When
            String encrypted = AesUtils.encrypt(content, password);

            // Then
            assertNotNull(encrypted, "加密结果不应为null");
            assertNotEquals(content, encrypted, "加密后的内容应与原内容不同");
            assertTrue(encrypted.length() > 0, "加密结果长度应大于0");
        }

        @Test
        @DisplayName("应该成功加密中文字符串")
        void shouldEncryptChineseStringSuccessfully() {
            // Given
            String content = CHINESE_CONTENT;
            String password = TEST_PASSWORD;

            // When
            String encrypted = AesUtils.encrypt(content, password);

            // Then
            assertNotNull(encrypted, "加密中文内容结果不应为null");
            assertNotEquals(content, encrypted, "加密后的中文内容应与原内容不同");
        }

        @Test
        @DisplayName("应该成功加密包含特殊字符的字符串")
        void shouldEncryptSpecialCharsSuccessfully() {
            // Given
            String content = SPECIAL_CHARS;
            String password = TEST_PASSWORD;

            // When
            String encrypted = AesUtils.encrypt(content, password);

            // Then
            assertNotNull(encrypted, "加密特殊字符结果不应为null");
            assertNotEquals(content, encrypted, "加密后的特殊字符应与原内容不同");
        }

        @Test
        @DisplayName("应该成功加密长字符串")
        void shouldEncryptLongStringSuccessfully() {
            // Given
            String content = LONG_CONTENT;
            String password = TEST_PASSWORD;

            // When
            String encrypted = AesUtils.encrypt(content, password);

            // Then
            assertNotNull(encrypted, "加密长字符串结果不应为null");
            assertNotEquals(content, encrypted, "加密后的长字符串应与原内容不同");
        }

        @ParameterizedTest
        @DisplayName("应该支持不同长度的密码")
        @ValueSource(strings = {"1", "12", "**********12345", "**********123456", "********************"})
        void shouldSupportDifferentPasswordLengths(String password) {
            // Given
            String content = TEST_CONTENT;

            // When
            String encrypted = AesUtils.encrypt(content, password);

            // Then
            assertNotNull(encrypted, "使用不同长度密码加密结果不应为null");
            assertNotEquals(content, encrypted, "加密后内容应与原内容不同");
        }

        @Test
        @DisplayName("相同内容和密码应该产生相同的加密结果")
        void shouldProduceSameEncryptionForSameInput() {
            // Given
            String content = TEST_CONTENT;
            String password = TEST_PASSWORD;

            // When
            String encrypted1 = AesUtils.encrypt(content, password);
            String encrypted2 = AesUtils.encrypt(content, password);

            // Then
            assertNotNull(encrypted1, "第一次加密结果不应为null");
            assertNotNull(encrypted2, "第二次加密结果不应为null");
            assertEquals(encrypted1, encrypted2, "相同输入应产生相同的加密结果");
        }

        @ParameterizedTest
        @DisplayName("空值或null输入应该返回null")
        @NullAndEmptySource
        void shouldReturnNullForNullOrEmptyContent(String content) {
            // Given
            String password = TEST_PASSWORD;

            // When & Then
            assertDoesNotThrow(() -> {
                String result = AesUtils.encrypt(content, password);
                // 根据实际实现，可能返回null或抛出异常
            });
        }

        @ParameterizedTest
        @DisplayName("null密码应该处理异常情况")
        @NullAndEmptySource
        void shouldHandleNullPassword(String password) {
            // Given
            String content = TEST_CONTENT;

            // When & Then
            assertDoesNotThrow(() -> {
                String result = AesUtils.encrypt(content, password);
                // 根据实际实现，可能返回null或抛出异常
            });
        }
    }

    @Nested
    @DisplayName("解密功能测试")
    class DecryptTests {

        @Test
        @DisplayName("应该成功解密普通字符串")
        void shouldDecryptNormalStringSuccessfully() {
            // Given
            String originalContent = TEST_CONTENT;
            String password = TEST_PASSWORD;
            String encrypted = AesUtils.encrypt(originalContent, password);

            // When
            String decrypted = AesUtils.decrypt(encrypted, password);

            // Then
            assertNotNull(decrypted, "解密结果不应为null");
            assertEquals(originalContent, decrypted, "解密后应恢复原始内容");
        }

        @Test
        @DisplayName("应该成功解密中文字符串")
        void shouldDecryptChineseStringSuccessfully() {
            // Given
            String originalContent = CHINESE_CONTENT;
            String password = TEST_PASSWORD;
            String encrypted = AesUtils.encrypt(originalContent, password);

            // When
            String decrypted = AesUtils.decrypt(encrypted, password);

            // Then
            assertNotNull(decrypted, "解密中文内容结果不应为null");
            assertEquals(originalContent, decrypted, "解密后应恢复原始中文内容");
        }

        @Test
        @DisplayName("应该成功解密特殊字符")
        void shouldDecryptSpecialCharsSuccessfully() {
            // Given
            String originalContent = SPECIAL_CHARS;
            String password = TEST_PASSWORD;
            String encrypted = AesUtils.encrypt(originalContent, password);

            // When
            String decrypted = AesUtils.decrypt(encrypted, password);

            // Then
            assertNotNull(decrypted, "解密特殊字符结果不应为null");
            assertEquals(originalContent, decrypted, "解密后应恢复原始特殊字符");
        }

        @Test
        @DisplayName("应该成功解密长字符串")
        void shouldDecryptLongStringSuccessfully() {
            // Given
            String originalContent = LONG_CONTENT;
            String password = TEST_PASSWORD;
            String encrypted = AesUtils.encrypt(originalContent, password);

            // When
            String decrypted = AesUtils.decrypt(encrypted, password);

            // Then
            assertNotNull(decrypted, "解密长字符串结果不应为null");
            assertEquals(originalContent, decrypted, "解密后应恢复原始长字符串内容");
        }

        @Test
        @DisplayName("错误的密码应该解密失败")
        void shouldFailWithWrongPassword() {
            // Given
            String originalContent = TEST_CONTENT;
            String correctPassword = TEST_PASSWORD;
            String wrongPassword = "wrongPassword";
            String encrypted = AesUtils.encrypt(originalContent, correctPassword);

            // When
            String decrypted = AesUtils.decrypt(encrypted, wrongPassword);

            // Then
            // 根据实际实现，错误密码可能返回null或抛出异常
            assertNotEquals(originalContent, decrypted, "错误密码不应解密出正确内容");
        }

        @Test
        @DisplayName("无效的加密内容应该解密失败")
        void shouldFailWithInvalidEncryptedContent() {
            // Given
            String invalidEncrypted = "invalidBase64Content";
            String password = TEST_PASSWORD;

            // When
            String decrypted = AesUtils.decrypt(invalidEncrypted, password);

            // Then
            assertNull(decrypted, "无效的加密内容应该返回null");
        }

        @ParameterizedTest
        @DisplayName("null或空值输入应该返回null")
        @NullAndEmptySource
        void shouldReturnNullForNullOrEmptyEncryptedContent(String encryptedContent) {
            // Given
            String password = TEST_PASSWORD;

            // When & Then
            assertDoesNotThrow(() -> {
                String result = AesUtils.decrypt(encryptedContent, password);
                // 根据实际实现，可能返回null或抛出异常
            });
        }
    }

    @Nested
    @DisplayName("加密解密完整流程测试")
    class EncryptDecryptFlowTests {

        @Test
        @DisplayName("完整的加密解密流程应该保持数据一致性")
        void shouldMaintainDataConsistencyInFullFlow() {
            // Given
            String[] testContents = {
                TEST_CONTENT,
                CHINESE_CONTENT,
                SPECIAL_CHARS,
                LONG_CONTENT,
                "",
                "1",
                "12345",
                "测试"
            };
            String password = TEST_PASSWORD;

            for (String originalContent : testContents) {
                if (originalContent != null && !originalContent.isEmpty()) {
                    // When
                    String encrypted = AesUtils.encrypt(originalContent, password);
                    String decrypted = AesUtils.decrypt(encrypted, password);

                    // Then
                    assertNotNull(encrypted, "加密结果不应为null: " + originalContent);
                    assertNotNull(decrypted, "解密结果不应为null: " + originalContent);
                    assertEquals(originalContent, decrypted, 
                        "完整流程后内容应保持一致: " + originalContent);
                }
            }
        }

        @Test
        @DisplayName("多次加密解密应该保持一致性")
        void shouldMaintainConsistencyInMultipleRounds() {
            // Given
            String originalContent = TEST_CONTENT;
            String password = TEST_PASSWORD;

            // When & Then
            String currentContent = originalContent;
            for (int i = 0; i < 5; i++) {
                String encrypted = AesUtils.encrypt(currentContent, password);
                String decrypted = AesUtils.decrypt(encrypted, password);
                
                assertNotNull(encrypted, "第" + (i + 1) + "轮加密不应为null");
                assertNotNull(decrypted, "第" + (i + 1) + "轮解密不应为null");
                assertEquals(currentContent, decrypted, "第" + (i + 1) + "轮应保持内容一致");
                
                currentContent = decrypted;
            }
            
            assertEquals(originalContent, currentContent, "多轮加密解密后应保持原始内容");
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTests {

        @Test
        @DisplayName("空字符串加密解密测试")
        void shouldHandleEmptyString() {
            // Given
            String emptyContent = "";
            String password = TEST_PASSWORD;

            // When & Then
            assertDoesNotThrow(() -> {
                String encrypted = AesUtils.encrypt(emptyContent, password);
                if (encrypted != null) {
                    String decrypted = AesUtils.decrypt(encrypted, password);
                    assertEquals(emptyContent, decrypted, "空字符串应该正确处理");
                }
            });
        }

        @Test
        @DisplayName("单字符加密解密测试")
        void shouldHandleSingleCharacter() {
            // Given
            String singleChar = "A";
            String password = TEST_PASSWORD;

            // When
            String encrypted = AesUtils.encrypt(singleChar, password);
            String decrypted = AesUtils.decrypt(encrypted, password);

            // Then
            assertNotNull(encrypted, "单字符加密不应为null");
            assertNotNull(decrypted, "单字符解密不应为null");
            assertEquals(singleChar, decrypted, "单字符应正确加密解密");
        }

        @Test
        @DisplayName("超长内容加密解密测试")
        void shouldHandleVeryLongContent() {
            // Given
            StringBuilder longContentBuilder = new StringBuilder();
            for (int i = 0; i < 1000; i++) {
                longContentBuilder.append("这是第").append(i).append("行测试内容，包含中英文和数字123。");
            }
            String veryLongContent = longContentBuilder.toString();
            String password = TEST_PASSWORD;

            // When
            String encrypted = AesUtils.encrypt(veryLongContent, password);
            String decrypted = AesUtils.decrypt(encrypted, password);

            // Then
            assertNotNull(encrypted, "超长内容加密不应为null");
            assertNotNull(decrypted, "超长内容解密不应为null");
            assertEquals(veryLongContent, decrypted, "超长内容应正确加密解密");
        }
    }

    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTests {

        @Test
        @DisplayName("加密过程中的异常应该被正确处理")
        void shouldHandleEncryptionExceptions() {
            // 这里测试各种可能导致异常的情况
            assertDoesNotThrow(() -> {
                AesUtils.encrypt(null, TEST_PASSWORD);
            }, "null内容不应抛出未捕获异常");

            assertDoesNotThrow(() -> {
                AesUtils.encrypt(TEST_CONTENT, null);
            }, "null密码不应抛出未捕获异常");
        }

        @Test
        @DisplayName("解密过程中的异常应该被正确处理")
        void shouldHandleDecryptionExceptions() {
            assertDoesNotThrow(() -> {
                AesUtils.decrypt(null, TEST_PASSWORD);
            }, "null加密内容不应抛出未捕获异常");

            assertDoesNotThrow(() -> {
                AesUtils.decrypt("invalidBase64", TEST_PASSWORD);
            }, "无效Base64不应抛出未捕获异常");

            assertDoesNotThrow(() -> {
                AesUtils.decrypt(TEST_CONTENT, null);
            }, "null密码不应抛出未捕获异常");
        }
    }
}