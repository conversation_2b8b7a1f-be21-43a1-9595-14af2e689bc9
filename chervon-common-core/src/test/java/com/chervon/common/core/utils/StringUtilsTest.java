package com.chervon.common.core.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Nested;
import static org.junit.jupiter.api.Assertions.*;

/**
 * StringUtils工具类单元测试
 * 
 * <AUTHOR>
 * @since 1.1.0
 */
@DisplayName("StringUtils工具类测试")
class StringUtilsTest {

    @Nested
    @DisplayName("isEmpty方法测试")
    class IsEmptyTest {
        
        @Test
        @DisplayName("当输入为null时应返回true")
        void shouldReturnTrueWhenInputIsNull() {
            // Given
            String input = null;
            
            // When
            boolean result = StringUtils.isEmpty(input);
            
            // Then
            assertTrue(result, "null值应该被认为是empty");
        }
        
        @Test
        @DisplayName("当输入为空字符串时应返回true")
        void shouldReturnTrueWhenInputIsEmptyString() {
            // Given
            String input = "";
            
            // When
            boolean result = StringUtils.isEmpty(input);
            
            // Then
            assertTrue(result, "空字符串应该被认为是empty");
        }
        
        @Test
        @DisplayName("当输入有内容时应返回false")
        void shouldReturnFalseWhenInputHasContent() {
            // Given
            String input = "hello";
            
            // When
            boolean result = StringUtils.isEmpty(input);
            
            // Then
            assertFalse(result, "有内容的字符串不应该被认为是empty");
        }
        
        @Test
        @DisplayName("当输入只有空格时应返回false")
        void shouldReturnFalseWhenInputIsOnlySpaces() {
            // Given
            String input = "   ";
            
            // When
            boolean result = StringUtils.isEmpty(input);
            
            // Then
            assertFalse(result, "只包含空格的字符串不应该被认为是empty");
        }
    }

    @Nested
    @DisplayName("isNotEmpty方法测试")
    class IsNotEmptyTest {
        
        @Test
        @DisplayName("当输入为null时应返回false")
        void shouldReturnFalseWhenInputIsNull() {
            // Given
            String input = null;
            
            // When
            boolean result = StringUtils.isNotEmpty(input);
            
            // Then
            assertFalse(result, "null值不应该被认为是not empty");
        }
        
        @Test
        @DisplayName("当输入为空字符串时应返回false")
        void shouldReturnFalseWhenInputIsEmptyString() {
            // Given
            String input = "";
            
            // When
            boolean result = StringUtils.isNotEmpty(input);
            
            // Then
            assertFalse(result, "空字符串不应该被认为是not empty");
        }
        
        @Test
        @DisplayName("当输入有内容时应返回true")
        void shouldReturnTrueWhenInputHasContent() {
            // Given
            String input = "hello";
            
            // When
            boolean result = StringUtils.isNotEmpty(input);
            
            // Then
            assertTrue(result, "有内容的字符串应该被认为是not empty");
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTest {
        
        @Test
        @DisplayName("测试长字符串")
        void shouldHandleLongString() {
            // Given
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < 10000; i++) {
                sb.append("a");
            }
            String longString = sb.toString();
            
            // When & Then
            assertFalse(StringUtils.isEmpty(longString));
            assertTrue(StringUtils.isNotEmpty(longString));
        }
        
        @Test
        @DisplayName("测试包含特殊字符的字符串")
        void shouldHandleSpecialCharacters() {
            // Given
            String specialString = "hello\n\t\r世界🌍";
            
            // When & Then
            assertFalse(StringUtils.isEmpty(specialString));
            assertTrue(StringUtils.isNotEmpty(specialString));
        }
    }

    @Nested
    @DisplayName("自定义方法测试")
    class CustomMethodsTest {
        
        @Test
        @DisplayName("测试blankToDefault方法")
        void shouldTestBlankToDefault() {
            // Given & When & Then
            assertEquals("default", StringUtils.blankToDefault("", "default"));
            assertEquals("default", StringUtils.blankToDefault(null, "default"));
            assertEquals("default", StringUtils.blankToDefault("   ", "default"));
            assertEquals("test", StringUtils.blankToDefault("test", "default"));
        }
        
        @Test
        @DisplayName("测试字符串格式化方法")
        void shouldTestFormat() {
            // Given & When & Then
            assertEquals("hello world", StringUtils.format("hello {}", "world"));
            assertEquals("hello world test", StringUtils.format("hello {} {}", "world", "test"));
            assertEquals("hello", StringUtils.format("hello", "world"));
        }
        
        @Test
        @DisplayName("测试驼峰转换方法")
        void shouldTestCamelCaseConversion() {
            // Given & When & Then
            assertEquals("helloWorld", StringUtils.toCamelCase("hello_world"));
            assertEquals("HelloWorld", StringUtils.convertToCamelCase("HELLO_WORLD"));
            assertEquals("hello_world", StringUtils.toUnderScoreCase("helloWorld"));
            
            // 测试underscoreToCamel和camelToUnderscore
            assertEquals("HelloWorld", StringUtils.underscoreToCamel("hello_world"));
            assertEquals("hello_world", StringUtils.camelToUnderscore("HelloWorld"));
        }
        
        @Test
        @DisplayName("测试字符串转集合方法")
        void shouldTestStringToCollection() {
            // Given
            String input = "a,b,c,d";
            
            // When
            java.util.List<String> list = StringUtils.str2List(input, ",", true, true);
            java.util.Set<String> set = StringUtils.str2Set(input, ",");
            
            // Then
            assertEquals(4, list.size());
            assertEquals(4, set.size());
            assertTrue(list.contains("a"));
            assertTrue(set.contains("b"));
        }
        
        @Test
        @DisplayName("测试空字符串转集合")
        void shouldTestEmptyStringToCollection() {
            // Given
            String input = "";
            
            // When
            java.util.List<String> list = StringUtils.str2List(input, ",", true, true);
            java.util.Set<String> set = StringUtils.str2Set(input, ",");
            
            // Then
            assertTrue(list.isEmpty());
            assertTrue(set.isEmpty());
        }
        
        @Test
        @DisplayName("测试URL验证方法")
        void shouldTestIsHttp() {
            // Given & When & Then
            assertTrue(StringUtils.isHttp("http://www.example.com"));
            assertTrue(StringUtils.isHttp("https://www.example.com"));
            assertTrue(StringUtils.isHttp("ftp://www.example.com")); // hutool的Validator.isUrl()认为ftp也是有效URL
            assertFalse(StringUtils.isHttp("not-a-url"));
        }
        
        @Test
        @DisplayName("测试字符串匹配方法")
        void shouldTestStringMatching() {
            // Given & When & Then
            assertTrue(StringUtils.containsAnyIgnoreCase("Hello World", "HELLO", "TEST"));
            assertFalse(StringUtils.containsAnyIgnoreCase("Hello World", "TEST", "XYZ"));
            
            assertTrue(StringUtils.inStringIgnoreCase("test", "TEST", "OTHER"));
            assertFalse(StringUtils.inStringIgnoreCase("test", "OTHER", "ANOTHER"));
        }
        
        @Test
        @DisplayName("测试路径匹配方法")
        void shouldTestPathMatching() {
            // Given & When & Then
            assertTrue(StringUtils.isMatch("/api/*", "/api/users"));
            assertTrue(StringUtils.isMatch("/api/**", "/api/v1/users"));
            assertFalse(StringUtils.isMatch("/api/*", "/api/v1/users"));
            
            // 测试matches方法
            java.util.List<String> patterns = java.util.Arrays.asList("/api/*", "/admin/**");
            assertTrue(StringUtils.matches("/api/users", patterns));
            assertTrue(StringUtils.matches("/admin/system/config", patterns));
            assertFalse(StringUtils.matches("/public/index", patterns));
        }
        
        @Test
        @DisplayName("测试左补齐方法")
        void shouldTestPadLeft() {
            // Given & When & Then
            assertEquals("0001", StringUtils.padl(1, 4));
            assertEquals("0123", StringUtils.padl(123, 4));
            assertEquals("3456", StringUtils.padl(123456, 4)); // 超过长度时保留最后4位
            
            assertEquals("___test", StringUtils.padl("test", 7, '_'));
            assertEquals("est", StringUtils.padl("test", 3, '_')); // 长度不够时截取最后3位
        }
        
        @Test
        @DisplayName("测试自定义trim方法")
        void shouldTestCustomTrim() {
            // Given & When & Then
            assertEquals("hello", StringUtils.trim("xxxhelloxxx", "xxx"));
            assertEquals("test", StringUtils.trim("__test__", "__"));
            assertEquals("middle", StringUtils.trim("abcmiddleabc", "abc"));
        }
        
        @Test
        @DisplayName("测试符号格式化方法")
        void shouldTestSymbolFormat() {
            // Given & When & Then
            assertEquals("test\\\\test", StringUtils.symbolFormat("test\\test"));
            assertEquals("test\\%test", StringUtils.symbolFormat("test%test"));
            assertEquals("test\\_test", StringUtils.symbolFormat("test_test"));
            assertEquals("test\\'test", StringUtils.symbolFormat("test'test"));
            assertEquals("test\\\"test", StringUtils.symbolFormat("test\"test"));
            assertNull(StringUtils.symbolFormat(null));
        }
        
        @Test
        @DisplayName("测试字符串截取方法")
        void shouldTestSubstring() {
            // Given
            String input = "hello world";
            
            // When & Then
            assertEquals("world", StringUtils.substring(input, 6));
            assertEquals("hello", StringUtils.substring(input, 0, 5));
            assertEquals("", StringUtils.substring(input, 5, 5));
        }
        
        @Test
        @DisplayName("测试trim方法")
        void shouldTestTrim() {
            // Given & When & Then
            assertEquals("test", StringUtils.trim("  test  "));
            assertEquals("", StringUtils.trim("   "));
            assertEquals("test", StringUtils.trim("test"));
        }
    }
}
