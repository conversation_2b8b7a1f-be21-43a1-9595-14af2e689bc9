package com.chervon.common.core.prop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 * @date 2023/3/31 16:13
 */
@Data
@ConfigurationProperties(prefix = "sf")
@RefreshScope
public class SfProperties {
    /**
     * 是否开启SF同步功能
     */
    private boolean enable;
    /**
     * 查询产品信息时对应的数据中心
     * United States, Australia, United Kingdom
     */
    private String countryOfOriginC;
}
