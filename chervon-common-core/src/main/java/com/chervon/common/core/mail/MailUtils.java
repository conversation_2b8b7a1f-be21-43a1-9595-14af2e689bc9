package com.chervon.common.core.mail;

import cn.hutool.core.io.resource.ClassPathResource;
import com.chervon.common.core.utils.file.FileUtils;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.Version;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.CollectionUtils;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.Message.RecipientType;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;

/**
 * 邮件发送工具类
 *
 * <AUTHOR>
 * @date 20220614
 */
@Slf4j
public class MailUtils {

    private static final String SSL_FACTORY = "javax.net.ssl.SSLSocketFactory";

    public static void main(String[] args) throws MessagingException {
        // 配置发送邮件的环境属性
        MailServerConfig config = new MailServerConfig();
        MailBody mail = new MailBody();
        config.setAuth(true);

        config.setSmtphost("mail2.cn.chervongroup.com");
        config.setUser("iot.dev.noreply");
        config.setPassword("Chervon,2025");
        config.setDebug(true);
        config.setPort("25");
        config.setSslEnable(false);


        mail.setSubject("你好");
        mail.setContent("rt");
        mail.setTo("<EMAIL>");
        mail.setFrom("<EMAIL>");
        // 发送文本邮件
        //sendTextMessage(config, mail);
        // 发送附件邮件
        sendImgAttachmentMessage(config, mail);

//        Map<String, Object> map = new HashMap<>();
//        map.put("userName", "xiaocx");
//        map.put("code", "123456");
//        String conetnt = getTemplateMailText("example.html", map);
//        mail.setContent(conetnt);
        //sendTextMessage(config, mail);
    }

    /**
     * 发送一般的文本邮件
     *
     * @param config
     * @param mail
     * @throws javax.mail.MessagingException
     */
    public static void sendTextMessage(MailServerConfig config, MailBody mail) throws MessagingException {
        final Properties props = new Properties();
        // 表示SMTP发送邮件，需要进行身份验证
        // 连接协议
        Authenticator authenticator = makeAuthenticator(config, props);
        // 使用环境属性和授权信息，创建邮件会话
        Session mailSession = Session.getInstance(props, authenticator);
        // 创建邮件消息
        MimeMessage message = buildMessage(mail, mailSession);
        // 发送邮件
        Transport.send(message);
    }

    private static Authenticator makeAuthenticator(MailServerConfig config, Properties props) {
        if(!Objects.isNull(config.getStarttlsEnable())&&config.getStarttlsEnable()){
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
        }
        // 连接协议
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", String.valueOf(config.isAuth()));
        props.put("mail.smtp.host", config.getSmtphost());
        props.put("mail.user", config.getUser());
        props.put("mail.password", config.getPassword());
        props.put("mail.smtp.port", config.getPort());
        props.put("mail.debug", config.isDebug());
        //使用JSSE的SSL socketfactory来取代默认的socketfactory
        props.put("mail.smtp.socketFactory.class", SSL_FACTORY);
        props.put("mail.smtp.ssl.enable", config.isSslEnable());
        //修改
        props.put("mail.smtp.timeout", 10000);
        props.put("mail.smtp.connectiontimeout", 10000);
        props.put("mail.smtp.writetimeout", 10000);
        Authenticator authenticator = new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                // 用户名、密码
                String userName = props.getProperty("mail.user");
                String password = props.getProperty("mail.password");
                return new PasswordAuthentication(userName, password);
            }
        };
        return authenticator;
    }

    /**
     * 批量发送邮件
     * @param config 邮件配置
     * @param sendUsesMailBody 发送者
     * @throws MessagingException
     */
    public static void sendTextMessageToUsers(MailServerConfig config, SendUsesMailBody sendUsesMailBody)
            throws MessagingException {
        final Properties props = new Properties();
        Authenticator authenticator = makeAuthenticator(config, props);
        // 使用环境属性和授权信息，创建邮件会话
        Session mailSession = Session.getInstance(props, authenticator);
        // 创建邮件消息
        MimeMessage message = buildMessages(sendUsesMailBody.getFrom(), sendUsesMailBody.getSubject(),
                sendUsesMailBody.getContent(), mailSession, sendUsesMailBody.getRecipients());
        // 发送邮件
        Transport.send(message);
    }

    private static MimeMessage buildMessages(String from, String subject, String content, Session mailSession,
                                             List<String> recipients)
            throws javax.mail.MessagingException {
        MimeMessage message = new MimeMessage(mailSession);
        // 设置发件人
        InternetAddress form = new InternetAddress(from);
        message.setFrom(form);

        // 设置收件人
        if (!CollectionUtils.isEmpty(recipients)) {
            InternetAddress[] addresses =new InternetAddress[recipients.size()];
            int index = 0;
            for (String recipient : recipients) {
                InternetAddress to = new InternetAddress(recipient);
                addresses[index ++] = to;
            }
            message.setRecipients(RecipientType.TO, addresses);
        }

        // 设置邮件标题
        message.setSubject(subject);

        // 设置邮件的内容体
        message.setContent(content, "text/html;charset=UTF-8");
        return message;
    }
    /**
     * 发送模板邮件
     *
     * @param config       邮件配置
     * @param mail         邮件主题等信息
     * @param map          模板参数
     * @param templateName 模板名称
     * @throws MessagingException
     */
    public static void sendTemplateMessage(MailServerConfig config, MailBody mail, HashMap<String, Object> map,
                                           String templateLocation, String templateName) throws MessagingException {
        String content = getTemplateMailText(templateLocation, templateName, map);
        mail.setContent(content);
        //sendTextMessage(config, mail);
        sendImgAttachmentMessage(config, mail);
    }

    /**
     * 发送模板邮件
     *
     * @param config  邮箱配置
     * @param mail    邮件主题等信息
     * @param map     模板参数
     * @param content 模板内容
     */
    public static void sendTemplateContentMessage(MailServerConfig config, MailBody mail, HashMap<String, Object> map,
                                                  String content, String templateName) throws MessagingException {
        String path = "./" + templateName;
        try (FileWriter writer = new FileWriter(path)) {
            writer.write("");
            writer.write(content);
            writer.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
        sendTemplateMessage(config, mail, map, "./", templateName);
    }
    /**
     * 发送模板带图片邮件
     *
     * @param config  邮箱配置
     * @param mail    邮件主题等信息
     * @param map     模板参数
     * @param content 模板内容
     */
    public static void sendTemplateImgContentMessage(MailServerConfig config, MailBody mail, HashMap<String, Object> map,
                                                  String content, String templateName) throws MessagingException {
        String path = "./" + templateName;
        try (FileWriter writer = new FileWriter(path)) {
            writer.write("");
            writer.write(content);
            writer.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
        sendTemplateMessage(config, mail, map, "./", templateName);
    }
    /**
     * 发送带附件的邮件
     *
     * @param attachment
     * @return MimeMessage
     * @throws MessagingException
     * @throws IOException
     */
    public static void sendAttachmentMessage(MailServerConfig config, MailBody mail,
                                             File attachment)
            throws javax.mail.MessagingException {
        final Properties props = new Properties();
        // 表示SMTP发送邮件，需要进行身份验证
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", String.valueOf(config.isAuth()));
        props.put("mail.smtp.host", config.getSmtphost());
        props.put("mail.user", config.getUser());
        props.put("mail.password", config.getPassword());
        props.put("mail.smtp.port", config.getPort());
        props.put("mail.debug", config.isDebug());
        //使用JSSE的SSL socketfactory来取代默认的socketfactory
        props.put("mail.smtp.socketFactory.class", SSL_FACTORY);
        props.put("mail.smtp.ssl.enable", config.isSslEnable());
        Authenticator authenticator = new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                // 用户名、密码
                String userName = props.getProperty("mail.user");
                String password = props.getProperty("mail.password");
                return new PasswordAuthentication(userName, password);
            }
        };
        // 使用环境属性和授权信息，创建邮件会话
        Session mailSession = Session.getInstance(props, authenticator);

        MimeMessage email = buildAttachment(mail, mailSession, attachment);
        // 发送邮件
        Transport.send(email);
    }
    /**
     * 发送带附件的邮件
     *
     * @param
     * @return MimeMessage
     * @throws MessagingException
     * @throws IOException
     */
    public static void sendImgAttachmentMessage(MailServerConfig config, MailBody mail)
        throws javax.mail.MessagingException {
        final Properties props = new Properties();
        if(!Objects.isNull(config.getStarttlsEnable())&&config.getStarttlsEnable()){
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
        }
        // 表示SMTP发送邮件，需要进行身份验证
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", String.valueOf(config.isAuth()));
        props.put("mail.smtp.host", config.getSmtphost());
        props.put("mail.user", config.getUser());
        props.put("mail.password", config.getPassword());
        props.put("mail.smtp.port", config.getPort());
        props.put("mail.debug", config.isDebug());
        //使用JSSE的SSL socketfactory来取代默认的socketfactory
        props.put("mail.smtp.socketFactory.class", SSL_FACTORY);
        props.put("mail.smtp.ssl.enable", config.isSslEnable());
        //修改
        props.put("mail.smtp.timeout", 10000);
        props.put("mail.smtp.connectiontimeout", 10000);
        props.put("mail.smtp.writetimeout", 10000);
        Authenticator authenticator = new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                // 用户名、密码
                String userName = props.getProperty("mail.user");
                String password = props.getProperty("mail.password");
                return new PasswordAuthentication(userName, password);
            }
        };
        // 使用环境属性和授权信息，创建邮件会话
        Session mailSession = Session.getInstance(props, authenticator);

        MimeMessage email = buildImgAttachment(mail, mailSession);
        log.error("email"+email.getSubject());
        // 发送邮件
        Transport.send(email);
    }

    private static MimeMessage buildMessage(@NotNull MailBody mail, Session mailSession)
            throws javax.mail.MessagingException {
        MimeMessage message = new MimeMessage(mailSession);
        // 设置发件人
        InternetAddress form = new InternetAddress(mail.getFrom());
        message.setFrom(form);

        // 设置收件人
        InternetAddress to = new InternetAddress(mail.getTo());
        message.setRecipient(RecipientType.TO, to);

        // 设置抄送
        if (StringUtils.isNotBlank(mail.getCc())) {
            InternetAddress cc = new InternetAddress(mail.getCc());
            message.setRecipient(RecipientType.CC, cc);
        }
        // 设置密送，其他的收件人不能看到密送的邮件地址
        if (StringUtils.isNotBlank(mail.getBcc())) {
            InternetAddress bcc = new InternetAddress(mail.getBcc());
            message.setRecipient(RecipientType.CC, bcc);
        }
        // 设置邮件标题
        message.setSubject(mail.getSubject());

        // 设置邮件的内容体
        message.setContent(mail.getContent(), "text/html;charset=UTF-8");
        return message;
    }

    /**
     * 创建携带附件的邮件信息
     *
     * @param mail
     * @param mailSession
     * @return
     * @throws javax.mail.MessagingException
     */
    private static MimeMessage buildAttachment(MailBody mail, Session mailSession, File attachment)
            throws javax.mail.MessagingException {
        MimeMessage email = new MimeMessage(mailSession);

        email.setFrom(new InternetAddress(mail.getFrom()));
        email.addRecipient(javax.mail.Message.RecipientType.TO,
                new InternetAddress(mail.getTo()));
        email.setSubject(mail.getSubject());
        email.setSentDate(new Date());

        MimeBodyPart mimeBodyPart = new MimeBodyPart();
        mimeBodyPart.setContent(mail.getContent(), "text/html");

        Multipart multipart = new MimeMultipart();
        multipart.addBodyPart(mimeBodyPart);

        mimeBodyPart = new MimeBodyPart();
        DataSource source = new FileDataSource(attachment);

        mimeBodyPart.setDataHandler(new DataHandler(source));
        mimeBodyPart.setFileName(attachment.getName());

        multipart.addBodyPart(mimeBodyPart);
        email.setContent(multipart);
        return email;
    }
    private static MimeMessage buildImgAttachment(MailBody mail, Session mailSession)
        throws javax.mail.MessagingException {
        MimeMessage email = new MimeMessage(mailSession);
        email.setFrom(new InternetAddress(mail.getFrom()));
        email.addRecipient(javax.mail.Message.RecipientType.TO,
            new InternetAddress(mail.getTo()));
        log.error("标题"+mail.getSubject());
        email.setSubject(mail.getSubject());
        email.setSentDate(new Date());
        //  显示图片必须为related，如果还需要添加附件必须为multi
        //  邮件内容
        MimeMultipart multipart = new MimeMultipart("related");
        BodyPart messageBodyPart = new MimeBodyPart();
        //必须明确指定字体为UTF-8，避免中文乱码
        messageBodyPart.setContent(mail.getContent(), "text/html;charset=utf-8;");
        multipart.addBodyPart(messageBodyPart);
        //添加图片
        MimeBodyPart imagePart = new MimeBodyPart();
        String path=mail.getImgPath()+"attach.png";
        String fileName=new ClassPathResource("").getPath() + File.separator + "attach.png";
        FileUtils.writeFile(path, fileName);
        DataSource fds = new FileDataSource(fileName);
        imagePart.setDataHandler(new DataHandler(fds));
        //设置ID
        imagePart.setHeader("Content-ID","<attach>");
        imagePart.setHeader("Content-Type", "image/png");
        imagePart.setDisposition(MimeBodyPart.INLINE);
        imagePart.setFileName("attach.png");
        //添加内容
        multipart.addBodyPart(imagePart);
        email.setContent(multipart);
        return email;
    }
    /**
     * 获取模板邮件
     *
     * @param templateLocation 模板所在位置
     * @param fileName         模板文件名称
     * @param map              参数
     * @return
     */
    public static String getTemplateMailText(String templateLocation, String fileName, Map<String, Object> map) {
        String freemarkerContent = "";
        try {
            Configuration configuration = new Configuration(new Version("2.3.0"));
            configuration.setDefaultEncoding("utf-8");
            configuration.setDirectoryForTemplateLoading(new File(templateLocation));
            //以utf-8的编码读取ftl模板文件
            Template template = configuration.getTemplate(fileName, "utf-8");
            //获取freemarker生成的内容
            freemarkerContent = FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("getTemplateMailText --> 获取模板文件内容失败：{}", e.getMessage());
            // todo
            throw new RuntimeException();
        }
        return freemarkerContent;
    }
}
