package com.chervon.common.core.config;

import java.net.InetAddress;
import java.net.UnknownHostException;

import ch.qos.logback.core.PropertyDefinerBase;

/**
 * 获取主机名
 * <AUTHOR>
 * @date 2022-09-28
 */
public class CanonicalHostNamePropertyDefiner extends PropertyDefinerBase {

    @Override
    public String getPropertyValue() {
        InetAddress ia;
        try {
            ia = InetAddress.getLocalHost();
            //获取计算机主机名
            String host = ia.getHostName();
            return host;
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        return null;
    }

}