package com.chervon.common.core.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    public static final String YYYY = "yyyy";

    public static final String YYYY_MM = "yyyy-MM";

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static final String YYYY_MM_REG = "([1-9]\\d{3}-)(([0]{0,1}[1-9])|([1][0-2]))";

    public static final String YYYY_MM_DD_REG = "((\\d{2}(([02468][048])|([13579][26]))[\\-]((((0?[13578])|(1[02]))[\\-]((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-]((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-]((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-]((((0?[13578])|(1[02]))[\\-]((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-]((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-]((0?[1-9])|(1[0-9])|(2[0-8]))))))";

    public static final String YYYY_MM_DD_HH_MM_SS_REG = "((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s((([0-1][0-9])|(2?[0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))";

    private static final String[] PARSE_PATTERNS = {
        "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
        "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
        "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), PARSE_PATTERNS);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 获取时间段内每个月的最后一天日期
     *
     * @param startDate 起始日期
     * @param endDate   结束日期
     * @return 日期范围内每个月的最后一天+结束日期
     */
    public static List<String> listLastDaysOfMonth(Date startDate, Date endDate) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<String> result = new ArrayList<>();
        Calendar startCalendar = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        startCalendar.setTime(startDate);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(endDate);
        System.out.println(startCalendar.getTime());
        // 测试此日期是否在指定日期之后
        Calendar calBegin_1 = (Calendar) startCalendar.clone();
        //把月底提出来
        calBegin_1.add(Calendar.MONTH, 1);
        calBegin_1.set(Calendar.DAY_OF_MONTH, 0);
        boolean endMouth = false;
        while (endDate.after(startCalendar.getTime())) {
            if (calBegin_1.equals(startCalendar) || endMouth) {
                //保存最后一天
                result.add(simpleDateFormat.format(startCalendar.getTime()));
                startCalendar.add(Calendar.MONTH, 1);
                startCalendar.set(Calendar.DAY_OF_MONTH, 1);
                startCalendar.add(Calendar.MONTH, 1);
                // 设置为上个月最后一天
                startCalendar.set(Calendar.DAY_OF_MONTH, 0);
                endMouth = true;
            } else {
                startCalendar.add(Calendar.DAY_OF_MONTH, 1);
            }
        }
        result.add(simpleDateFormat.format(endDate));
        return result;
    }

    /**
     * 获取两个时间段字符串内每个月的最后一天日期
     *
     * @param startDate 起始日期字符串
     * @param endDate   结束日期字符串
     * @return 日期范围内每个月的最后一天+结束日期
     */
    public static List<String> listLastDaysOfMonth(String startDate, String endDate) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        List<String> dates = new ArrayList<>();
        try {
            dates = listLastDaysOfMonth(df.parse(startDate), df.parse(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return dates;
    }

    /**
     * 获取某月第一天日期
     *
     * @param dateStr yyyy-MM-dd日期字符串
     * @return 某年第一天Date对象
     */
    public static Date getMonthFirst(String dateStr) {
        if (!dateStr.matches(YYYY_MM_DD_REG)) {
            return null;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(dateFormat.parse(dateStr));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 获取某月第一天日期
     *
     * @param dateStr yyyy-MM-dd日期字符串
     * @return 某月第一天yyyy-MM-dd字符串
     */
    public static String getMonthFirstStr(String dateStr) {
        Date monthFirstDayDate = getMonthFirst(dateStr);
        if (null == monthFirstDayDate) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(monthFirstDayDate);
    }

    /**
     * 获取yyyy-MM字符串所在月份第一天yyyy-MM-dd日期
     *
     * @param dateStr 月份 yyyy-MM
     * @return 日期yyyy-MM-dd
     */
    public static String getYyyyMmMonthFirstStr(String dateStr) {
        if (!dateStr.matches(YYYY_MM_REG)) {
            return null;
        }
        SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(monthFormat.parse(dateStr));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(calendar.getTime());
    }

    /**
     * 获取yyyy-MM字符串所在月份最后一天yyyy-MM-dd日期
     *
     * @param dateStr 月份 yyyy-MM
     * @return 日期yyyy-MM-dd
     */
    public static String getYyyyMmMonthLastStr(String dateStr) {
        if (!dateStr.matches(YYYY_MM_REG)) {
            return null;
        }
        SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(monthFormat.parse(dateStr));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(calendar.getTime());
    }

    /**
     * 获取某月最后一天日期
     *
     * @param dateStr 日期字符串
     * @param format  支持yyyy-MM或yyyy-MM-dd两种正则
     * @return 某年第一天Date对象
     */
    public static Date getMonthLast(String dateStr, String format) {
        if (!dateStr.matches(YYYY_MM_DD_REG) && !dateStr.matches(YYYY_MM_REG)) {
            return null;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(dateFormat.parse(dateStr));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        return calendar.getTime();
    }

    /**
     * 获取某月最后一天日期
     *
     * @param dateStr 日期字符串
     * @param format  支持yyyy-MM或yyyy-MM-dd两种正则
     * @return 某月第一天yyyy-MM-dd字符串
     */
    public static String getMonthLastStr(String dateStr, String format) {
        Date monthFirstDayDate = getMonthLast(dateStr, format);
        if (null == monthFirstDayDate) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(monthFirstDayDate);
    }

    /**
     * 获取某月第一天日期
     *
     * @param dateStr yyyy-MM-dd日期字符串
     * @return 某月第一天yyyy-MM-dd字符串
     */
    public static String getYearFirstStr(String dateStr) {
        Date yearFirst = getYearFirst(Integer.parseInt(dateStr.substring(0, 4)));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(yearFirst);
    }

    /**
     * 获取某年第一天日期
     *
     * @param year 年份
     * @return 某年第一天Date对象
     */
    public static Date getYearFirst(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        return calendar.getTime();
    }

    /**
     * 获取某年最后一天日期
     *
     * @param year 年份
     * @return 某年最后一天Date对象
     */
    public static Date getYearLast(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        return calendar.getTime();
    }

    /**
     * 获取某一天一年前字符串
     *
     * @param dateStr 日期yyyy-MM-dd
     * @return 这一天一年前的日期
     */
    public static String minusOneYear(String dateStr) {
        if (!dateStr.matches(YYYY_MM_DD_REG)) {
            return null;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(dateFormat.parse(dateStr));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        calendar.add(Calendar.YEAR, -1);
        Date dateMinusOneYear = calendar.getTime();
        return dateFormat.format(dateMinusOneYear);
    }

    /**
     * 日期字符串加减一定天数
     *
     * @param date 日期yyyy-MM-dd
     * @param num  加减天数 加为正整数 减为负整数
     * @return 加减之后的日期字符串
     */
    public static String getDateStrAddDays(String date, int num) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date da = df.parse(date);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(da);
            calendar.add(Calendar.DAY_OF_MONTH, num);
            return df.format(calendar.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取yyyy-MM字符串加减一定月份后的字符串
     *
     * @param date   yyyy-MM日期字符串
     * @param months 加减的月份数,正数为加,负数为减
     * @return yyyy-MM字符串
     */
    public static String getDateStrAddMonths(String date, int months) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM");
        try {
            Date da = df.parse(date);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(da);
            calendar.add(Calendar.MONTH, months);
            return df.format(calendar.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取起始日期与结束日期之间所有的日期列表
     *
     * @param beginStr 开始日期
     * @param endStr   结束日期
     * @return 开始与结束之间的所以日期，包括起止
     */
    public static List<String> getBetweenDateStrList(String beginStr, String endStr) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate begin = LocalDate.parse(beginStr, fmt);
        LocalDate end = LocalDate.parse(endStr, fmt);
        List<String> result = new ArrayList<>();
        long length = end.toEpochDay() - begin.toEpochDay();
        for (long i = length; i >= 0; i--) {
            result.add(end.minusDays(i).format(fmt));
        }
        return result;
    }

    /**
     * 根据起始时间Str获取Date列表
     *
     * @param beginStr 开始时间
     * @param endStr   结束时间
     * @return Date列表, 0时区
     */
    public static List<Date> getBetweenDateList(String beginStr, String endStr) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate begin = LocalDate.parse(beginStr, fmt);
        LocalDate end = LocalDate.parse(endStr, fmt);
        List<Date> result = new ArrayList<>();
        long length = end.toEpochDay() - begin.toEpochDay();
        for (long i = length; i >= 0; i--) {
            LocalDate localDate = end.minusDays(i);
            Instant instant = Timestamp.valueOf(localDate.atTime(LocalTime.MIDNIGHT)).toInstant();
            result.add(Date.from(instant));
        }
        return result;
    }


    /**
     * 获取两个日期之间的所有月份 (年月)
     *
     * @param startTime 起始日期
     * @param endTime   结束日期
     * @return yyyy-MM 月份字符串列表
     */
    public static List<String> getBetweenMonthStrList(String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        // 声明保存日期集合
        List<String> list = new ArrayList<>();
        try {
            // 转化成日期类型
            Date startDate = sdf.parse(startTime);
            Date endDate = sdf.parse(endTime);

            //用Calendar 进行日期比较判断
            Calendar calendar = Calendar.getInstance();
            while (startDate.getTime() <= endDate.getTime()) {
                // 把日期添加到集合
                list.add(sdf.format(startDate));
                // 设置日期
                calendar.setTime(startDate);
                //把日期增加一天
                calendar.add(Calendar.MONTH, 1);
                // 获取增加后的日期
                startDate = calendar.getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return list;
    }


    /**
     * 获取两个日期之间的所有年
     *
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return yyyy 年份字符串列表
     */
    public static List<String> getBetweenYearStrList(String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        // 声明保存日期集合
        List<String> list = new ArrayList<>();
        try {
            // 转化成日期类型
            Date startDate = sdf.parse(startTime);
            Date endDate = sdf.parse(endTime);
            //用Calendar 进行日期比较判断
            Calendar calendar = Calendar.getInstance();
            while (startDate.getTime() <= endDate.getTime()) {
                // 把日期添加到集合
                list.add(sdf.format(startDate));
                // 设置日期
                calendar.setTime(startDate);
                //把年数增加 1
                calendar.add(Calendar.YEAR, 1);
                // 获取增加后的日期
                startDate = calendar.getTime();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    public static void main(String[] args) {
        List<Date> betweenYearStrList = getBetweenDateList("2023-08-01", "2023-08-25");
        System.out.println(betweenYearStrList);
    }
}
