package com.chervon.common.core.thread;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.concurrent.ThreadFactory;

/**
 * <AUTHOR> 2023/6/8
 */
public class ThreadUtils {
    private static final Logger log = LoggerFactory.getLogger(ThreadUtils.class);

    public ThreadUtils() {
    }

    public static void spinWait() {
        spinWait(1L);
    }

    public static void spinWait(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException var3) {
            log.error(var3.getMessage());
            Thread.currentThread().interrupt();
        } catch (Exception var4) {
            log.error(var4.getMessage());
        }
    }

    public static ThreadFactory buildThreadFactory(String prefix) {
        return AuthThreadFactory.getInstance().setNamePrefix(prefix);
    }
}
