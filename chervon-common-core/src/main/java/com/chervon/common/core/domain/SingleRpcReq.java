package com.chervon.common.core.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/14 21:53
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SingleRpcReq<T> extends RpcBase implements Serializable {

    private T data;

    public SingleRpcReq() {
        super();
    }

    public SingleRpcReq(T data) {
        super();
        this.data = data;
    }

    public SingleRpcReq(T data, String lang) {
        super(lang);
        this.data = data;
    }

    public SingleRpcReq(T data, String lang, LoginSysUser login) {
        super(lang, login);
        this.data = data;
    }

    public SingleRpcReq(T data, LoginSysUser login) {
        super(login);
        this.data = data;
    }

}
