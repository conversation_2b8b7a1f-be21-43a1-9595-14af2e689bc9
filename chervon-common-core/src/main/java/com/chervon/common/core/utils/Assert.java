/*
 * Copyright 2002-2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.chervon.common.core.utils;
import com.chervon.common.core.error.IError;
import com.chervon.common.core.exception.ServiceException;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import java.util.Collection;
import java.util.Map;

/**
 * 断言
 * <p>
 * copy by spring.
 * 主要为了I18N将验证异常统一
 */
public abstract class Assert {
    private Assert(){}

    public static void isId(Long id, IError error, String... parameters) {
        isTrue(id != null && id > 0, error, parameters);
    }

    public static void isIdInteger(Integer id, IError error, String... parameters) {
        isTrue(id != null && id > 0, error, parameters);
    }

    public static void isTrue(boolean expression, IError error, String... parameters) {
        if (!expression) {
            throw new ServiceException(error, parameters);
        }
    }

    public static void isNull(@Nullable Object object, IError error, String... parameters) {
        if (object != null) {
            throw new ServiceException(error, parameters);
        }
    }

    public static void notNull(@Nullable Object object, IError error, String... parameters) {
        if (object == null) {
            throw new ServiceException(error, parameters);
        }
    }

    public static void hasLength(@Nullable String text, IError error, String... parameters) {
        if (!StringUtils.hasLength(text)) {
            throw new ServiceException(error, parameters);
        }
    }

    public static void hasText(@Nullable String text, IError error, String... parameters) {
        if (!StringUtils.hasText(text)) {
            throw new ServiceException(error, parameters);
        }
    }

    public static void doesNotContain(@Nullable String textToSearch, String substring, IError error, String... parameters) {
        if (StringUtils.hasLength(textToSearch) && StringUtils.hasLength(substring) &&
                textToSearch.contains(substring)) {
            throw new ServiceException(error, parameters);
        }
    }

    public static void notEmpty(@Nullable Object[] array, IError error, String... parameters) {
        if (ObjectUtils.isEmpty(array)) {
            throw new ServiceException(error, parameters);
        }
    }

    public static void noNullElements(@Nullable Object[] array, IError error, String... parameters) {
        if (array != null) {
            for (Object element : array) {
                if (element == null) {
                    throw new ServiceException(error, parameters);
                }
            }
        }
    }

    public static void notEmpty(@Nullable Collection<?> collection, IError error, String... parameters) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new ServiceException(error, parameters);
        }
    }

    public static void noNullElements(@Nullable Collection<?> collection, IError error, String... parameters) {
        if (collection != null) {
            for (Object element : collection) {
                if (element == null) {
                    throw new ServiceException(error, parameters);
                }
            }
        }
    }

    public static void notEmpty(@Nullable Map<?, ?> map, IError error, String... parameters) {
        if (CollectionUtils.isEmpty(map)) {
            throw new ServiceException(error, parameters);
        }
    }
}
