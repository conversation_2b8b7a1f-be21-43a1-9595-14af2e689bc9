package com.chervon.common.core.utils;

/**
 * 
 * @date 2023/5/15 15:08
 */
public class ClassUtils {
    /**
     * 是否存在类型
     *
     * @param className 类型名称
     * @return 是否存在
     */
    public static boolean exist(String className) {
        boolean hasClass = false;
        try {
            Class.forName(className);
            hasClass = true;
        } catch (Exception e) {
            //ignore
        }
        return hasClass;
    }

    /**
     * 加载类型
     *
     * @param className 类型名
     * @return 类型
     */
    public static Class<?> load(String className) {
        try {
            return Class.forName(className);
        } catch (Exception e) {
            //ignore
            return null;
        }
    }
}
