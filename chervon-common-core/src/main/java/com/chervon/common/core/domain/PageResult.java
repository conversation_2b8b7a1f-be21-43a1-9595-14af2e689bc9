package com.chervon.common.core.domain;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @description: 分页结果
 * @create: 2019-09-25 09:24
 **/
@Accessors(chain = true)
@Data
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结果集
     */
    private List<T> list = new ArrayList<>();

    /**
     * 当前页
     */
    private long pageNum;

    /**
     * 每页的数量
     */
    private long pageSize;

    /**
     * 总页数
     */
    private long pages;

    /**
     * 总记录数
     */
    private long total;

    public PageResult(long pageNum, long pageSize) {
        this(pageNum, pageSize, 0L);
    }

    public PageResult(long pageNum, long pageSize, long total) {
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.pages = getPages();
    }

    public PageResult() {
    }

    public long getPages() {
        if (this.getPageSize() == 0L) {
            return 0L;
        } else {
            long pages = this.getTotal() / this.getPageSize();
            if (this.getTotal() % this.getPageSize() != 0L) {
                ++pages;
            }

            return pages;
        }
    }

    /**
     * 创建实例
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param total 总条数
     * @param items 页数据
     * @param <T> 数据类型
     * @return 分页实例
     */
    public static <T> PageResult<T> of(long pageNo, long pageSize, long total, List<T> items) {
        if (CollectionUtils.isEmpty(items)) {
            items = new ArrayList<>(0);
        }
        return new PageResult<T>()
                .setPageNum(pageNo)
                .setPageSize(pageSize)
                .setTotal(total)
                .setList(items);
    }



}
