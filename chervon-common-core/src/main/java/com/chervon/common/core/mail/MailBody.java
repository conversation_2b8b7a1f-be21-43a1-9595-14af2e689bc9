package com.chervon.common.core.mail;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022-06-16
 * 邮件发送者等信息配置
 */
@Data
public class MailBody {

    private String from;

    private String cc;

    private String to;

    private String bcc;

    private String subject;

    private String content;

    private String imgPath;
    public MailBody(String from, String to, String subject, String content) {
        this.from = from;
        this.to = to;
        this.subject = subject;
        this.content = content;
    }

    public MailBody(String from, String to, String subject) {
        this.from = from;
        this.to = to;
        this.subject = subject;
    }

    public MailBody() {

    }
}
