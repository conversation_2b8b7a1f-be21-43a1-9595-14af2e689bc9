package com.chervon.common.core.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2022/8/8 15:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseRemoteReqDto<T> implements Serializable {

    private String language;

    private T req;

    public BaseRemoteReqDto(Locale locale, T req) {
        this.language = locale.getLanguage();
        this.req = req;
    }
}
