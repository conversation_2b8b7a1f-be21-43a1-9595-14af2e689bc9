package com.chervon.common.core.domain;

import com.chervon.common.core.constant.Constants;
import com.chervon.common.core.error.DefaultError;
import com.chervon.common.core.error.IError;
import com.chervon.common.core.exception.ErrorCodeI;
import com.chervon.common.core.utils.SnowFlake;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.text.MessageFormat;
import java.util.Objects;

/**
 * 响应信息主体
 */
@Data
@NoArgsConstructor
public class R<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 成功
     */
    public static final int SUCCESS = Constants.SUCCESS;
    /**
     * 失败
     */
    public static final int FAIL = Constants.FAIL;

    private Long requestId;

    private boolean status;

    private String message;

    private String exception;

    private String responseCode;

    private Long timestamp;

    private T entry;

    public static <T> R<T> ok() {
        return restResult(null, SUCCESS, "success", true);
    }

    public static <T> R<T> ok(T data) {
        return restResult(data, SUCCESS, "success", true);
    }

    public static <T> R<T> ok(String msg, T data) {
        return restResult(data, SUCCESS, msg, true);
    }

    public static <T> R<T> fail(String msg) {
        return restResult(null, FAIL, msg, false);
    }

    public static <T> R<T> fail(T data) {
        return restResult(data, FAIL, "操作失败", false);
    }
    /**
     * 失败
     *
     * @param error 失败原因
     * @param <T>   目标类型
     * @return result
     */
    public static <T> R<T> failed(ErrorCodeI error) {
        return failed(error, null);
    }
    public static <T> R<T> failed(ErrorCodeI error, String... parameters) {
        R<T> dataResult = new R<T>();
        dataResult.setRequestId(SnowFlake.nextId());
        dataResult.setStatus(false);
        dataResult.setResponseCode(error.getCode().toString());
        dataResult.setMessage(resolvePlaceholder(error.getDefaultMessage(), parameters));
        return dataResult;
    }

    public static <T> R<T> fail(String msg, T data) {
        return restResult(data, FAIL, msg, false);
    }

    public static <T> R<T> fail(int code, String msg) {
        return restResult(null, code, msg, false);
    }

    public static <T> R<T> fail(String errorCode, String msg) {
        return restResult(null, errorCode, msg, false);
    }

    public static <T> R<T> fail(T data, String errorCode, String msg, String exception) {
        R<T> r = new R<>();
        r.setRequestId(SnowFlake.nextId());
        r.setResponseCode(errorCode);
        r.setEntry(data);
        r.setMessage(msg);
        r.setException(exception);
        r.setStatus(false);
        return r;
    }

    public static <T> R<T> fail(String errorCode, String msg, String exception) {
        R<T> r = new R<>();
        r.setRequestId(SnowFlake.nextId());
        r.setResponseCode(errorCode);
        r.setEntry(null);
        r.setMessage(msg);
        r.setException(exception);
        r.setStatus(false);
        return r;
    }

    public static <T> R<T> fail(T data, int code, String msg, String exception) {
        R<T> r = new R<>();
        r.setRequestId(SnowFlake.nextId());
        r.setResponseCode(String.valueOf(code));
        r.setEntry(data);
        r.setMessage(msg);
        r.setException(exception);
        r.setStatus(false);
        return r;
    }

    public static <T> R<T> fail(int code, String msg, String exception) {
        R<T> r = new R<>();
        r.setRequestId(SnowFlake.nextId());
        r.setResponseCode(String.valueOf(code));
        r.setEntry(null);
        r.setMessage(msg);
        r.setException(exception);
        r.setStatus(false);
        return r;
    }

    private static <T> R<T> restResult(T data, int code, String msg, Boolean isStatus) {
        R<T> r = new R<>();
        r.setRequestId(SnowFlake.nextId());
        r.setResponseCode(String.valueOf(code));
        r.setEntry(data);
        r.setMessage(msg);
        r.setStatus(isStatus);
        return r;
    }

    private static <T> R<T> restResult(T data, String errorCode, String msg, Boolean isStatus) {
        R<T> r = new R<>();
        r.setRequestId(SnowFlake.nextId());
        r.setResponseCode(errorCode);
        r.setEntry(data);
        r.setMessage(msg);
        r.setStatus(isStatus);
        return r;
    }

    /**
     * 失败
     *
     * @param error 失败原因
     * @param <T>   目标类型
     * @return result
     */
    public static <T> R<T> failed(IError error) {
        return failed(error, null);
    }


    public static <T> R<T> failed(Object data, IError error, String... parameters) {
        R<T> dataResult = new R<T>();
        dataResult.setRequestId(SnowFlake.nextId());
        dataResult.setStatus(false);
        dataResult.setResponseCode(error.getCode());
        dataResult.setMessage(resolvePlaceholder(error.getMessage(), parameters));
        dataResult.setEntry((T) data);
        return dataResult;
    }
    /**
     * 失败
     *
     * @param error 失败原因
     * @param <T>   目标类型
     * @return result
     */
    public static <T> R<T> failed(IError error, String... parameters) {
        R<T> dataResult = new R<T>();
        dataResult.setRequestId(SnowFlake.nextId());
        dataResult.setStatus(false);
        dataResult.setResponseCode(error.getCode());
        dataResult.setMessage(resolvePlaceholder(error.getMessage(), parameters));
        return dataResult;
    }

    private static String resolvePlaceholder(String msg, String... parameters) {
        if (Objects.isNull(parameters) || parameters.length == 0) {
            return msg;
        }
        return MessageFormat.format(msg, parameters);
    }

    /**
     * 包装结果
     *
     * @param source 接口结果
     * @param <T>    类型参数
     * @return 结果
     */
    public static <T> R<T> of(R<T> source) {
        R<T> ret = failed(DefaultError.of(source.getResponseCode(), source.getMessage())
                , null);
        return ret;
    }

}
