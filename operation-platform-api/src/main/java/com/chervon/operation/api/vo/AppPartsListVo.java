package com.chervon.operation.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/23 19:53
 */
@Data
@ApiModel(description = "app配件列表对象")
public class AppPartsListVo implements Serializable {

    @ApiModelProperty(value = "产品id")
    private Long productId;

    @ApiModelProperty(value = "配件id")
    private Long partsId;

    @ApiModelProperty(value = "配件图片url")
    private String iconUrl;

    @ApiModelProperty(value = "配件名称")
    private String partsName;

    @ApiModelProperty(value = "配件短描述")
    private String shortDescription;

    @ApiModelProperty(value = "配件长描述")
    private String longDescription;

    @ApiModelProperty(value = "配件技术规格")
    private String technicalSpecification;

    @ApiModelProperty(value = "配件商品型号")
    private String partsCommodityModel;

    @ApiModelProperty(value = "维保类型 1 日期 2 工时")
    private Integer maintenanceType;

    @ApiModelProperty(value = "产品配件实体id，用于复位操作的入参")
    private Long productPartsInstanceId;

}
