package com.chervon.operation.api;

import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.operation.api.vo.cache.BrandCache;
import com.chervon.operation.api.vo.cache.CategoryCache;
import com.chervon.operation.api.vo.cache.ProductCache;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 运营数据缓存接口，主要给app使用，数据的维护由运营平台增删改决定，目前支持：产品、品类、品牌、品类产品显示顺序
 */
public interface RemoteOperationCacheService {

    String DEFAULT_LANGUAGE = "en";

    String OPERATION_CACHE_ROOT = "cache:operation:";

    String CATEGORY_ROOT = OPERATION_CACHE_ROOT + "category:";

    String BRAND_ROOT = OPERATION_CACHE_ROOT + "brand:";

    String PRODUCT_ROOT = OPERATION_CACHE_ROOT + "product:";

    /**
     * 默认有效期30天
     */
    Duration DEFAULT_DURATION = Duration.ofDays(30);

    /********************品类**********************/

    /**
     * 根据品类id获取品类信息
     *
     * @param categoryId 品类id
     * @return 品类信息
     */
    default CategoryCache getCategory(Long categoryId) {
        List<CategoryCache> res = this.listCategories(Collections.singletonList(categoryId));
        return res.isEmpty() ? new CategoryCache() : res.get(0);
    }

    /**
     * 批量获取品类信息
     *
     * @param categoryIds 品类id集合
     * @return 品类列表
     */
    List<CategoryCache> listCategories(Collection<Long> categoryIds);

    default String getCategoryCacheKey(Long categoryId) {
        return CATEGORY_ROOT + categoryId;
    }

    default void removeCategoryCache(Long categoryId) {
        RedisUtils.deleteObject(getCategoryCacheKey(categoryId));
    }

    /********************品牌**********************/

    /**
     * 根据品牌id获取品牌信息
     *
     * @param brandId 品牌id
     * @return 品牌信息
     */
    default BrandCache getBrand(Long brandId){
        List<BrandCache> res = this.listBrands(Collections.singletonList(brandId));
        return res.isEmpty() ? new BrandCache() : res.get(0);
    }

    /**
     * 批量获取品牌信息
     *
     * @param brandIds 品牌id集合
     * @return 品牌列表
     */
    List<BrandCache> listBrands(Collection<Long> brandIds);

    default String getBrandCacheKey(Long brandId) {
        return BRAND_ROOT + brandId;
    }

    default void removeBrandCache(Long brandId) {
        RedisUtils.deleteObject(getBrandCacheKey(brandId));
    }

    /********************产品**********************/

    /**
     * 根据产品id获取产品信息
     *
     * @param productId 产品id
     * @return 产品信息
     */
    default ProductCache getProduct(Long productId) {
        List<ProductCache> res = this.listProducts(Collections.singletonList(productId));
        return res.isEmpty() ? new ProductCache() : res.get(0);
    }

    /**
     * 批量获取产品信息
     *
     * @param productIds 产品id集合
     * @return 产品列表
     */
    List<ProductCache> listProducts(Collection<Long> productIds);

    default String getProductCacheKey(Long productId) {
        return PRODUCT_ROOT + productId;
    }

    default void removeProductCache(Long productId) {
        RedisUtils.deleteObject(getProductCacheKey(productId));
    }

    /********************app列表顺序**********************/

    /**
     * 查询显示品类Id列表
     *
     * @return 品类列表
     */
    List<Long> getAppListOrderCategory();

}
