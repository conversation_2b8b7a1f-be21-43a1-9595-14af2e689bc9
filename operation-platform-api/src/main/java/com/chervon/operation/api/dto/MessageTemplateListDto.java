package com.chervon.operation.api.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-09-07 16:46
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class MessageTemplateListDto extends PageRequest implements Serializable {
    /**
     * 模板Id
     */
    @ApiModelProperty("模板Id")
    private String id;
    /**
     * 模板类型： 0设备消息
     */
    @ApiModelProperty("模板类型： 0设备消息")
    private Integer type;
    /**
     * 模板名称
     */
    @ApiModelProperty("模板名称")
    private String name;
    /**
     * 模板标题
     */
    @ApiModelProperty("模板标题")
    private String title;
    /**
     * 消息展示类型： 1 text，2 voice
     */
    @ApiModelProperty("消息展示类型： 1 text，2 voice")
    private Integer messageDisplayType;
}