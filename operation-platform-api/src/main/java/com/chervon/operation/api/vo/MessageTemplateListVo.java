package com.chervon.operation.api.vo;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022-09-07 16:45
 **/
@Data
public class MessageTemplateListVo implements Serializable {
    /**
     * 模板Id
     */
    private Long id;
    /**
     * 模板类型
     */
    private Integer type;
    /**
     * 模板名称
     */
    private MultiLanguageVo name;
    /**
     * 模板标题
     */
    private MultiLanguageVo title;
    /**
     * 模板内容
     */
    private MultiLanguageVo content;
    /**
     * 消息展示类型： 1 text，2 voice
     */
    private Integer messageDisplayType;
    /**
     * 被引用数量
     */
    private Integer usedTimes;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;
}
