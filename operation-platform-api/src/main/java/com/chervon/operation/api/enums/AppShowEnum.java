package com.chervon.operation.api.enums;


/**
 * 是否在app展示 0：不展示 1：展示
 *   品类、商品型号（PID）
 *
 * <AUTHOR>
 * @date 2024-9-13
 */
public enum AppShowEnum {
    /**
     * 是否在app展示 0：不展示 1：展示
     */
    HIDE(0, "隐藏"),
    SHOW(1, "展示");

    private int value;

    private String label;

    AppShowEnum(int value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public int getValue() {
        return value;
    }


}
