package com.chervon.operation.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-07 14:43
 **/
@Data
@ApiModel(value = "fleet设备分类级别中间类", description = "同步更新fleet_platform.company_device表时用")
public class CategoryLevelBo implements Serializable {
    @ApiModelProperty("品类Id")
    private Long id;
    @ApiModelProperty("父级分类编号")
    private Long parentCategoryId;
    @ApiModelProperty("分类级别: 1级 2级")
    private Integer categoryLevel;
}
