package com.chervon.operation.api.domain.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @className DeviceGroupDto
 * @description
 * @date 2022/7/12 19:33
 */
@Data
@ApiModel("用户分组结果")
public class PageUserGroupDto extends PageRequest {
    /**
     * 用户id
     **/
    @ApiModelProperty(value = "用户id", required = true)
    private Long userId;
}
