package com.chervon.operation.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/12 13:53
 */
@Data
@ApiModel(description = "fleet品类数据")
public class FleetCategoryListVo implements Serializable {
    @ApiModelProperty("品类code")
    private String code;
    @ApiModelProperty("品类层级 1 一级 2 二级（平台维护的品类）")
    private Integer categoryLevel;
    @ApiModelProperty("对应lang的品类名称，如果lang未空或者lang没有找到，则返回品类描述description")
    private String categoryName;
    private String parentCategoryCode;
    private String lang;
}
