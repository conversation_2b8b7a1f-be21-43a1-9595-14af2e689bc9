package com.chervon.operation.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/23 20:03
 */
@Data
@ApiModel(description = "app配件详情对象")
public class AppPartsVo implements Serializable {

    @ApiModelProperty(value = "配件id")
    private Long partsId;

    @ApiModelProperty(value = "配件图片url")
    private String iconUrl;

    @ApiModelProperty(value = "配件名称")
    private String partsName;

    @ApiModelProperty(value = "配件短描述")
    private String shortDescription;

    @ApiModelProperty(value = "配件长描述")
    private String longDescription;

    @ApiModelProperty(value = "配件技术规格")
    private String technicalSpecification;

    @ApiModelProperty(value = "配件商品型号")
    private String partsCommodityModel;

    @ApiModelProperty(value = "用户手册属性")
    private FileVo manual = new FileVo();

    @ApiModelProperty(value = "操作指导，即how-to videos")
    private List<FileVo> guidance = new ArrayList<>();

    @ApiModelProperty(value = "faq")
    private List<FaqVo> faq = new ArrayList<>();

    @ApiModelProperty(value = "购买链接")
    private String link;
}
