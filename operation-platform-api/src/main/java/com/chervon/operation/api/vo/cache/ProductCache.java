package com.chervon.operation.api.vo.cache;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/23 14:27
 */
@Data
public class ProductCache implements Serializable {

    private Long id;

    private Long nameLangId;

    private String nameLangCode;

    /**
     * 默认取英文文案
     */
    private String defaultName;

    private String url;

    private Long categoryId;

    private Long brandId;

    private String model;

    private String commodityModel;

    private String type;

    private String snCode;

    private String networkModes;

    private String description;

    private String questionTemplate;

    private Integer iconType;

    private List<Integer> businessType;

    private String releaseStatus;

}
