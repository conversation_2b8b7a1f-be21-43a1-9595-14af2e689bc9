package com.chervon.operation.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 通过协议Id和版本号获取协议详情的Dto
 *
 * <AUTHOR>
 * @since 2022-08-30 14:14
 **/
@Data
public class AppDeviceAgreementDetailDto implements Serializable {
    /**
     * 产品Id
     */
    @ApiModelProperty("产品Id")
    private Long productId;
    /**
     * 协议Id(主键)
     */
    @ApiModelProperty("协议Id(主键)")
    private Long id;
}
