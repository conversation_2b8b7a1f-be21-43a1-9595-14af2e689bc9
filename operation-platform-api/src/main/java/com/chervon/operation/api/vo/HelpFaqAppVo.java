package com.chervon.operation.api.vo;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/20 21:01
 */
@Data
@ApiModel(description = "app帮助中心faq对象")
public class HelpFaqAppVo implements Serializable {

    @ApiModelProperty("帮助id")
    private Long helpFaqId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("解决内容")
    private String answer;

    @ApiModelProperty("商品型号")
    private List<String> model = new ArrayList<>();

    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("点赞量")
    private int praiseCount;

    @ApiModelProperty("当前用户是否点赞 false 未点赞 true 已点赞")
    private boolean praised;

    @ApiModelProperty("阅读量")
    private int readCount;
}
