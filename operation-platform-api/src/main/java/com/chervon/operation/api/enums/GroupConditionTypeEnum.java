package com.chervon.operation.api.enums;

/**
 * 分组条件类型枚举
 * <AUTHOR>
 * @date 20:20 2022/7/28
 **/
public enum GroupConditionTypeEnum {

    /**
     * 设备条件
     **/
    DEVICE_CONDITION("DEVICE_CONDITION"),

    /**
     * 用户条件
     **/
    USER_CONDITION("USER_CONDITION");


    private String value;

    GroupConditionTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static GroupConditionTypeEnum build(String value) {
        if (DEVICE_CONDITION.getValue().equals(value)) {
            return GroupConditionTypeEnum.DEVICE_CONDITION;
        } else if (USER_CONDITION.getValue().equals(value)) {
            return GroupConditionTypeEnum.USER_CONDITION;
        } else {
            return null;
        }
    }
}
