package com.chervon.operation.api.enums;

/**
 * 设备分组条件判断规则
 * <AUTHOR>
 * @date 12:11 2022/7/26
 **/
public enum GroupConditionRule {

    /**
     * 等于
     **/
    EQUALS(":", " = "),

    /**
     * 包含
     **/
    INCLUDE(":", " in "),

    /**
     * 大于
     **/
    GRATER_THAN(">", " > "),

    /**
     * 大于等于
     **/
    GRATER_THAN_EQUAL(">=", " >= "),

    /**
     * 小于
     **/
    LESS_THAN("<", " < "),

    /**
     * 小于等于
     **/
    LESS_THAN_EQUAL("<=", " <= ");

    private final String deviceRule;

    private final String userRule;

    GroupConditionRule(String deviceRule, String userRule) {
        this.deviceRule = deviceRule;
        this.userRule = userRule;
    }

    public String getDeviceRule() {
        return deviceRule;
    }

    public String getUserRule() {
        return userRule;
    }

}
