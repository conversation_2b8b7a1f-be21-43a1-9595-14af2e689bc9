<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.authority.mapper.RoleMapper">


    <select id="getRolesByResourceIds" resultType="com.chervon.authority.domain.entity.Role">
        select r.id, r.role_name,r.role_name_lang_id,r.role_name_lang_code, r.role_type, r.role_sort, r.role_status,
        r.platform_id
        from authority_role r
        join authority_role_resource rr
        on rr.role_id=r.id
        where rr.is_deleted=0
        and r.is_deleted = 0
        and rr.resource_id in
        <if test="resourceIds != null">
            <foreach collection="resourceIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
