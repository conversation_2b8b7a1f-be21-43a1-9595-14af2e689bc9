<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.authority.mapper.ResourceMapper">

    <select id="getResourceMaxOrderNum" resultType="java.lang.Integer">
        select IFNULL(max(order_num), 0) order_num
        FROM authority_resource
        where app_id = '${appId}'
          and parent_id = '${parentId}'
          and is_deleted = 0;
    </select>

    <select id="getResourceIdByRoleIds"
            resultType="com.chervon.authority.domain.entity.Resource">
        select r.id as id,
        resource_name,
        resource_name_lang_code,
        resource_name_lang_id,
        parent_id,
        order_num,
        path,
        element_id,
        resource_type,
        is_visible,
        resource_status,
        icon,
        app_id,
        r.is_deleted as isDeleted,
        r.create_by as createBy,
        r.create_time as createTime,
        r.update_by as updateBy,
        r.update_time as updateTime,
        description
        from authority_resource r
        join authority_role_resource rr
        on r.id = rr.resource_id
        where
        r.is_deleted = 0 and rr.is_deleted = 0
        and
        rr.role_id
        in
        <if test="roleIds != null">
            <foreach collection="roleIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        and r.resource_type in
        <if test="resourceTypes != null">
            <foreach collection="resourceTypes" item="type" index="index" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="appId != null and appId != ''">
            and r.app_id = #{appId}
        </if>
    </select>

    <select id="getRequestResource" resultType="java.lang.String">
        select distinct t1.path
        from authority_resource as t1
        left join authority_role_resource as t2 on t1.id = t2.resource_id
        left join authority_role as t3 on t2.role_id = t3.id
        left join authority_user_role as t4 on t3.id = t4.role_id
        where t1.is_deleted = 0
        and t2.is_deleted = 0
        and t3.is_deleted = 0
        and t4.is_deleted = 0
        and t1.resource_status = 1
        and t3.role_status = 1
        and t1.app_id = #{appId}
        and t1.resource_type = 'R'
        and t4.authorized_guid in
        <foreach collection="guids" item="guid" index="index" open="(" separator="," close=")">
            #{guid}
        </foreach>
    </select>
</mapper>
