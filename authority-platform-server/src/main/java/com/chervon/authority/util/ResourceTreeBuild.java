package com.chervon.authority.util;

import com.chervon.authority.api.core.ResourceTreeElementVo;
import com.chervon.authority.domain.entity.Resource;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/5 14:43
 */
public class ResourceTreeBuild {

    // 保存参与构建树形的所有数据（通常数据库查询结果）
    private final List<Resource> nodeList;

    /**
     * 构造方法
     *
     * @param nodeList 将数据集合赋值给nodeList，即所有数据作为所有节点。
     */
    public ResourceTreeBuild(List<Resource> nodeList) {
        this.nodeList = nodeList;
    }

    /**
     * 获取需构建的所有根节点（顶级节点） "0"
     *
     * @return 所有根节点List集合
     */
    private List<ResourceTreeElementVo> getRootNode() {
        List<ResourceTreeElementVo> res = new ArrayList<>();
        for (Resource e : nodeList) {
            if (0 == e.getParentId()) {
                ResourceTreeElementVo vo = new ResourceTreeElementVo();
                vo.setNodeId(e.getId());
                vo.setName(e.getResourceName());
                vo.setType(e.getResourceType());
                vo.setOrderNo(e.getOrderNum());
                vo.setParentNodeId(e.getParentId());
                vo.setPath(e.getPath());
                vo.setElementId(e.getElementId());
                res.add(vo);
            }
        }
        return res;
    }

    /**
     * 根据每一个顶级节点（根节点）进行构建树形结构
     *
     * @return 构建整棵树
     */
    public List<ResourceTreeElementVo> buildTree() {
        // treeNodes：保存一个顶级节点所构建出来的完整树形
        List<ResourceTreeElementVo> treeNodes = new ArrayList<>();
        // getRootNode()：获取所有的根节点
        List<ResourceTreeElementVo> rootNodeList = getRootNode();
        for (ResourceTreeElementVo treeRootNode : rootNodeList) {
            // 将顶级节点进行构建子树
            buildChildTree(treeRootNode);
            // 完成一个顶级节点所构建的树形，增加进来
            treeNodes.add(treeRootNode);
        }
        return treeNodes;
    }

    /**
     * 递归-----构建子树形结构
     *
     * @param pNode 根节点（顶级节点）
     * @return 整棵树
     */
    private ResourceTreeElementVo buildChildTree(ResourceTreeElementVo pNode) {
        List<ResourceTreeElementVo> childTree = new ArrayList<>();
        // nodeList：所有节点集合（所有数据）
        for (Resource e : nodeList) {
            if (e.getParentId().equals(pNode.getNodeId())) {
                // 再递归进行判断当前节点的情况，调用自身方法
                ResourceTreeElementVo vo = new ResourceTreeElementVo();
                vo.setNodeId(e.getId());
                vo.setName(e.getResourceName());
                vo.setType(e.getResourceType());
                vo.setOrderNo(e.getOrderNum());
                vo.setParentNodeId(e.getParentId());
                vo.setPath(e.getPath());
                vo.setElementId(e.getElementId());
                childTree.add(buildChildTree(vo));
            }
        }
        // for循环结束，即节点下没有任何节点，树形构建结束，设置树结果
        pNode.setChildren(childTree);
        return pNode;
    }

}
