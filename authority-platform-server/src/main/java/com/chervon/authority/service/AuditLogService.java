package com.chervon.authority.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.authority.domain.dto.AuditLogListDto;
import com.chervon.authority.domain.entity.AuditLog;
import com.chervon.authority.domain.vo.AuditLogVo;
import com.chervon.common.core.domain.PageResult;

/**
 * <AUTHOR>
 * @since 2022-09-03 10:36
 **/
public interface AuditLogService extends IService<AuditLog> {

    /**
     * 分页获取审计日志列表
     *
     * @param auditLogListDto 搜索条件
     * @return 审计日志Vo分页结果
     */
    PageResult<AuditLogVo> list(AuditLogListDto auditLogListDto);
}
