package com.chervon.authority.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.authority.domain.dto.AuditLogListDto;
import com.chervon.authority.domain.entity.AuditLog;
import com.chervon.authority.domain.vo.AuditLogVo;
import com.chervon.authority.mapper.AuditLogMapper;
import com.chervon.authority.service.AuditLogService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-09-03 10:36
 **/
@Service
@Slf4j
public class AuditLogServiceImpl extends ServiceImpl<AuditLogMapper, AuditLog> implements AuditLogService {

    @Override
    public PageResult<AuditLogVo> list(AuditLogListDto auditLogListDto) {
        LambdaQueryWrapper<AuditLog> auditLogLambdaQueryWrapper = new LambdaQueryWrapper<AuditLog>()
                .like(StringUtils.isNotBlank(auditLogListDto.getUserId()), AuditLog::getUserId, auditLogListDto.getUserId())
                .like(StringUtils.isNotBlank(auditLogListDto.getUserName()), AuditLog::getUserName, auditLogListDto.getUserName())
                .eq(StringUtils.isNotBlank(auditLogListDto.getOperation()), AuditLog::getOperation, auditLogListDto.getOperation())
                .like(StringUtils.isNotBlank(auditLogListDto.getOperationModel()), AuditLog::getOperationModel, auditLogListDto.getOperationModel())
                .orderByDesc(AuditLog::getCreateTime);
        Page<AuditLog> page = this.getBaseMapper().selectPage(new Page<>(auditLogListDto.getPageNum(),
                auditLogListDto.getPageSize()), auditLogLambdaQueryWrapper);
        PageResult<AuditLogVo> result = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<AuditLog> records = page.getRecords();
        List<AuditLogVo> resList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(records)) {
            records.forEach(r -> {
                AuditLogVo convert = ConvertUtil.convert(r, AuditLogVo.class);
                convert.setUserId(r.getUserId() != null ? r.getUserId().toString() : null);
                resList.add(convert);
            });
        }
        ConvertUtil.convertList(page.getRecords(), AuditLogVo.class);
        result.setList(resList);
        return result;
    }
}
