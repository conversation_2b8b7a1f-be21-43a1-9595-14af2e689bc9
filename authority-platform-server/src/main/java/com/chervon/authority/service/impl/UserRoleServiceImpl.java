package com.chervon.authority.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.authority.api.exception.AuthorityErrorCode;
import com.chervon.authority.api.service.RemoteDataLimitRefreshService;
import com.chervon.authority.config.ExceptionMessageUtil;
import com.chervon.authority.domain.dto.role.UserRoleDto;
import com.chervon.authority.domain.dto.role.UserRoleItem;
import com.chervon.authority.domain.dto.role.UserRolePageDto;
import com.chervon.authority.domain.dto.role.UserRoleUnbindDto;
import com.chervon.authority.domain.entity.Role;
import com.chervon.authority.domain.entity.UserRole;
import com.chervon.authority.domain.vo.UserRoleDetailVo;
import com.chervon.authority.domain.vo.UserRoleVo;
import com.chervon.authority.mapper.UserRoleMapper;
import com.chervon.authority.service.PlatformService;
import com.chervon.authority.service.RoleService;
import com.chervon.authority.service.RpcService;
import com.chervon.authority.service.UserRoleService;
import com.chervon.authority.util.PageAssembler;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.usercenter.api.service.OrganizationQueryService;
import com.chervon.usercenter.api.service.RemoteUserRoleService;
import com.chervon.usercenter.api.service.SysUserQueryService;
import com.chervon.usercenter.api.vo.SysUserVo;
import com.chervon.usercenter.api.vo.UserRoleAddVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.ClusterRules;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户和角色关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-27
 */
@Service
@Slf4j
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements UserRoleService {

    @Autowired
    @Lazy
    private RoleService roleService;

    @Autowired
    @Lazy
    private PlatformService platformService;

    @Autowired
    private RpcService rpcService;
    @DubboReference
    private SysUserQueryService userQueryService;

    @DubboReference
    private OrganizationQueryService organizationQueryService;

    @DubboReference
    private RemoteUserRoleService remoteUserRoleService;

    @DubboReference(cluster = ClusterRules.BROADCAST)
    private RemoteDataLimitRefreshService remoteDataLimitRefreshService;

    @Override
    public List<Long> getRoleIdsByUserAndOrgGuid(List<String> guids) {
        LambdaQueryWrapper<UserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(UserRole::getAuthorizedGuid, guids);
        wrapper.select(UserRole::getRoleId);
        List<UserRole> list = this.list(wrapper);
        List<Long> roleIds = new ArrayList<>();
        for (UserRole userRole : list) {
            roleIds.add(userRole.getRoleId());
        }
        if (roleIds.isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<Role> roleWrapper = new LambdaQueryWrapper<Role>()
                .eq(Role::getRoleStatus, CommonConstant.ONE)
                .in(Role::getId, roleIds);
        List<Role> roleList = roleService.list(roleWrapper);
        return roleList.stream().map(Role::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bind(UserRoleDto userRoleDto) {
        List<UserRole> oldList = this.list(new LambdaQueryWrapper<UserRole>().eq(UserRole::getRoleId, userRoleDto.getRoleId()));
        List<String> oldAuthGuids = oldList.stream().map(UserRole::getAuthorizedGuid).collect(Collectors.toList());
        userRoleDto.getUserRoleItems().removeIf(e -> oldAuthGuids.contains(e.getAuthorizedGuid()));
        if (userRoleDto.getUserRoleItems().isEmpty()) {
            return;
        }
        List<String> allGuids = new ArrayList<>();
        allGuids.addAll(userRoleDto.getUserRoleItems().stream().map(UserRoleItem::getAuthorizedGuid).collect(Collectors.toList()));
        allGuids.addAll(oldList.stream().map(UserRole::getAuthorizedGuid).collect(Collectors.toList()));
        List<UserRoleAddVo> userRoleAdds = remoteUserRoleService.userRoleAdd(LocaleContextHolder.getLocale().getLanguage(), allGuids);
        this.remove(new LambdaQueryWrapper<UserRole>().eq(UserRole::getRoleId, userRoleDto.getRoleId()));
        List<UserRole> collect = userRoleAdds.stream().map(e -> {
            UserRole ur = new UserRole();
            ur.setRoleId(userRoleDto.getRoleId());
            ur.setDescription(userRoleDto.getDescription());
            ur.setType(e.getType());
            ur.setAuthorizedGuid(e.getGuid());
            return ur;
        }).collect(Collectors.toList());
        if (!collect.isEmpty()) {
            this.saveBatch(collect);
        }
        // 刷新数据权限
        Role byId = roleService.getById(userRoleDto.getRoleId());
        if (byId != null && byId.getRoleType() == 1) {
            remoteDataLimitRefreshService.refresh();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(UserRoleDto userRoleDto) {
        // 全量删除原有的角色-用户 绑定数据
        List<UserRole> oldUserRoles = this.listByRoleId(userRoleDto.getRoleId());
        this.removeBatchByIds(oldUserRoles);
        // 全量新增角色-用户 绑定数据
        List<UserRole> newUserRoles = toUserRole(userRoleDto);
        this.saveBatch(newUserRoles);
        // 刷新数据权限
        Role byId = roleService.getById(userRoleDto.getRoleId());
        if (byId != null && byId.getRoleType() == 1) {
            remoteDataLimitRefreshService.refresh();
        }
    }

    @Override
    public PageResult<UserRoleVo> getPage(UserRolePageDto userRoleQuery) {
        Page<UserRole> page = new Page<>(userRoleQuery.getPageNum(),
                userRoleQuery.getPageSize());
        LambdaQueryWrapper<UserRole> queryWrapper = new LambdaQueryWrapper<>();
        if (userRoleQuery.getRoleType() != null) {
            List<Long> roleIds = roleService.listIdByType(userRoleQuery.getRoleType());
            if (CollectionUtil.isNotEmpty(roleIds)) {
                queryWrapper.in(UserRole::getRoleId, roleIds);
            }
        }
        if (StringUtils.hasLength(userRoleQuery.getQuery())) {
            queryWrapper.and(wrapper -> {
                wrapper.eq(UserRole::getRoleId, userRoleQuery.getQuery());
                // 根据查询条件查询角色表是否有该角色名的记录
                List<Long> roleIds = roleService.selectIdByName(userRoleQuery.getQuery());
                if (CollectionUtil.isNotEmpty(roleIds)) {
                    wrapper.or().in(UserRole::getRoleId, roleIds);
                }
            });
        }
        queryWrapper.orderByDesc(UserRole::getCreateTime);
        Page<UserRole> userRolePage = this.page(page, queryWrapper);
        PageResult pageResult = PageAssembler.assemble(userRolePage);
        List<UserRoleVo> userRoleVos = new ArrayList<>();
        List<UserRole> list = pageResult.getList();
        list.forEach(userRole -> {
            Role role = roleService.getById(userRole.getRoleId());
            UserRoleVo userRoleVo = ConvertUtil.convert(userRole, UserRoleVo.class);
            if (0 == userRole.getType()) {
                SysUserVo user = rpcService.getUserByGuid(userRole.getAuthorizedGuid());
                if (user != null) {
                    userRoleVo.setUserName(user.getUserName() + "/" + user.getEmployeeNumber());
                }
            } else {
                userRoleVo.setOrganizationName(rpcService.
                        getOrgNameByGuid(userRole.getAuthorizedGuid()));
            }
            userRoleVo.setPlatformName(platformService.selectNameById(role.getPlatformId()));
            userRoleVo.setRoleName(com.chervon.authority.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(role.getRoleNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            userRoleVos.add(userRoleVo);
        });
        pageResult.setList(userRoleVos);
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbind(UserRoleUnbindDto userRoleUnbindDto) {
        LambdaQueryWrapper<UserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserRole::getRoleId, userRoleUnbindDto.getRoleId()).
                eq(UserRole::getAuthorizedGuid, userRoleUnbindDto.getAuthorizedGuid());
        UserRole userRole = this.getOne(wrapper);
        if (null == userRole) {
            throw ExceptionMessageUtil.getException(AuthorityErrorCode.AUTHORITY_ROLE_IS_NOT_FOUND);
        }
        this.removeById(userRole);
        // 刷新数据权限
        Role byId = roleService.getById(userRoleUnbindDto.getRoleId());
        if (byId != null && byId.getRoleType() == 1) {
            remoteDataLimitRefreshService.refresh();
        }
    }

    @Override
    public List<UserRole> listByRoleId(Long roleId) {
        LambdaQueryWrapper<UserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserRole::getRoleId, roleId);
        return this.list(wrapper);
    }

    @Override
    public List<String> getRoleNames(String userGuid) {
        log.debug("UserRoleServiceImpl#getRoleNames lang:{}", LocaleContextHolder.getLocale().getLanguage());
        List<String> guids = organizationQueryService.treeOrgGuids(userGuid);
        if (CollectionUtils.isEmpty(guids)) {
            return new ArrayList<>();
        }
        log.debug("UserRoleServiceImpl#getRoleNames userGuid:{}", userGuid);
        log.debug("UserRoleServiceImpl#getRoleNames guids:{}", guids);
        List<String> roleNames = this.getBaseMapper().getRoleNames(guids);
        log.debug("UserRoleServiceImpl#getRoleNames roleNames:{}", roleNames);
        if (roleNames == null) {
            return new ArrayList<>();
        }
        roleNames.forEach(e -> {
            String ss = com.chervon.authority.config.MultiLanguageUtil.getByLangCode(e, LocaleContextHolder.getLocale().getLanguage());
            log.debug("UserRoleServiceImpl#getRoleNames ss:{}", ss);
        });
        return roleNames.stream().filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct().map(e -> (String) com.chervon.authority.config.MultiLanguageUtil.getByLangCode(e, LocaleContextHolder.getLocale().getLanguage())).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbindById(Long req) {
        UserRole userRole = this.getById(req);
        if (userRole == null) {
            throw ExceptionMessageUtil.getException(AuthorityErrorCode.AUTHORITY_ROLE_BINDING_USER_NOT_EXIST);
        }
        this.removeById(req);
        // 刷新数据权限
        if (userRole.getRoleId() != null) {
            Role byId = roleService.getById(userRole.getRoleId());
            if (byId != null && byId.getRoleType() == 1) {
                remoteDataLimitRefreshService.refresh();
            }
        }
    }

    @Override
    public UserRoleDetailVo getDetailById(Long id) {
        UserRole userRole = this.getById(id);
        if (userRole == null) {
            throw ExceptionMessageUtil.getException(AuthorityErrorCode.AUTHORITY_ROLE_BINDING_USER_NOT_EXIST);
        }
        // set userId description
        UserRoleDetailVo result = ConvertUtil.convert(userRole, UserRoleDetailVo.class);
        result.setPlatformId(roleService.getPlatformByRoleId(userRole.getRoleId()));

        List<UserRole> userRoles = this.listByRoleId(userRole.getRoleId());
        List<UserRoleItem> userRoleItems = new ArrayList<>();
        userRoles.forEach(userRole1 -> {
            UserRoleItem userRoleItem = new UserRoleItem();
            userRoleItem.setType(userRole1.getType());
            userRoleItem.setAuthorizedGuid(userRole1.getAuthorizedGuid());
            userRoleItems.add(userRoleItem);
        });

        result.setUserRoleItems(userRoleItems);
        return result;
    }

    @Override
    public List<Long> listDataRoleIds(String userGuid, String organizationGuid) {
        return this.getBaseMapper().listDataRoleIds(userGuid, organizationGuid);
    }

    private List<UserRole> toUserRole(UserRoleDto userRoleDto) {
        List<UserRole> userRoles = ConvertUtil
                .convertList(userRoleDto.getUserRoleItems(), UserRole.class);
        for (UserRole userRole : userRoles) {
            userRole.setRoleId(userRoleDto.getRoleId());
            userRole.setDescription(userRoleDto.getDescription());
        }
        return userRoles;
    }
}
