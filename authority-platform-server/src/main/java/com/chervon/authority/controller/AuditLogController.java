package com.chervon.authority.controller;

import com.chervon.authority.domain.dto.AuditLogListDto;
import com.chervon.authority.domain.vo.AuditLogVo;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.authority.service.AuditLogService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 审计日志相关接口
 *
 * <AUTHOR>
 * @since 2022-09-03 10:24
 **/
@Api(tags = "审计日志相关接口")
@RestController
@RequestMapping("/audit/log")
public class AuditLogController {

    @Resource
    private AuditLogService auditLogService;

    /**
     * 分页获取审计日志列表
     *
     * @param auditLogListDto 搜索条件
     * @return 审计日志分页结果
     */
    @ApiOperation("分页获取审计日志列表")
    @Log(businessType = BusinessType.VIEW)
    @PostMapping("/list")
    public R<PageResult<AuditLogVo>> list(@RequestBody @Validated AuditLogListDto auditLogListDto) {
        return R.ok(auditLogService.list(auditLogListDto));
    }
}
