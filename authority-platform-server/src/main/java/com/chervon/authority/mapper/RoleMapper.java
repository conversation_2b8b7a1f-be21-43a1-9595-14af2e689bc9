package com.chervon.authority.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.authority.domain.entity.Role;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-27
 */
public interface RoleMapper extends BaseMapper<Role> {

    /**
     * 根据资源Id获取和资源关联的角色Id
     * @param resourceIds 资源Id
     * @return 角色列表
     */
    List<Role> getRolesByResourceIds(@Param("resourceIds") List<Long> resourceIds);

}
