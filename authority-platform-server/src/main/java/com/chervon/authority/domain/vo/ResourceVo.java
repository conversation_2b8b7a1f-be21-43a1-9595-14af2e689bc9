package com.chervon.authority.domain.vo;

import com.chervon.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-06-20 15:31
 **/
@Data
public class ResourceVo extends BaseEntity {
    /**
     * 资源Id
     */
    private Long id;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 父菜单名称
     */
    private Long parentId;
    /**
     * 显示顺序
     */
    private Integer orderNum;
    /**
     * 路由地址
     */
    private String path;
    /**
     * 资源类型(M目录 P page，C菜单 F按钮)
     */
    private String resourceType;
    /**
     * 资源状态(0正常 1停用)
     */
    private Integer resourceStatus;
    /**
     * 资源状态(0隐藏 1可见)
     */
    private Integer isVisible;
    /**
     * 菜单图标
     */
    private String icon;
    /**
     * 系统APPID
     */
    private String appId;
    /**
     * 系统名称
     */
    private String platformName;
    /**
     * 描述
     */
    private String description;
    /**
     * 是否被选中
     */
    private Boolean checked;

    /**
     * elementId
     */
    private String elementId;

    /**
     * 子资源列表
     */
    private List<ResourceVo> childResource;
}
