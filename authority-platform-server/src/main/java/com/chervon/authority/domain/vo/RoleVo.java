package com.chervon.authority.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-05-06 17:47
 **/
@Data
public class RoleVo {
    /**
     * 角色Id
     */
    private Long id;
    /**
     * 角色名
     */
    private String roleName;

    private Long roleNameLangId;
    /**
     * 角色类型(0功能角色 1数据角色)
     */
    private Integer roleType;
    /**
     * 角色顺序
     */
    private Integer roleSort;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 角色状态(0正常 1停用)
     */
    private Integer roleStatus;
    /**
     * 描述
     */
    private String description;
}
