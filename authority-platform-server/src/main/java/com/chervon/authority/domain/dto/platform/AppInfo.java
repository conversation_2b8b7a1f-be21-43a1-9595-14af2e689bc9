package com.chervon.authority.domain.dto.platform;

import com.chervon.authority.api.exception.AuthorityErrorCode;
import com.chervon.authority.config.ExceptionMessageUtil;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.ValueObject;
import com.chervon.common.core.utils.CommonUtil;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022-06-20
 */
@Getter
public class AppInfo implements ValueObject<AppInfo> {

    /**
     * 系统Appid
     */
    private final String appId;

    /**
     * 系统密钥，用于单点登录
     */
    private final String appSecret;


    public AppInfo(String appId, String appSecret) {
        if (StringUtils.isEmpty(appId)) {
            throw new IllegalArgumentException("平台AppId不能为空");
        }
        this.appId = appId;
        // 形成一个随机字符串作为Appid
        this.appSecret = appSecret;
    }

    public static AppInfo create(String appId) {
        if (StringUtils.isEmpty(appId)) {
            throw new IllegalArgumentException("平台AppId不能为空");
        }
        if (appId.length() > CommonConstant.THIRTY_SIX || appId.contains(CommonConstant.SPACE)) {
            throw ExceptionMessageUtil.getException(AuthorityErrorCode.AUTHORITY_PLATFORM_APPID_INVALID);
        }
        // 形成一个随机字符串作为Appid
        String appSecret = CommonUtil.getGUID(CommonConstant.SIXTEEN);
        return new AppInfo(appId, appSecret);
    }

    @Override
    public boolean sameValueAs(AppInfo other) {
        return other != null && this.appId.equals(other.getAppId())
                && this.appSecret.equals(other.getAppSecret());
    }

    @Override
    public String toString() {
        return "AppInfo{" +
                "appId='" + appId + '\'' +
                "appSecret=''" + appSecret + '\'' +
                '}';
    }
}
