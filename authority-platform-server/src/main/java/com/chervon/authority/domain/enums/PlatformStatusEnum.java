package com.chervon.authority.domain.enums;

import com.chervon.authority.api.exception.AuthorityErrorCode;
import com.chervon.authority.config.ExceptionMessageUtil;

/**
 * <AUTHOR>
 * @date 20220606
 */
public enum PlatformStatusEnum {

    /**
     * 启用
     */
    ACTIVATION(1, "启用"),

    /**
     * 不启用
     */
    DISABLE(0, "不启用");


    private final Integer value;

    private final String label;

    PlatformStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public Integer getValue() {
        return value;
    }

    /**
     * 获取StatusEnum
     *
     * @param value
     * @return
     */
    public static PlatformStatusEnum getStatusEnum(Integer value) {
        for (PlatformStatusEnum s : PlatformStatusEnum.values()) {
            if (value.equals(s.getValue())) {
                return s;
            }
        }
        throw ExceptionMessageUtil.getException(AuthorityErrorCode.AUTHORITY_PARAM_ERROR);
    }

    public boolean sameValueAs(final PlatformStatusEnum other) {
        return this.equals(other);
    }
}
