package com.chervon.authority.domain.vo;

import com.chervon.authority.domain.dto.role.UserRoleItem;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @className UserRoleDetailVo
 * @description
 * @date 2022/6/15 17:41
 */
@Data
public class UserRoleDetailVo {
    /**
     * platformId
     */
    private Long platformId;

    /**
     * 用户或组织列表
     **/
    private List<UserRoleItem> userRoleItems;

    /**
     * 角色ID
     **/
    private Long roleId;

    /**
     * 描述
     **/
    private String description;
}
