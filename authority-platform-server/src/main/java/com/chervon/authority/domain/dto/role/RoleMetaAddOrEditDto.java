package com.chervon.authority.domain.dto.role;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2022-06-22 15:01
 **/
@Data
public class RoleMetaAddOrEditDto {
    /**
     * mapper方法Id
     */
    @NotEmpty
    private String mapperId;
    /**
     * 元数据ID(会与角色ID绑定)
     */
    @NotEmpty
    private String metaId;
    /**
     * 元数据类型(1:sql, 2:类方法)
     */
    @Min(1)
    @Max(2)
    private Integer metaType;
    /**
     * SQL
     */
    @NotEmpty
    private String metaSql;
    /**
     * 数据角色名
     */
    @NotEmpty
    private String roleName;
    /**
     * 角色所属平台Id
     */
    @NotNull
    private Long platformId;
    /**
     * 角色Id,仅修改角色使用
     */
    private Long roleId;
    /**
     * 备注
     */
    private String description;
}
