package com.chervon.authority.domain.vo;

import com.chervon.common.core.annotation.Sensitive;
import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.common.core.enums.SensitiveStrategy;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022-09-03 11:30
 **/
@Data
public class AuditLogVo implements Serializable {
    /**
     * 用户Id
     */
    @ApiModelProperty("用户Id")
    @Sensitive(strategy = SensitiveStrategy.USER_ID)
    private String userId;
    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    @Sensitive(strategy = SensitiveStrategy.CHINESE_NAME)
    private String userName;
    /**
     * 浏览器类型
     */
    @ApiModelProperty("浏览器类型")
    private String browser;
    /**
     * 设备Mac
     */
    @ApiModelProperty("设备Mac")
    private String deviceMac;
    /**
     * ip
     */
    @ApiModelProperty("ip")
    @Sensitive(strategy = SensitiveStrategy.IP)
    private String ip;
    /**
     * 操作类型
     */
    @ApiModelProperty("操作类型")
    private String operation;
    /**
     * 操作模块
     */
    @ApiModelProperty("操作模块")
    private String operationModel;
    /**
     * 操作内容
     */
    @ApiModelProperty("操作内容")
    private String operationContent;
    /**
     * 入参
     */
    @ApiModelProperty("入参")
    private String input;
    /**
     * 出参
     */
    @ApiModelProperty("出参")
    private String output;
    /**
     * 操作结果：0失败 1成功
     */
    @ApiModelProperty("操作结果：0失败 1成功")
    private Integer operationResult;
    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime operationTime;
}
