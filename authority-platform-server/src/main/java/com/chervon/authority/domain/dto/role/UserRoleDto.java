package com.chervon.authority.domain.dto.role;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @className UserRoleAddCommand
 * @description 用户授权 command
 * @date 2022/6/15 16:57
 */
@Data
public class UserRoleDto {
    /**
     * 用户或组织列表
     **/
    @Valid
    private List<UserRoleItem> userRoleItems;

    /**
     * 角色id
     **/
    @NotNull
    private Long roleId;

    /**
     * 描述
     **/
    private String description;
}
