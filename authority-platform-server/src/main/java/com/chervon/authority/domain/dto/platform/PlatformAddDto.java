package com.chervon.authority.domain.dto.platform;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 创建平台
 *
 * <AUTHOR>
 * @date 2022-06-20
 */
@Data
public class PlatformAddDto implements Serializable {

    @NotEmpty(message = "platformName不能为空")
    private String platformName;

    @NotEmpty(message = "appId不能为空")
    private String appId;

    @NotEmpty(message = "baseUrl不能为空")
    private String baseUrl;

    private String description;

    /**
     * 图标
     */
    private String icon;

}
