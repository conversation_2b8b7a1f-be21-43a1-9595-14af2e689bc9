package com.chervon.authority.domain.dto.role;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-05-06 15:07
 **/
@Data
public class FunctionRoleAddDto {
    /**
     * 角色名
     */
    @NotEmpty
    private String roleName;
    /**
     * 平台Id
     */
    private Long platformId;
    /**
     * 资源Id列表
     */
    private List<Long> resourceList;
    /**
     * 描述
     */
    private String description;
}
