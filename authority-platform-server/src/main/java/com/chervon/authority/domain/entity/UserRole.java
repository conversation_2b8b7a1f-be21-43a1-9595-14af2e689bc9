package com.chervon.authority.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户和角色关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-27
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("authority_user_role")
public class UserRole extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 授权用户或组织ID
     **/
    private String authorizedGuid;

    /**
     * id类型 0:用户授权 1:组织授权
     **/
    private Integer type;

    /**
     * 角色ID
     **/
    private Long roleId;

    /**
     * 描述
     **/
    private String description;
}
