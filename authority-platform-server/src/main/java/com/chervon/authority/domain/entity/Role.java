package com.chervon.authority.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.chervon.common.mybatis.config.BaseDo;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 角色信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-27
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("authority_role")
public class Role extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色名称
     **/
    private String roleName;

    private Long roleNameLangId;

    private String roleNameLangCode;

    /**
     * 角色类型(0功能角色1数据角色)
     **/
    private Integer roleType;

    /**
     * 显示顺序
     **/
    private Integer roleSort;

    /**
     * 角色状态(0停用 1正常)
     **/
    private Integer roleStatus;

    /**
     * 平台id
     **/
    private Long platformId;

    /**
     * 描述
     **/
    private String description;
}
