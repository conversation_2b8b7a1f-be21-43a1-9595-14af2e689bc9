package com.chervon.authority.config;

import cn.hutool.extra.spring.SpringUtil;
import com.chervon.authority.api.exception.AuthorityErrorCode;
import com.chervon.authority.api.exception.AuthorityException;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;

/**
 * <AUTHOR>
 * @date 2023/4/17 11:18
 */
public class ExceptionMessageUtil {

    private static final RemoteMultiLanguageService remoteMultiLanguageService = SpringUtil.getBean("remoteMultiLanguageService");

    public static AuthorityException getException(AuthorityErrorCode errorCode, Object... args) {
        AuthorityException exception = new AuthorityException(errorCode, args);
        String code = errorCode.getCode();
        if (code != null) {
            String language = remoteMultiLanguageService.simpleFindMultiLanguageByCode(code);
            if (language != null) {
                exception.setTempMessage(language);
            }
        }
        return exception;
    }

    public static AuthorityException getException(String message) {
        return new AuthorityException(message);
    }

}
