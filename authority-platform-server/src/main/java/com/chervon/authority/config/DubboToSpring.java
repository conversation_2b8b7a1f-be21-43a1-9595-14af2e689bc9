package com.chervon.authority.config;

import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/4/17 10:54
 */
@Component
public class DubboToSpring {

    @DubboReference
    RemoteMultiLanguageService remoteMultiLanguageService;

    @Bean(name = "remoteMultiLanguageService")
    public RemoteMultiLanguageService getRemoteMultiLanguageService() {
        return remoteMultiLanguageService;
    }
}
