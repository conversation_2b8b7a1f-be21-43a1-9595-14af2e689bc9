package com.chervon.common.mybatis.handler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.mybatis.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * MP注入处理器
 *
 * <AUTHOR> Li
 * @date 2021/4/25
 */
@Slf4j
public class CreateAndUpdateMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseDo) {
                BaseDo baseEntity = (BaseDo) metaObject.getOriginalObject();
                baseEntity.setCreateTime(Optional.ofNullable(baseEntity.getCreateTime()).orElse(LocalDateTime.now()));
                baseEntity.setUpdateTime(baseEntity.getCreateTime());
                baseEntity.setCreateBy(Optional.ofNullable(baseEntity.getCreateBy()).orElse(LoginUserUtil.getSign()));
                baseEntity.setUpdateBy(baseEntity.getCreateBy());
                baseEntity.setIsDeleted(0);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(String.valueOf(HttpStatus.HTTP_UNAUTHORIZED),"自动注入异常 => " + e.getMessage());
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseDo) {
                BaseDo baseEntity = (BaseDo) metaObject.getOriginalObject();
                // 更新时间填充(不管为不为空)
                baseEntity.setUpdateTime(LocalDateTime.now());
                baseEntity.setUpdateBy(Optional.ofNullable(baseEntity.getUpdateBy()).orElse(LoginUserUtil.getSign()));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(String.valueOf(HttpStatus.HTTP_UNAUTHORIZED),"自动注入异常 => " + e.getMessage());
        }
    }

}
