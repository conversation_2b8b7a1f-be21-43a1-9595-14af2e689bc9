package com.chervon.feedback.controller;

import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/1 14:07
 */
@RestController
@RequestMapping("/m/language")
@AllArgsConstructor
@Api(value = "多语言接口", tags = {"多语言接口"})
@Slf4j
public class MultiLanguageController {

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @ApiOperation(value = "根据sysCode查询所有多语言")
    @PostMapping("listLanguageBySysCode")
    public Map<String, String> listLanguageBySysCode(@RequestBody SingleInfoReq<String> req) {
        return remoteMultiLanguageService.listLanguageBySysCode(req.getReq());
    }
}
