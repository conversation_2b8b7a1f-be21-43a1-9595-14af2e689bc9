package com.chervon.feedback.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.common.sso.CurrentLoginUtil;
import com.chervon.feedback.api.core.FeedBackEnum;
import com.chervon.feedback.api.dto.AppFeedbackCommitDto;
import com.chervon.feedback.api.dto.AppFeedbackEditDto;
import com.chervon.feedback.api.dto.AppFeedbackReplyDto;
import com.chervon.feedback.api.dto.AppFeedbackReqDto;
import com.chervon.feedback.api.service.RemoteFeedbackService;
import com.chervon.feedback.api.vo.AppFeedbackDetailVo;
import com.chervon.feedback.api.vo.AppFeedbackPageVo;
import com.chervon.feedback.api.vo.AppFeedbackReplyVo;
import com.chervon.feedback.api.vo.AppFeedbackStatusVo;
import com.chervon.feedback.config.ExceptionMessageUtil;
import com.chervon.feedback.entity.Feedback;
import com.chervon.feedback.entity.FeedbackReply;
import com.chervon.feedback.mapper.FeedbackMapper;
import com.chervon.feedback.service.FeedbackReplyService;
import com.chervon.feedback.service.FeedbackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chervon.common.core.constant.CommonConstant.COMMON_SPLIT;
import static com.chervon.feedback.api.core.FeedbackConstant.*;
import static com.chervon.feedback.api.exception.FeedbackErrorCode.*;

/**
 * <AUTHOR>
 * @date 2023/3/17 19:17
 */
@Service
@DubboService
@Slf4j
public class RemoteFeedbackServiceImpl implements RemoteFeedbackService {

    private final FeedbackService feedbackService;

    private final FeedbackReplyService feedbackReplyService;

    private final AwsProperties awsProperties;

    @Autowired
    private FeedbackMapper feedbackMapper;

    public RemoteFeedbackServiceImpl(FeedbackService feedbackService, FeedbackReplyService feedbackReplyService, AwsProperties awsProperties) {
        this.feedbackService = feedbackService;
        this.feedbackReplyService = feedbackReplyService;
        this.awsProperties = awsProperties;
    }

    @Override
    public boolean showRed() {
        // 当用户下的展示反馈存在showRed为1
        long count = feedbackService.count(new LambdaQueryWrapper<Feedback>()
                .eq(Feedback::getUserId, CurrentLoginUtil.getCurrentId())
                .ne(Feedback::getReplyState, String.valueOf(FeedBackEnum.DELETED.getValue()))
                .eq(Feedback::getShowRed, (Integer)FeedBackEnum.SHOW.getValue()));
        return count > 0;
    }

    @Override
    public void commit(AppFeedbackCommitDto req) {
        if (req == null) {
            log.warn("commit req null");
            return;
        }
        if (req.getCategory1() == null || !CATEGORY_1.contains(req.getCategory1())) {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_COMMIT_CATEGORY_1_INVALID, req.getCategory1());
        }
        if ("more".equals(req.getCategory1()) && !CATEGORY_2_MORE.contains(req.getCategory2())) {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_COMMIT_MORE_CATEGORY_2_INVALID, req.getCategory2());
        }
        if (StringUtils.isBlank(req.getFeedbackContent())) {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_COMMIT_CONTENT_BLANK);
        }
        if (req.getCommitPhoneType() == null) {
            req.setCommitPhoneType("");
        }
        if (StringUtils.isNotBlank(req.getCommitPhoneType()) && !PHONE_TYPE.contains(req.getCommitPhoneType().toLowerCase())) {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_COMMIT_PHONE_TYPE_INVALID, req.getCommitPhoneType());
        }
        Feedback feedback = new Feedback();
        feedback.setCategory1(req.getCategory1());
        feedback.setCategory2(req.getCategory2());
        feedback.setCommitPhoneModel(req.getCommitPhoneModel());
        feedback.setCommitPhoneOsVersion(req.getCommitPhoneOsVersion());
        feedback.setCommitPhoneType(req.getCommitPhoneType().toLowerCase());
        feedback.setCommitAppVersion(req.getCommitAppVersion());
        if ("device".equals(req.getCategory1())) {
            feedback.setProductId(req.getProductId());
            feedback.setDeviceId(req.getDeviceId());
            feedback.setCommodityModel(req.getCommodityModel());
            feedback.setDeviceSn(req.getDeviceSn());
            feedback.setRnVersion(req.getRnVersion());
        }
        feedback.setContent(req.getFeedbackContent());
        if (!StringUtils.isBlank(req.getPhone())) {
            if (StringUtils.isBlank(req.getAreaCode())) {
                throw ExceptionMessageUtil.getException(FEEDBACK_APP_COMMIT_PHONE_AREA_CODE_BLANK);
            }
            feedback.setPhone(req.getPhone());
            feedback.setAreaCode(req.getAreaCode());
        }
        if (req.getFeedbackPictures() == null) {
            req.setFeedbackPictures(new ArrayList<>());
        }
        feedback.setPictures(req.getFeedbackPictures().stream()
                .filter(StringUtils::isNoneBlank)
                .map(e -> UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), e)).collect(Collectors.joining(COMMON_SPLIT)));
        feedback.setUserId(CurrentLoginUtil.getCurrentId());
        // 辅助信息
        feedback.setAppShowTime(LocalDateTime.now());
        feedback.setShowRed((Integer) FeedBackEnum.HIDE.getValue());
        feedback.setReplyState((String) FeedBackEnum.TO_BE_REPLIED.getValue());
        feedbackService.save(feedback);
    }

    @Override
    public PageResult<AppFeedbackPageVo> page(Integer pageNum, Integer pageSize) {
        // 排序：根据用户反馈提交时间、客服回复时间、用户回复时间，按照时间从新到旧排排序，列表上的时间，显示数据最新时间（用户提交/客服回复/用户回复）。
        Page<Feedback> page = feedbackService.page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<Feedback>()
                .eq(Feedback::getUserId, CurrentLoginUtil.getCurrentId())
                .ne(Feedback::getReplyState, String.valueOf(FeedBackEnum.DELETED.getValue()))
                .orderByDesc(Feedback::getAppShowTime));
        PageResult<AppFeedbackPageVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        res.setList(page.getRecords().stream().map(e -> {
            AppFeedbackPageVo vo = new AppFeedbackPageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setFeedbackId(e.getId());
            vo.setFeedbackContent(e.getContent());
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    @Override
    public void delete(List<Long> feedbackIds) {
        if (!CollectionUtils.isEmpty(feedbackIds)) {
            feedbackService.update(new Feedback(), new LambdaUpdateWrapper<Feedback>()
                    .eq(Feedback::getUserId, CurrentLoginUtil.getCurrentId())
                    .in(Feedback::getId, feedbackIds)
                    .set(Feedback::getReplyState, String.valueOf(FeedBackEnum.DELETED.getValue())));
        }
    }

    @Override
    public AppFeedbackDetailVo detail(Long feedbackId) {
        AppFeedbackDetailVo detail = new AppFeedbackDetailVo();
        if (feedbackId == null) {
            return null;
        }
        Feedback feedback = feedbackService.getOne(new LambdaQueryWrapper<Feedback>()
                .eq(Feedback::getUserId, CurrentLoginUtil.getCurrentId())
                .eq(Feedback::getId, feedbackId));
        if (feedback == null) {
            return null;
        }
        // 已读
        Feedback f = new Feedback();
        f.setId(feedbackId);
        f.setShowRed((Integer) FeedBackEnum.HIDE.getValue());
        feedbackService.updateById(f);

        BeanUtils.copyProperties(feedback, detail);
        detail.setFeedbackId(feedbackId);
        detail.setFeedbackContent(feedback.getContent());
        if (StringUtils.isNotBlank(feedback.getPictures())) {
            detail.setFeedbackPictures(Arrays.asList(feedback.getPictures().split(COMMON_SPLIT)));
        }
        detail.setFeedbackTime(feedback.getCreateTime());
        // 查询回复并排序
        List<FeedbackReply> replies = feedbackReplyService.list(new LambdaQueryWrapper<FeedbackReply>()
                .eq(FeedbackReply::getFeedbackId, feedbackId));
        if (replies.isEmpty()) {
            return detail;
        }
        Map<String, FeedbackReply> map = replies.stream().collect(Collectors.toMap(e -> String.valueOf(e.getId()), Function.identity(), (e1, e2) -> e2));
        //排序、数据组装
        replies.stream()
                .sorted(Comparator.comparing(FeedbackReply::getReplyPath, Comparator.nullsFirst(String::compareTo))
                        .thenComparing(FeedbackReply::getCreateTime))
                .map(e -> {
                    if (StringUtils.isBlank(e.getReplyPath())) {
                        return e.getId() + "";
                    }
                    return e.getReplyPath() + "," + e.getId();
                })
                .forEach(e -> {
                    String[] split = e.split(",");
                    String last = split[split.length - 1];
                    FeedbackReply reply = map.get(last);
                    if (reply != null) {
                        AppFeedbackReplyVo vo = new AppFeedbackReplyVo();
                        vo.setReplyId(reply.getId());
                        vo.setReplyType(reply.getType());
                        vo.setReplyContent(reply.getContent());
                        if (StringUtils.isNotBlank(reply.getPictures())) {
                            vo.setReplyPictures(Arrays.asList(reply.getPictures().split(COMMON_SPLIT)));
                        }
                        vo.setReplyTime(reply.getCreateTime());
                        detail.getReplies().add(vo);
                    }
                });
        return detail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reply(AppFeedbackReplyDto req) {
        if (req.getFeedbackId() == null) {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_REPLY_FEEDBACK_ID_NULL);
        }
        Feedback feedback = feedbackService.getOne(new LambdaQueryWrapper<Feedback>()
                .eq(Feedback::getUserId, CurrentLoginUtil.getCurrentId())
                .ne(Feedback::getReplyState, String.valueOf(FeedBackEnum.DELETED.getValue()))
                .eq(Feedback::getId, req.getFeedbackId()));
        if (feedback == null) {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_REPLY_FEEDBACK_ID_INVALID, req.getFeedbackId());
        }
        // 判断反馈是否可以回复，当反馈的回复状态是已关闭时不能进行用户回复，为适配老APP版本，toast提示用户
        if (feedback.getReplyState().toLowerCase().equals(FeedBackEnum.CLOSED.getValue())) {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_REPLY_CLOSED_STATE_NOT_ALLOWED_REPLY);
        }
        // 判断反馈是否可以回复，当反馈的回复状态是待回复时不能进行用户回复
        if (String.valueOf(FeedBackEnum.TO_BE_REPLIED.getValue()).equals(feedback.getReplyState())) {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_REPLY_REPLY_STATE_TO_BE_REPLIED);
        }
        if (req.getReplyId() == null) {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_REPLY_REPLY_ID_NULL);
        }
        FeedbackReply toReply = feedbackReplyService.getById(req.getReplyId());
        if (toReply == null) {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_REPLY_REPLY_ID_INVALID, req.getReplyId());
        }
        if (StringUtils.isBlank(req.getReplyContent())) {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_REPLY_CONTENT_BLANK);
        }
        // 新增回复
        FeedbackReply reply = new FeedbackReply();
        reply.setFeedbackId(feedback.getId());
        reply.setType(String.valueOf(FeedBackEnum.USER.getValue()));
        reply.setReplyId(toReply.getId());
        reply.setReplyPath(StringUtils.isBlank(toReply.getReplyPath()) ? toReply.getId() + "" : toReply.getReplyPath() + "," + toReply.getId());
        reply.setContent(req.getReplyContent());
        if (req.getReplyPictures() == null) {
            req.setReplyPictures(new ArrayList<>());
        }
        reply.setPictures(req.getReplyPictures().stream()
                .filter(StringUtils::isNoneBlank)
                .map(e -> UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), e)).collect(Collectors.joining(COMMON_SPLIT)));
        feedbackReplyService.save(reply);
        // 编辑反馈
        Feedback f = new Feedback();
        f.setId(feedback.getId());
        f.setAppShowTime(reply.getCreateTime());
        f.setReplyState(String.valueOf(FeedBackEnum.TO_BE_REPLIED.getValue()));
        f.setReplyType(String.valueOf(FeedBackEnum.USER.getValue()));
        f.setReplyContent(reply.getContent());
        f.setReplyTime(reply.getCreateTime());
        feedbackService.updateById(f);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(AppFeedbackEditDto req) {
        if (req.getType().equals(CommonConstant.ONE)) {
            // 判断feedback状态
            validateReplyFeedbackState(req.getType(), req.getId());
            Feedback feedback = new Feedback();
            feedback.setId(req.getId());
            feedback.setContent(req.getContent() == null ? "" : req.getContent());
            LocalDateTime now = LocalDateTime.now();
            feedback.setCreateTime(now);
            feedback.setAppShowTime(now);
            feedback.setPictures(req.getPictures().isEmpty() ? "" : req.getPictures().stream()
                    .filter(StringUtils::isNoneBlank)
                    .map(e -> UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), e)).collect(Collectors.joining(COMMON_SPLIT)));
            feedbackService.updateById(feedback);
        } else if (req.getType().equals(CommonConstant.TWO)) {
            // 判断reply对应feedback的状态
            validateReplyFeedbackState(req.getType(), req.getId());
            FeedbackReply replyOld = feedbackReplyService.getById(req.getId());

            // 更新reply信息
            FeedbackReply replyNew = new FeedbackReply();
            replyNew.setId(req.getId());
            replyNew.setContent(req.getContent() != null ? req.getContent() : "");
            replyNew.setCreateTime(LocalDateTime.now());
            replyNew.setPictures(req.getPictures().isEmpty() ? "" : req.getPictures().stream()
                    .filter(StringUtils::isNoneBlank)
                    .map(e -> UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), e)).collect(Collectors.joining(COMMON_SPLIT)));
            feedbackReplyService.updateById(replyNew);

            // 更新feedback信息
            Feedback feedback = feedbackService.getById(replyOld.getFeedbackId());
            feedback.setReplyContent(req.getContent() == null ? "" : req.getContent());
            feedback.setAppShowTime(replyNew.getCreateTime());
            feedback.setReplyTime(replyNew.getCreateTime());
            feedbackService.updateById(feedback);

        } else {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_EDIT_FEEDBACK_TYPE_INVALID, req.getType());
        }
    }

    /**
     * 校验反馈状态
     */
    private void validateReplyFeedbackState(Integer type, Long id) {
        if (type.equals(CommonConstant.ONE)) {
            // 1. 获取对应的反馈信息
            Feedback feedback = feedbackService.getById(id);
            // 2. 判断反馈状态
            if (String.valueOf(FeedBackEnum.CLOSED.getValue()).equals(feedback.getReplyState())) {
                throw ExceptionMessageUtil.getException(FEEDBACK_APP_REPLY_CLOSED_STATE_NOT_ALLOWED_REPLY);
            }
        } else if (type.equals(CommonConstant.TWO)) {
            // 1. 获取回复信息
            FeedbackReply reply = feedbackReplyService.getById(id);
            // 2. 获取对应的反馈信息
            Feedback feedback = feedbackService.getById(reply.getFeedbackId());
            // 3. 判断反馈状态
            if (String.valueOf(FeedBackEnum.CLOSED.getValue()).equals(feedback.getReplyState())) {
                throw ExceptionMessageUtil.getException(FEEDBACK_APP_REPLY_CLOSED_STATE_NOT_ALLOWED_REPLY);
            }
        } else {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_EDIT_FEEDBACK_TYPE_INVALID, type);
        }
    }

    @Override
    public AppFeedbackStatusVo hasFeedback(AppFeedbackReqDto req) {
        AppFeedbackStatusVo vo = new AppFeedbackStatusVo();
        boolean status = false;
        if (req.getType().equals(CommonConstant.ONE)) {
            Assert.hasText(req.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, "deviceId");
            Assert.notNull(req.getUserId(), ErrorCode.PARAMETER_NOT_PROVIDED, "userId");
            // 根据用户ID、设备ID、replyState查询最新的device feedbackId
            Long feedbackId = feedbackMapper.selectDeviceFeedback(req.getDeviceId(), req.getUserId());
            if (Objects.nonNull(feedbackId)) {
                status = true;
                vo.setStatus(status);
                vo.setFeedbackId(feedbackId);
            } else {
                vo.setStatus(status);
            }
        } else if (req.getType().equals(CommonConstant.TWO)) {
            Assert.notNull(req.getUserId(), ErrorCode.PARAMETER_NOT_PROVIDED, "userId");
            // 根据用户ID、category2=app、replyState查询最新的app issue feedbackId
            Long appFeedbackId = feedbackMapper.selectAppFeedback(req.getUserId());
            if (Objects.nonNull(appFeedbackId)) {
                status = true;
                vo.setStatus(status);
                vo.setFeedbackId(appFeedbackId);
            } else {
                vo.setStatus(status);
            }
        } else {
            throw ExceptionMessageUtil.getException(FEEDBACK_APP_FIND_FEEDBACK_BY_TYPE, req.getType());
        }
        return vo;
    }

    @Override
    public void closeFeedback(Long feedbackId) {
        Feedback feedback = feedbackService.getById(feedbackId);
        if (!feedback.getReplyState().toLowerCase().equals(String.valueOf(FeedBackEnum.CLOSED.getValue()))) {
            // 更新反馈状态为closed
            feedback.setReplyState(String.valueOf(FeedBackEnum.CLOSED.getValue()));
            feedbackService.updateById(feedback);
        } else {
            log.warn("feedback is closed, no need to close!");
        }

    }
}
