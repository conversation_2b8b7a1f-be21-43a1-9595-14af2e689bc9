package com.chervon.feedback.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/17 20:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "用户反馈导出查询条件对象")
public class FeedbackExportDto extends FeedbackPageDto implements Serializable {

    private static final long serialVersionUID = 6341672755075720623L;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("手机型号")
    private String commitPhoneModel;

    @ApiModelProperty("系统版本号")
    private String commitPhoneOsVersion;

    @ApiModelProperty("APP类型---字典appType")
    private String commitPhoneType;

    @ApiModelProperty("APP版本号")
    private String commitAppVersion;

    @ApiModelProperty("产品的商品型号")
    private String commodityModel;

    @ApiModelProperty("问题类型选择more时选择的第二分类----字典feedbackMoreCategory")
    private String feedbackMoreCategory;

    @ApiModelProperty("设备SN")
    private String deviceSn;

    @ApiModelProperty("RN版本号")
    private String rnVersion;

    @ApiModelProperty("手机区号，写死+1到+?")
    private String areaCode;

    @ApiModelProperty("手机号码")
    private String phone;

    @ApiModelProperty("客服备注")
    private String csRemark;

}
