package com.chervon.feedback.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/3/9 19:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("feedback")
public class Feedback extends BaseDo implements Serializable {

    private static final long serialVersionUID = -5183076200601314472L;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户反馈内容
     */
    private String content;

    /**
     * 分类1 device 设备问题 more 更多
     */
    private String category1;

    /**
     * 分类2 设备问题下记录设备昵称，更多下记录register 注册问题 app APP问题 ota OTA升级 dealer 经销商查找 faq FAQ
     */
    private String category2;

    /**
     * app 反馈列表显示的时间，如果是第一次提交则显示用户提交时间，否则用客服、用户的反馈时间填充
     */
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime appShowTime;

    /**
     * app 是否显示小红点 0 不显示 1 显示
     */
    private Integer showRed;

    /**
     * 用户提交的图片，多个，用[SPLIT]分割
     */
    private String pictures;

    /**
     * 手机区号，手机号码为空，区号不保存
     */
    private String areaCode;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 提交时的手机型号，如huawei等
     */
    private String commitPhoneModel;

    /**
     * 提交时的手机系统版本
     */
    private String commitPhoneOsVersion;

    /**
     * 提交时的手机类型 android ios
     */
    private String commitPhoneType;

    /**
     * 提交时的app版本
     */
    private String commitAppVersion;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 产品的商品型号
     */
    private String commodityModel;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备对应的rn版本
     */
    private String rnVersion;

    /**
     * 回复状态 replied 已回复 toBeReplied 待回复
     */
    private String replyState;

    /**
     * 回复类型 user 用户 cs 客服
     */
    private String replyType;

    /**
     * 回复内容
     */
    private String replyContent;

    /**
     * 客服、用户回复时间
     */
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime replyTime;

    /**
     * 客服的备注
     */
    private String csRemark;

}
