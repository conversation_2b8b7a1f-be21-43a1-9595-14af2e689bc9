package com.chervon.feedback;

import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.satoken.config.SaTokenConfiguration;
import com.chervon.common.sso.SaTokenConfigure;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(exclude = {SaTokenConfigure.class, SaTokenConfiguration.class},
        scanBasePackages = {"com.chervon.common.log.aspect","com.chervon.feedback"})
@EnableDubbo
@EnableAsync
@EnableConfigurationProperties(AwsProperties.class)
public class FeedbackApplication {

    public static void main(String[] args) {
        SpringApplication.run(FeedbackApplication.class, args);
    }

}
