package com.chervon.feedback.resp;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/3/17 20:09
 */
@Data
@ApiModel(description = "用户反馈分页内容")
public class FeedbackPageVo implements Serializable {

    private static final long serialVersionUID = -6271873985983485649L;

    @ApiModelProperty("反馈id")
    private Long feedbackId;

    @ApiModelProperty("问题类型---字典feedbackCategory")
    private String feedbackCategory;

    @ApiModelProperty("问题状态---字典feedbackReplyState")
    private String replyState;

    @ApiModelProperty("原始问题内容")
    private String feedbackContent;

    @ApiModelProperty("回复类型 user 用户 cs 客服")
    private String replyType;

    @ApiModelProperty("反馈回复内容")
    private String replyContent;

    @ApiModelProperty("原始反馈时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime feedbackTime;

    @ApiModelProperty("最新反馈/回复时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime replyTime;

    @ApiModelProperty("设备SN")
    private String deviceSn;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户email")
    private String email;
}
