package com.chervon.feedback.resp;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/17 21:08
 */
@Data
@ApiModel(description = "客服、用户回复对象")
public class FeedbackReplyVo implements Serializable {

    private static final long serialVersionUID = -7859027070541345255L;
    @ApiModelProperty("回复id")
    private Long replyId;

    @ApiModelProperty("回复类型 user 用户 cs 客服")
    private String replyType;

    @ApiModelProperty("回复内容")
    private String replyContent;

    @ApiModelProperty("回复图片")
    private List<String> replyPictures = new ArrayList<>();

    @ApiModelProperty("回复时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime replyTime;

}
