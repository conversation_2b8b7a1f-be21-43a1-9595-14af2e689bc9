package com.chervon.feedback.resp;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/17 20:34
 */
@Data
public class FeedbackExcel implements Serializable {

    private static final long serialVersionUID = -649128624408383604L;
    @Alias("反馈ID")
    private Long feedbackId;

    @Alias("用户ID")
    private Long userId;

    @Alias("用户邮箱")
    private String email;

    @Alias("手机型号")
    private String commitPhoneModel;

    @Alias("系统版本号")
    private String commitPhoneOsVersion;

    @Alias("APP类型")
    private String commitPhoneType;

    @Alias("APP版本号")
    private String commitAppVersion;

    @Alias("问题类型")
    private String feedbackCategory;

    @Alias("类型名称")
    private String categoryName;

    @Alias("设备SN")
    private String deviceSn;

    @Alias("RN版本号")
    private String rnVersion;

    @Alias("问题状态")
    private String replyState;

    @Alias("手机号码")
    private String areaCodeAndPhone;

    @Alias("问题描述")
    private String feedbackContent;

    @Alias("反馈图片")
    private List<String> feedbackPictures = new ArrayList<>();

    @Alias("用户反馈时间")
    private String feedbackTime;

    @Alias("客服备注")
    private String csRemark;

    @Alias("回复类型")
    private String replyType;

    @Alias("回复内容")
    private String replyContent;

    @Alias("回复图片")
    private List<String> replyPictures = new ArrayList<>();

    @Alias("回复时间")
    private String replyTime;
}
