package com.chervon.feedback.config;

import cn.hutool.extra.spring.SpringUtil;
import com.chervon.common.i18n.util.MessageTools;
import com.chervon.feedback.api.exception.FeedbackErrorCode;
import com.chervon.feedback.api.exception.FeedbackException;

/**
 * <AUTHOR>
 * @date 2023/4/17 11:18
 */
public class ExceptionMessageUtil {

    private static final  MessageTools messageTools = SpringUtil.getBean("messageTools");

    public static FeedbackException getException(FeedbackErrorCode errorCode, Object... args) {
        FeedbackException exception = new FeedbackException(errorCode, args);
        String code = errorCode.getCode();
        if (code != null) {
            String language = messageTools.getCodeValue(code);
            if (language != null) {
                exception.setTempMessage(language);
            }
        }
        return exception;
    }

}
