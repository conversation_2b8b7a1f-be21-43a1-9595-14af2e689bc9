package com.chervon.feedback.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.i18n.util.MessageTools;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.feedback.api.core.FeedBackEnum;
import com.chervon.feedback.api.core.FeedbackConstant;
import com.chervon.feedback.config.ExceptionMessageUtil;
import com.chervon.feedback.domain.bo.DictBo;
import com.chervon.feedback.domain.bo.DictNodeBo;
import com.chervon.feedback.entity.Feedback;
import com.chervon.feedback.entity.FeedbackReply;
import com.chervon.feedback.entity.enums.StaticMultiLanguageEnum;
import com.chervon.feedback.req.FeedbackCsReplyDto;
import com.chervon.feedback.req.FeedbackExportDto;
import com.chervon.feedback.req.FeedbackPageDto;
import com.chervon.feedback.req.FeedbackSaveCsRemarkDto;
import com.chervon.feedback.resp.*;
import com.chervon.feedback.service.DictService;
import com.chervon.feedback.service.FeedbackReplyService;
import com.chervon.feedback.service.FeedbackService;
import com.chervon.feedback.service.MainService;
import com.chervon.iot.app.api.RemoteUserSettingService;
import com.chervon.iot.app.api.vo.UserSettingBo;
import com.chervon.message.api.RemoteMessageService;
import com.chervon.message.api.dto.MessageDto;
import com.chervon.message.api.enums.MessageTypeEnum;
import com.chervon.message.api.enums.OsType;
import com.chervon.message.api.enums.PushMethodEnum;
import com.chervon.usercenter.api.service.RemoteAppUserService;
import com.chervon.usercenter.api.vo.AppUserVo;
import com.chervon.usercenter.api.vo.UserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chervon.common.core.constant.CommonConstant.COMMON_SPLIT;
import static com.chervon.feedback.api.exception.FeedbackErrorCode.*;

/**
 * <AUTHOR>
 * @date 2023/3/17 19:56
 */
@Service
@Slf4j
public class MainServiceImpl implements MainService {

    @Autowired
    private DictService dictService;

    private final FeedbackService feedbackService;

    private final FeedbackReplyService feedbackReplyService;

    private final AwsProperties awsProperties;

    @DubboReference
    private RemoteAppUserService remoteAppUserService;

    @DubboReference
    private RemoteUserSettingService remoteUserSettingService;

    @DubboReference
    private RemoteMessageService remoteMessageService;

    @Resource
    private MessageTools messageTools;

    //feedback消息路由前缀
    private static final String FEED_BACK_MESSAGE_ROUTE_PREFIX="ChervonIoT://EGO/UserCenter/feedbackHistoryDetail";

    public MainServiceImpl(FeedbackService feedbackService, FeedbackReplyService feedbackReplyService, AwsProperties awsProperties) {
        this.feedbackService = feedbackService;
        this.feedbackReplyService = feedbackReplyService;
        this.awsProperties = awsProperties;
    }

    private String getUserId(FeedbackPageDto req) {
        if (req.getUserId() != null) {
            return req.getUserId();
        }
        return Optional.ofNullable(req.getEmail())
                .map(email -> {
                    AppUserVo user = remoteAppUserService.getAppUserByEmail(email);
                    return Optional.ofNullable(user)
                            .map(AppUserVo::getUserId)
                            .orElse(null);
                })
                .orElse(null);
    }

    @Override
    public PageResult<FeedbackPageVo> page(FeedbackPageDto req) {
        String userId = getUserId(req);
        // 查询feedback数据
        Page<Feedback> page = getFeedbackPage(req, userId);
        PageResult<FeedbackPageVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        if (page.getRecords().isEmpty()) {
            res.setList(new ArrayList<>());
            return res;
        }
        // 组装结果
        List<Feedback> records = page.getRecords();
        if (StringUtils.isNotBlank(req.getEmail())) {
            res.setList(convertToVoListWithEmail(records, req.getEmail()));
        } else {
            res.setList(convertToVoListWithEmails(records));
        }

        return res;
    }

    private Page<Feedback> getFeedbackPage(FeedbackPageDto req, String userId) {
        // 如果email存在但userId为null,返回空结果
        if (StringUtils.isNotEmpty(req.getEmail()) && userId == null) {
            return new Page<>(req.getPageNum(), req.getPageSize());
        }
        // 构建基础查询条件
        LambdaQueryWrapper<Feedback> queryWrapper = buildBaseQueryWrapper(req);
        // 根据email和userId判断添加不同的查询条件
        if (StringUtils.isNotEmpty(req.getEmail()) && userId != null) {
            queryWrapper.eq(StringUtils.isNotEmpty(userId), Feedback::getUserId, userId);
        }
        // 执行分页查询
        return feedbackService.page(new Page<>(req.getPageNum(), req.getPageSize()), queryWrapper);
    }

    /**
     * 构建基础查询条件
     */
    private LambdaQueryWrapper<Feedback> buildBaseQueryWrapper(FeedbackPageDto req) {
        return new LambdaQueryWrapper<Feedback>()
                .like(StringUtils.isNotBlank(req.getFeedbackId()),
                        Feedback::getId, req.getFeedbackId())
                .ge(StringUtils.isNotBlank(req.getFeedbackStartTime()),
                        Feedback::getCreateTime, req.getFeedbackStartTime())
                .le(StringUtils.isNotBlank(req.getFeedbackEndTime()),
                        Feedback::getCreateTime, req.getFeedbackEndTime())
                .ge(StringUtils.isNotBlank(req.getReplyStartTime()),
                        Feedback::getReplyTime, req.getReplyStartTime())
                .le(StringUtils.isNotBlank(req.getReplyEndTime()),
                        Feedback::getReplyTime, req.getReplyEndTime())
                .eq(StringUtils.isNotBlank(req.getFeedbackCategory()),
                        Feedback::getCategory1, req.getFeedbackCategory())
                .eq(StringUtils.isNotBlank(req.getReplyState()),
                        Feedback::getReplyState, req.getReplyState())
                .like(StringUtils.isNotBlank(req.getFeedbackContent()),
                        Feedback::getContent, req.getFeedbackContent())
                .like(StringUtils.isNotBlank(req.getReplyContent()),
                        Feedback::getReplyContent, req.getReplyContent())
                .like(StringUtils.isNotBlank(req.getDeviceSn()),
                        Feedback::getDeviceSn, req.getDeviceSn())
                .orderByDesc(Feedback::getCreateTime);
    }

    private List<FeedbackPageVo> convertToVoListWithEmail(List<Feedback> feedbacks, String fixedEmail) {
        return feedbacks.stream()
                .map(e -> convertToVo(e, fixedEmail))
                .collect(Collectors.toList());
    }

    private List<FeedbackPageVo> convertToVoListWithEmails(List<Feedback> feedbacks) {
        List<Long> userIds = feedbacks.stream()
                .map(Feedback::getUserId)
                .collect(Collectors.toList());

        Map<Long, String> userEmailMap = remoteAppUserService.listUserEmailByUserIds(userIds)
                .stream()
                .collect(Collectors.toMap(UserVo::getId, UserVo::getEmail));

        return feedbacks.stream()
                .map(e -> convertToVo(e, userEmailMap.get(e.getUserId())))
                .collect(Collectors.toList());
    }

    private FeedbackPageVo convertToVo(Feedback feedback, String email) {
        FeedbackPageVo vo = new FeedbackPageVo();
        vo.setFeedbackId(feedback.getId());
        vo.setFeedbackCategory(feedback.getCategory1());
        vo.setReplyState(feedback.getReplyState());
        vo.setFeedbackContent(feedback.getContent());
        vo.setReplyType(feedback.getReplyType());
        vo.setReplyContent(feedback.getReplyContent());
        vo.setFeedbackTime(feedback.getCreateTime());
        vo.setReplyTime(feedback.getReplyTime());
        vo.setDeviceSn(feedback.getDeviceSn());
        vo.setUserId(feedback.getUserId());
        vo.setEmail(email);
        return vo;
    }

    @Override
    public FeedbackDetailVo detail(Long feedbackId) {
        if (feedbackId == null) {
            throw ExceptionMessageUtil.getException(FEEDBACK_WEB_FEEDBACK_ID_NULL);
        }
        Feedback feedback = feedbackService.getById(feedbackId);
        if (feedback == null) {
            throw ExceptionMessageUtil.getException(FEEDBACK_WEB_FEEDBACK_ID_INVALID, feedbackId);
        }
        FeedbackDetailVo detail = new FeedbackDetailVo();
        BeanUtils.copyProperties(feedback, detail);
        detail.setUserId(feedback.getUserId() == null ? null : feedback.getUserId() + "");
        detail.setEmail(feedback.getUserId() == null ? null : remoteAppUserService.getAppUser(feedback.getUserId()).getEmail());
        detail.setFeedbackId(feedbackId);
        detail.setFeedbackCategory(feedback.getCategory1());
        detail.setCategoryName(feedback.getCategory2());
        detail.setAreaCodeAndPhone(Optional.ofNullable(feedback.getAreaCode()).orElse("") + Optional.ofNullable(feedback.getPhone()).orElse(""));
        detail.setFeedbackContent(feedback.getContent());
        if (StringUtils.isNotBlank(feedback.getPictures())) {
            detail.setFeedbackPictures(Arrays.asList(feedback.getPictures().split(COMMON_SPLIT)));
        }
        detail.setFeedbackTime(feedback.getCreateTime());
        // 查询回复并排序
        List<FeedbackReply> replies = feedbackReplyService.list(new LambdaQueryWrapper<FeedbackReply>()
                .eq(FeedbackReply::getFeedbackId, feedbackId));
        if (replies.isEmpty()) {
            return detail;
        }
        Map<String, FeedbackReply> map = replies.stream().collect(Collectors.toMap(e -> String.valueOf(e.getId()), Function.identity(), (e1, e2) -> e2));
        replies.stream()
                .sorted(Comparator.comparing(FeedbackReply::getReplyPath, Comparator.nullsFirst(String::compareTo))
                        .thenComparing(FeedbackReply::getCreateTime))
                .map(e -> {
                    if (StringUtils.isBlank(e.getReplyPath())) {
                        return e.getId() + "";
                    }
                    return e.getReplyPath() + "," + e.getId();
                }).forEach(e -> {
                    String[] split = e.split(",");
                    String last = split[split.length - 1];
                    FeedbackReply reply = map.get(last);
                    if (reply != null) {
                        FeedbackReplyVo vo = new FeedbackReplyVo();
                        vo.setReplyId(reply.getId());
                        vo.setReplyType(reply.getType());
                        vo.setReplyContent(reply.getContent());
                        if (StringUtils.isNotBlank(reply.getPictures())) {
                            vo.setReplyPictures(Arrays.asList(reply.getPictures().split(COMMON_SPLIT)));
                        }
                        vo.setReplyTime(reply.getCreateTime());
                        detail.getReplies().add(vo);
                    }
                });
        return detail;
    }

    @Override
    public void saveCsRemark(FeedbackSaveCsRemarkDto req) {
        if (req.getFeedbackId() == null) {
            throw ExceptionMessageUtil.getException(FEEDBACK_WEB_FEEDBACK_ID_NULL);
        }
        Feedback feedback = feedbackService.getById(req.getFeedbackId());
        if (feedback == null) {
            throw ExceptionMessageUtil.getException(FEEDBACK_WEB_FEEDBACK_ID_INVALID, req.getFeedbackId());
        }
        if (StringUtils.isBlank(req.getCsRemark())) {
            throw ExceptionMessageUtil.getException(FEEDBACK_WEB_FEEDBACK_CS_REMARK_BLANK);
        }
        Feedback one = new Feedback();
        one.setId(feedback.getId());
        one.setCsRemark(req.getCsRemark());
        feedbackService.updateById(one);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void csReply(FeedbackCsReplyDto req) {
        // 1. 检查 feedbackId
        if (req.getFeedbackId() == null) {
            throw ExceptionMessageUtil.getException(FEEDBACK_WEB_FEEDBACK_ID_NULL);
        }

        // 2. 获取并验证 feedback
        Feedback feedback = feedbackService.getById(req.getFeedbackId());
        if (feedback == null) {
            throw ExceptionMessageUtil.getException(FEEDBACK_WEB_FEEDBACK_ID_INVALID, req.getFeedbackId());
        }

        // 3. 检查反馈状态
        validateFeedbackState(feedback);

        // 4. 验证回复对象
        FeedbackReply toReply = null;
        if (req.getReplyId() != null) {
            toReply = feedbackReplyService.getById(req.getReplyId());
            if (toReply == null) {
                throw ExceptionMessageUtil.getException(FEEDBACK_WEB_REPLY_ID_INVALID, req.getReplyId());
            }
        }

        // 5. 检查内容
        if (StringUtils.isBlank(req.getContent())) {
            throw ExceptionMessageUtil.getException(FEEDBACK_WEB_REPLY_CONTENT_BLANK);
        }

        // 6. 创建新回复
        FeedbackReply reply = createNewReply(req, feedback, toReply);
        feedbackReplyService.save(reply);

        // 7. 更新反馈状态
        Feedback updatedFeedback = updateFeedbackStatus(feedback, reply);

        // 8. 推送消息
        pushFeedbackMsg(updatedFeedback);
    }

    /**
     * 验证反馈状态
     */
    private void validateFeedbackState(Feedback feedback) {
        if (feedback.getReplyState().toLowerCase().equals(FeedBackEnum.CLOSED.getValue())) {
            throw ExceptionMessageUtil.getException(FEEDBACK_WEB_REPLY_CLOSED_STATE_NOT_ALLOWED_REPLY);
        }
        if (feedback.getReplyState().toLowerCase().equals(FeedBackEnum.DELETED.getValue())) {
            throw ExceptionMessageUtil.getException(FEEDBACK_WEB_REPLY_DELETED_STATE_NOT_ALLOWED_REPLY);
        }
    }

    /**
     * 创建新的回复对象
     */
    private FeedbackReply createNewReply(FeedbackCsReplyDto req, Feedback feedback, FeedbackReply toReply) {
        FeedbackReply reply = new FeedbackReply();
        reply.setFeedbackId(feedback.getId());
        reply.setType(String.valueOf(FeedBackEnum.CUSTOMER_SERVICE.getValue()));

        // 设置回复引用关系
        if (toReply != null) {
            reply.setReplyId(toReply.getId());
            reply.setReplyPath(StringUtils.isBlank(toReply.getReplyPath()) ?
                    toReply.getId() + "" :
                    toReply.getReplyPath() + "," + toReply.getId());
        }

        reply.setContent(req.getContent());

        // 处理图片
        List<String> pictures = req.getPictures();
        if (pictures == null) {
            pictures = new ArrayList<>();
        }
        reply.setPictures(pictures.stream()
                .filter(StringUtils::isNoneBlank)
                .map(e -> UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), e))
                .collect(Collectors.joining(COMMON_SPLIT)));

        return reply;
    }

    /**
     * 更新反馈状态
     */
    private Feedback updateFeedbackStatus(Feedback feedback, FeedbackReply reply) {
        Feedback f = new Feedback();
        f.setId(feedback.getId());
        f.setUserId(feedback.getUserId());
        f.setAppShowTime(reply.getCreateTime());
        f.setShowRed((Integer) FeedBackEnum.SHOW.getValue());
        f.setReplyState(String.valueOf(FeedBackEnum.REPLIED.getValue()));
        f.setReplyType(String.valueOf(FeedBackEnum.CUSTOMER_SERVICE.getValue()));
        f.setReplyContent(reply.getContent());
        f.setReplyTime(reply.getCreateTime());
        feedbackService.updateById(f);
        return f;
    }

    /**
     * 推送feedback消息
     */
    @Override
    public void pushFeedbackMsg(Feedback feedback) {

        List<MessageDto> messages = new ArrayList<>();
        //墓碑、banner推送方式、消息中心
        List<Integer> pushTypes = Arrays.asList(PushMethodEnum.TOMBSTONE.getPushTypes(), PushMethodEnum.BANNER.getPushTypes());
        //用户id
        long userId = feedback.getUserId();
        //反馈id
        long feedbackId = feedback.getId();
        //回复内容
        String replyContent=feedback.getReplyContent();
        //消息实体组装
        MessageDto messageDto = assembleMsseageDto(userId,feedbackId,replyContent,pushTypes);
        if (Objects.isNull(messageDto)) {
            log.error("userId:{},feedbackId:{} assembleMsseag failed",userId,feedbackId);
            return;
        }
        //设置消息内容
        messages.add(messageDto);
        log.info("MainServiceImpl#pushFeedbackMsg -> rpc调用RemoteMessageService发送消息:{}", messages);
        remoteMessageService.pushMessage(messages);
    }

    @Override
    public void closeFeedback(Long feedbackId) {
        Feedback feedback = feedbackService.getById(feedbackId);
        // 更新反馈状态为closed
        feedback.setReplyState(String.valueOf(FeedBackEnum.CLOSED.getValue()));
        feedbackService.updateById(feedback);
    }

    private MessageDto assembleMsseageDto(long userId, long feedbackId, String msgContent, List<Integer> pushTypes) {
        MessageDto messageDto=new MessageDto();
        messageDto.setUserId(String.valueOf(userId));
        messageDto.setSystemMessageId(String.valueOf(feedbackId));
        messageDto.setPushTypes(pushTypes);
        messageDto.setMessageType(MessageTypeEnum.FEEDBACK_MSG.getValue());
        // 将路由地址发送在payLoadData中
        Map<String, String> payloadDataMap = new HashMap<>(CommonConstant.ONE);
        payloadDataMap.put("rutePath", FEED_BACK_MESSAGE_ROUTE_PREFIX+"?feedbackId="+feedbackId);
        messageDto.setPayloadData(payloadDataMap);
        //获取用户使用app类型
        String userAppTypeCode=remoteAppUserService.getUserAppTypeCodeByUserId(userId);
        if(StringUtils.isBlank(userAppTypeCode)){
            log.error("userId:{} userAppTypeCode is empty", userId);
            return null;
        }
        messageDto.setDeviceType(OsType.valueOf(userAppTypeCode.toUpperCase()));
        // 用户校验并获取
        UserSettingBo userSettingBo=checkAndGetUserSetting(userId,pushTypes);
        if(Objects.isNull(userSettingBo)){
            log.error("userId:{} userSettingBo not satisfied", userId);
            return null;
        }
        messageDto.setToken(userSettingBo.getPushToken());
        messageDto.setPushSwitch(userSettingBo.getSystemMessageSwitch());
        //根据用户设置语言选择对应title文案
        String title = messageTools.getCodeValue(StaticMultiLanguageEnum.FEEDBACK_MESSAGE_TITLE.getCode(),userSettingBo.getLanguage());
        messageDto.setTitle(title);
        messageDto.setContent(msgContent);
        return messageDto;
    }

    /**
     *
     * @param userId
     */
    private UserSettingBo checkAndGetUserSetting(long userId,List<Integer> pushTypes) {
        // 推送消息用户Token(iot-app user_setting表中的push_token字段)
        UserSettingBo userSettingBo =remoteUserSettingService.get(userId);
        if (userSettingBo == null) {
            log.error("userSettingBo({})不存在", userId);
            return null;
        } else if (pushTypes.contains(CommonConstant.ZERO) && StringUtils.isEmpty(userSettingBo.getPushToken())) {
            log.error("推送方式包含墓碑,但是user({})的push_token为空", userId);
            return null;
        }
        // 推送消息开关(iot-app user_setting表中的system_message_switch字段)
        if (Objects.isNull(userSettingBo.getSystemMessageSwitch()) || !userSettingBo.getSystemMessageSwitch().equals(CommonConstant.ONE)) {
            log.warn("用户({})系统消息开关是关闭状态", userId);
            return null;
        }
        // 消息标题,内容
        if (StringUtils.isEmpty(userSettingBo.getLanguage())) {
            log.warn("用户({})默认语言为空", userSettingBo.getUserId());
            return null;
        }
        return userSettingBo;
    }

    @Override
    public List<FeedbackExcel> export(FeedbackExportDto req) {
        String userId = getUserId(req);
        List<Feedback> list = feedbackService.list(new LambdaQueryWrapper<Feedback>()
                .like(StringUtils.isNotBlank(req.getFeedbackId()), Feedback::getId, req.getFeedbackId())
                .ge(StringUtils.isNotBlank(req.getFeedbackStartTime()), Feedback::getCreateTime, req.getFeedbackStartTime())
                .le(StringUtils.isNotBlank(req.getFeedbackEndTime()), Feedback::getCreateTime, req.getFeedbackEndTime())
                .ge(StringUtils.isNotBlank(req.getReplyStartTime()), Feedback::getReplyTime, req.getReplyStartTime())
                .le(StringUtils.isNotBlank(req.getReplyEndTime()), Feedback::getReplyTime, req.getReplyEndTime())
                .eq(StringUtils.isNotBlank(req.getFeedbackCategory()), Feedback::getCategory1, req.getFeedbackCategory())
                .eq(StringUtils.isNotBlank(req.getReplyState()), Feedback::getReplyState, req.getReplyState())
                .eq(StringUtils.isNotBlank(req.getFeedbackContent()), Feedback::getContent, req.getFeedbackContent())
                .like(StringUtils.isNotBlank(req.getReplyContent()), Feedback::getReplyContent, req.getReplyContent())
                .like(StringUtils.isNotBlank(userId), Feedback::getUserId, userId)
                .like(StringUtils.isNotBlank(req.getCommitPhoneModel()), Feedback::getCommitPhoneModel, req.getCommitPhoneModel())
                .like(StringUtils.isNotBlank(req.getCommitPhoneOsVersion()), Feedback::getCommitPhoneOsVersion, req.getCommitPhoneOsVersion())
                .like(StringUtils.isNotBlank(req.getCommitPhoneType()), Feedback::getCommitPhoneType, req.getCommitPhoneType())
                .like(StringUtils.isNotBlank(req.getCommitAppVersion()), Feedback::getCommitAppVersion, req.getCommitAppVersion())
                .like(StringUtils.isNotBlank(req.getCommodityModel()), Feedback::getCategory2, req.getCommodityModel())
                .eq(StringUtils.isNotBlank(req.getFeedbackMoreCategory()), Feedback::getCategory2, req.getFeedbackMoreCategory())
                .like(StringUtils.isNotBlank(req.getDeviceSn()), Feedback::getDeviceSn, req.getDeviceSn())
                .like(StringUtils.isNotBlank(req.getRnVersion()), Feedback::getRnVersion, req.getRnVersion())
                .eq(StringUtils.isNotBlank(req.getAreaCode()), Feedback::getAreaCode, req.getAreaCode())
                .like(StringUtils.isNotBlank(req.getPhone()), Feedback::getPhone, req.getPhone())
                .like(StringUtils.isNotBlank(req.getCsRemark()), Feedback::getCsRemark, req.getCsRemark())
                .orderByDesc(Feedback::getCreateTime)
        );
        List<FeedbackExcel> res = new ArrayList<>();
        if (list.isEmpty()) {
            return res;
        }
        // 获取用户ID和邮箱映射
        List<Long> userIds = list.stream().map(Feedback::getUserId).collect(Collectors.toList());
        Map<Long, String> userEmailMap = new HashMap<>();
        if (StringUtils.isNotBlank(req.getEmail()))  {
            userEmailMap.put(Long.valueOf(userId), req.getEmail());
        } else {
            userEmailMap.putAll(remoteAppUserService.listUserEmailByUserIds(userIds)
                    .stream()
                    .collect(Collectors.toMap(UserVo::getId, UserVo::getEmail)));
        }
        // 获取反馈回复信息
        List<Long> feedbackIds = list.stream().map(Feedback::getId).collect(Collectors.toList());
        List<FeedbackReply> replies = feedbackReplyService.list(new LambdaUpdateWrapper<FeedbackReply>()
                .in(FeedbackReply::getFeedbackId, feedbackIds));
        Map<String, FeedbackReply> map = replies.stream().collect(Collectors.toMap(e -> String.valueOf(e.getId()), Function.identity(), (e1, e2) -> e2));
        Map<Long, List<FeedbackReply>> group = replies.stream().collect(Collectors.groupingBy(FeedbackReply::getFeedbackId));
        // 字典多语言映射
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(),
                Arrays.asList(FeedbackConstant.COMMIT_PHONE_TYPE, FeedbackConstant.FEEDBACK_CATEGORY,
                        FeedbackConstant.FEEDBACK_MORE_CATEGORY, FeedbackConstant.FEEDBACK_REPLY_STATE,FeedbackConstant.FEEDBACK_REPLAY_TYPE));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        // 遍历反馈列表，组装反馈excel对象
        list.forEach(feedback -> {
            List<FeedbackReply> replyList = group.get(feedback.getId());
            if (replyList == null) {
                // 处理无回复的反馈
                res.add(buildBasicFeedbackExcel(feedback, collect, userEmailMap, req));
            } else {
                // 处理有回复的反馈
                processReplies(feedback, replyList, map, collect, userEmailMap, req, res);
            }
        });

        return res;
    }

    private FeedbackExcel buildBasicFeedbackExcel(Feedback feedback, Map<String, DictBo> collect,
                                                  Map<Long, String> userEmailMap, FeedbackExportDto req) {
        FeedbackExcel excel = new FeedbackExcel();
        BeanUtils.copyProperties(feedback, excel);
        setBasicInfo(excel, feedback, collect, userEmailMap);
        setPhoneAndContent(excel, feedback, req);
        return excel;
    }

    private void processReplies(Feedback feedback, List<FeedbackReply> replyList,
                                Map<String, FeedbackReply> replyMap, Map<String, DictBo> collect,
                                Map<Long, String> userEmailMap, FeedbackExportDto req, List<FeedbackExcel> res) {

        replyList.stream()
                .map(i -> StringUtils.isBlank(i.getReplyPath()) ?
                        i.getId() + "" : i.getReplyPath() + "," + i.getId())
                .sorted(String::compareTo)
                .forEach(replyPath -> {
                    FeedbackExcel excel = buildBasicFeedbackExcel(feedback, collect, userEmailMap, req);

                    String[] split = replyPath.split(",");
                    String last = split[split.length - 1];
                    FeedbackReply reply = replyMap.get(last);

                    if (reply != null) {
                        setReplyInfo(excel, reply, collect, req);
                        res.add(excel);
                    }
                });
    }

    private void setBasicInfo(FeedbackExcel excel, Feedback feedback,
                              Map<String, DictBo> collect, Map<Long, String> userEmailMap) {
        excel.setFeedbackId(feedback.getId());
        excel.setEmail(userEmailMap.get(feedback.getUserId()));

        // 设置手机类型
        excel.setCommitPhoneType(collect.get(FeedbackConstant.COMMIT_PHONE_TYPE).getNodes()
                .stream()
                .filter(i -> StringUtils.equals(i.getLabel(), feedback.getCommitPhoneType()))
                .findFirst()
                .orElse(new DictNodeBo())
                .getDescription());

        // 设置反馈分类
        excel.setFeedbackCategory(collect.get(FeedbackConstant.FEEDBACK_CATEGORY).getNodes()
                .stream()
                .filter(i -> StringUtils.equals(i.getLabel(), feedback.getCategory1()))
                .findFirst()
                .orElse(new DictNodeBo())
                .getDescription());

        // 设置分类名称
        setCategoryName(excel, feedback, collect);

        // 设置回复状态
        excel.setReplyState(collect.get(FeedbackConstant.FEEDBACK_REPLY_STATE).getNodes()
                .stream()
                .filter(i -> StringUtils.equals(i.getLabel(), feedback.getReplyState()))
                .findFirst()
                .orElse(new DictNodeBo())
                .getDescription());
    }

    private void setCategoryName(FeedbackExcel excel, Feedback feedback, Map<String, DictBo> collect) {
        if (String.valueOf(FeedBackEnum.DEVICE.getValue()).equals(feedback.getCategory1())) {
            excel.setCategoryName(feedback.getCommodityModel());
        } else if (String.valueOf(FeedBackEnum.MORE.getValue()).equals(feedback.getCategory1())) {
            excel.setCategoryName(collect.get(FeedbackConstant.FEEDBACK_MORE_CATEGORY).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), feedback.getCategory2()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
        } else {
            excel.setCategoryName(feedback.getCategory1());
        }
    }

    private void setPhoneAndContent(FeedbackExcel excel, Feedback feedback, FeedbackExportDto req) {
        excel.setAreaCodeAndPhone(Optional.ofNullable(feedback.getAreaCode()).orElse("")
                + Optional.ofNullable(feedback.getPhone()).orElse(""));
        excel.setFeedbackContent(feedback.getContent());
        if (StringUtils.isNotBlank(feedback.getPictures())) {
            excel.setFeedbackPictures(Arrays.asList(feedback.getPictures().split(COMMON_SPLIT)));
        }
        excel.setFeedbackTime(DateTimeZoneUtil.format(feedback.getCreateTime(), req.getZone()));
    }

    private void setReplyInfo(FeedbackExcel excel, FeedbackReply reply,
                              Map<String, DictBo> collect, FeedbackExportDto req) {
        excel.setReplyType(collect.get(FeedbackConstant.FEEDBACK_REPLAY_TYPE).getNodes()
                .stream()
                .filter(temp -> StringUtils.equals(temp.getLabel(), reply.getType()))
                .findFirst()
                .orElse(new DictNodeBo())
                .getDescription());

        excel.setReplyContent(reply.getContent());
        if (StringUtils.isNotBlank(reply.getPictures())) {
            excel.setReplyPictures(Arrays.asList(reply.getPictures().split(COMMON_SPLIT)));
        }
        excel.setReplyTime(DateTimeZoneUtil.format(reply.getCreateTime(), req.getZone()));
    }

}
