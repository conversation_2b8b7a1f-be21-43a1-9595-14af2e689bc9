<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.feedback.mapper.FeedbackMapper">

    <select id="selectDeviceFeedback" resultType="java.lang.Long">
        select feedback_id from (
        select a.id as `feedback_id`,
               a.device_id,
               a.user_id,
               a.category2,
               a.create_time as `feedback_create_time`,
               a.update_time as `feedback_update_time`,
               b.id as `reply_id`
        from
            (select * from iot_feedback.feedback f
                      where f.is_deleted = 0 and f.reply_state in ('toBeReplied', 'replied') and f.category1 = 'device'
                        and f.device_id =#{deviceId} and f.user_id=#{userId}) a
            left join
            (select * from iot_feedback.feedback_reply fr where fr.is_deleted = 0) b
            on a.id = b.feedback_id order by feedback_update_time desc limit 1) t;
    </select>

    <select id="selectAppFeedback" resultType="java.lang.Long">
        select feedback_id from (
          select a.id as `feedback_id`,
                 a.device_id,
                 a.user_id,
                 a.category2,
                 a.create_time as `feedback_create_time`,
                 a.update_time as `feedback_update_time`,
                 b.id as `reply_id`
          from
              (select * from iot_feedback.feedback f
                        where f.is_deleted =0 and f.reply_state in ('toBeReplied', 'replied')
                          and f.category2 = 'app' and f.user_id=#{userId}) a
              left join
              (select * from iot_feedback.feedback_reply fr where fr.is_deleted = 0) b
              on a.id = b.feedback_id order by feedback_update_time desc limit 1) t;
    </select>
</mapper>
