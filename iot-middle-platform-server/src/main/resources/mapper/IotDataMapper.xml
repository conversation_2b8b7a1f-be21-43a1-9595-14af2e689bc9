<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.iot.middle.mapper.IotDataMapper">

    <select id="getLastUpdateLog" resultType="java.lang.String">
        select payload from sub_product_${productKey}_${deviceId}
        <where> idf_${identifier} is not null
            <if test='isReported != null'>
                and reported = #{isReported}
            </if>
        </where>
        ORDER BY ts desc limit 1
    </select>

    <select id="listDeviceShadowLog" resultType="java.util.HashMap">
        select * from sub_product_${productKey}_${deviceId} order by ts desc limit #{offset}, #{size}
    </select>

    <select id="countDeviceShadowLog" resultType="java.lang.Long">
        SELECT COUNT(*) FROM sub_product_${productKey}_${deviceId}
    </select>

    <select id="getLatestLog" resultType="com.chervon.iot.middle.domain.dto.DeviceLogDto">
        select ts as logTime, payload as log, reported as type, succeed as status from sub_product_${productKey}_${deviceId} order by ts desc limit 1;
    </select>

    <select id="getLatestColumnLog" resultType="map">
        select ${columns} from sub_product_${productKey}_${deviceId}
        <where>
            <if test="condition!=null">
                ${condition}
            </if>
        </where>
       order by ts desc limit 1;
    </select>

    <select id="getLatestDataBy1024" resultType="map">
        select ${columns} from sub_product_${productKey}_${deviceId}
        <where>
            <if test="condition!=null">
                ${condition}
            </if>
        </where>
        order by idf_1024 desc limit 1;
    </select>

    <select id="listDeviceLogs" resultType="com.chervon.iot.middle.domain.dto.DeviceLogDto">
        select ts as logTime, payload as log, reported as type, succeed as status from sub_product_${productKey}_${logPageDto.deviceId}
        <where>
            <if test="logPageDto.startTime != null">
                ts <![CDATA[>=]]> #{logPageDto.startTime}
            </if>
            <if test="logPageDto.endTime != null">
                and ts <![CDATA[<=]]> #{logPageDto.endTime}
            </if>
            <if test='logPageDto.type != null'>
                and reported = #{logPageDto.type}
            </if>
            <if test='logPageDto.status != null'>
                and succeed = #{logPageDto.status}
            </if>
        </where>
        order by ts desc
        limit #{offset}, #{pageSize};
    </select>

    <select id="countDeviceTopology" resultType="java.lang.Long">
        SELECT COUNT(*) FROM sub_product_${productKey}_${deviceId}
        <where>
            route_value is not null
            <if test='startTime != null'>
                and ts <![CDATA[>=]]> #{startTime}
            </if>
            <if test='endTime != null'>
                and ts <![CDATA[<=]]> #{endTime}
            </if>
            <if test='type != null and type == 0'>
                and route_up is not null
                <if test="topologyDeviceId != null and  topologyDeviceId != ''">
                    and route_up like #{topologyDeviceId}
                </if>
            </if>
            <if test='type != null and type == 1'>
                and route_down is not null
                <if test="topologyDeviceId != null and  topologyDeviceId != ''">
                    and route_down like #{topologyDeviceId}
                </if>
            </if>
        </where>
    </select>
    <select id="listDeviceTopology"
      resultType="com.chervon.iot.middle.api.vo.device.DeviceTopologyVo">
        SELECT
        <if test='type != null and type == 0'>
            route_up as deviceId, up_type as communicationType,
        </if>
        <if test='type != null and type == 1'>
            route_down as deviceId, down_type as communicationType,
        </if>
        ts as lastCommunicationTime
        FROM sub_product_${productKey}_${deviceId}
        <where>
            route_value is not null
            <if test='startTime != null'>
                and ts <![CDATA[>=]]> #{startTime}
            </if>
            <if test='endTime != null'>
                and ts <![CDATA[<=]]> #{endTime}
            </if>
            <if test='type != null and type == 0'>
                and route_up is not null
                <if test="topologyDeviceId != null and  topologyDeviceId != ''">
                    and route_up like #{topologyDeviceId}
                </if>
            </if>
            <if test='type != null and type == 1'>
                and route_down is not null
                <if test="topologyDeviceId != null and  topologyDeviceId != ''">
                    and route_down like #{topologyDeviceId}
                </if>
            </if>
        </where>
        order by ts desc
        limit #{offset}, #{size}
    </select>

    <select id="pageDeviceLogs" resultType="com.chervon.iot.middle.domain.dto.DeviceLogDto">
        select ts as logTime, payload as log, reported as type, succeed as status from sub_product_${productKey}_${logPageDto.deviceId}
        <where>
            <if test='logPageDto.startTime != null'>
                ts <![CDATA[>=]]> #{logPageDto.startTime}
            </if>
            <if test='logPageDto.endTime != null'>
                and ts <![CDATA[<=]]> #{logPageDto.endTime}
            </if>
            <if test='logPageDto.type != null'>
                and reported = #{logPageDto.type}
            </if>
            <if test='logPageDto.status != null'>
                and succeed = #{logPageDto.status}
            </if>
        </where>
        order by ts desc
        limit #{offset}, #{pageSize}
    </select>

    <select id="getDeviceLogs" resultType="com.chervon.iot.middle.domain.dto.DeviceLogDto">
        select ts as logTime, payload as log, reported as type, succeed as status from sub_product_${productKey}_${logPageDto.deviceId}
        <where>
            <if test='logPageDto.startTime != null'>
                ts <![CDATA[>=]]> #{logPageDto.startTime}
            </if>
            <if test='logPageDto.endTime != null'>
                and ts <![CDATA[<=]]> #{logPageDto.endTime}
            </if>
            <if test='logPageDto.type != null'>
                and reported = #{logPageDto.type}
            </if>
            <if test='logPageDto.status != null'>
                and succeed = #{logPageDto.status}
            </if>
        </where>
        order by ts desc
    </select>


    <select id="getDeviceLogList" resultType="com.chervon.iot.middle.domain.dto.DeviceLogDto">
        select ts as logTime, payload as log, reported as type, succeed as status from sub_product_${productKey}_${deviceLogParamDto.deviceId}
        <where>
            <if test='deviceLogParamDto.startTime != null'>
                ts <![CDATA[>=]]> #{deviceLogParamDto.startTime}
            </if>
        </where>
        order by ts desc
    </select>

    <select id="countDeviceLogs" resultType="java.lang.Long">
        select count(0) from sub_product_${productKey}_${logPageDto.deviceId}
        <where>
            <if test='logPageDto.startTime != null'>
                ts <![CDATA[>=]]> #{logPageDto.startTime}
            </if>
            <if test='logPageDto.endTime != null'>
                and ts <![CDATA[<=]]> #{logPageDto.endTime}
            </if>
            <if test='logPageDto.type != null'>
                and reported = #{logPageDto.type}
            </if>
            <if test='logPageDto.status != null'>
                and succeed = #{logPageDto.status}
            </if>
        </where>
    </select>

    <select id="getMountedBattery" resultType="java.lang.String">
        select idf_2019 from sub_product_${productKey}_${deviceId} WHERE idf_2019 != '{1:1,2:null}' order by ts desc limit 1;
    </select>
</mapper>
