<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.iot.middle.mapper.IotUsageMapper">

    <select id="getUsageData" resultType="java.util.Map">
        select device_id as `deviceId`, ${query.sql}
        from sub_product_${productKey}_${query.deviceId}
        where idf_1024 <![CDATA[>=]]> '${query.timeStart}'
        and idf_1024 <![CDATA[<]]> '${query.timeEnd}'
        <if test="query.where != null and query.where != ''">
            and ${query.where}
        </if>
        group by device_id
    </select>

    <select id="getTrackHistory" resultType="com.chervon.iot.middle.domain.pojo.CoordinateHistory">
        select cast(idf_1024 as BIGINT) as ts1024, ${query.sql}
        from sub_product_${productKey}_${query.deviceId}
        where idf_1024 <![CDATA[>=]]> '${query.timeStart}'
        and idf_1024 <![CDATA[<]]> '${query.timeEnd}'
        <if test="query.where != null and query.where != ''">
            and ${query.where}
        </if>
        order by idf_1024
    </select>

    <select id="getBeforeHistory" resultType="java.lang.Long">
        select count(1) from sub_product_${productKey}_${query.deviceId}
    </select>

    <select id="getDayHistory" resultType="com.chervon.iot.middle.api.pojo.usage.UsageKeyValue">
        select timetruncate(cast(idf_1024 as BIGINT), 1d) dataKey, ${query.sql}
        from sub_product_${productKey}_${query.deviceId}
        where idf_1024 <![CDATA[>=]]> '${query.timeStart}'
        and idf_1024 <![CDATA[<]]> '${query.timeEnd}'
        <if test="query.where != null and query.where != ''">
            and ${query.where}
        </if>
        group by timetruncate(cast(idf_1024 as BIGINT), 1d)
    </select>

    <select id="getWMHistory" resultType="java.lang.Long">
        select ${query.sql} from sub_product_${productKey}_${query.deviceId}
        where idf_1024 <![CDATA[>=]]> '${query.timeStart}'
        and idf_1024 <![CDATA[<]]> '${query.timeEnd}'
        <if test="query.where != null and query.where != ''">
            and ${query.where}
        </if>
    </select>

    <select id="getTotalHistory" resultType="java.lang.Long">
        select ${query.sql} from sub_product_${productKey}_${query.deviceId}
        <where>
            <if test="query.where != null and query.where != ''">
                ${query.where}
            </if>
        </where>
        order by ts desc limit 1
    </select>

    <select id="getLastSyncedTime" resultType="java.lang.Long">
        select cast(idf_1024 as BIGINT) from sub_product_${productKey}_${deviceId} order by ts desc limit 1
    </select>

</mapper>
