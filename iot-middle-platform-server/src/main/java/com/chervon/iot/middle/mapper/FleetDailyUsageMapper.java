package com.chervon.iot.middle.mapper;

import com.chervon.iot.middle.api.dto.query.FleetUsageQuery;
import com.chervon.iot.middle.api.vo.usage.FleetBatteryDailyUsageVo;
import com.chervon.iot.middle.api.vo.usage.FleetChargerDailyUsageVo;
import com.chervon.iot.middle.api.vo.usage.FleetToolDailyUsageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * fleet设备日使用数据统计
 */
@Mapper
public interface FleetDailyUsageMapper {

    /**
     * 查询FLEET工具设备日使用数据汇总
     **/
    FleetToolDailyUsageVo getToolDayUsage(@Param("productKey") String productKey, @Param("query") FleetUsageQuery query);

    /**
     * 查询FLEET充电器设备日使用数据汇总
     * @param query
     */
    FleetChargerDailyUsageVo getChargerHubDayUsage(@Param("productKey") String productKey, @Param("query") FleetUsageQuery query);
    /**
     * 查询FLEET充电器设备日使用数据汇总
     * @param query
     */
    FleetChargerDailyUsageVo getChargerBankDayUsage(@Param("productKey") String productKey, @Param("query") FleetUsageQuery query);
    /**
     * 查询FLEET电池包设备日使用数据汇总
     */
    FleetBatteryDailyUsageVo getBatteryDayUsage(@Param("productKey") String productKey, @Param("query") FleetUsageQuery query);

}
