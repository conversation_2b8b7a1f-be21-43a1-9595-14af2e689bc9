package com.chervon.iot.middle.domain.dto;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @className DeviceLogVo
 * @description
 * @date 2022/7/11
 */
@Data
public class DeviceLogDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志类型 0下发 1上报 2未知
     **/
    private Integer type;

    /**
     * 日志类型 0失败 1成功
     **/
    private Integer status;

    /**
     * 日志内容
     **/
    private String log;

    /**
     * 日志时间
     **/
    private Date logTime;
}
