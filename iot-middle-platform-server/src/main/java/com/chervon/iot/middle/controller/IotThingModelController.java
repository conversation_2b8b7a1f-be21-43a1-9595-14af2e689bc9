package com.chervon.iot.middle.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.iot.middle.api.pojo.thingmodel.Event;
import com.chervon.iot.middle.api.pojo.thingmodel.IotThingModel;
import com.chervon.iot.middle.api.service.RemoteThingModelService;
import com.chervon.iot.middle.domain.pojo.IotThingModelIdentifier;
import com.chervon.iot.middle.service.IotThingModelIdentifierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 设备证书测试接口
 * @date 2024/1/11 9:34
 */
@RestController
@RequestMapping("/thingModelRule")
@Slf4j
@Api(tags = "物模型规则测试相关接口")
public class IotThingModelController {

    @Autowired
    private IotThingModelIdentifierService iotThingModelIdentifierService;


    @Autowired
    private RemoteThingModelService remoteThingModelService;

    @GetMapping("/getIotThingModel")
    @ApiOperation(value = "查询物模型")
    public IotThingModel getIotThingModel(@RequestParam(required = false) String productKey) {
        final IotThingModel thingModel = iotThingModelIdentifierService.getProductThingModelAll(productKey);
        return thingModel;
    }
    @GetMapping("/query")
    @ApiOperation(value = "查询物模型")
    public Object query() {
        Event event = remoteThingModelService.getSimpleEvent("1745006336321839105", "44005");
        log.info("获取事件信息：{}",event);
        return event;
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除物模型")
    public void delete() {
        LambdaUpdateWrapper<IotThingModelIdentifier> updateWrapper=new LambdaUpdateWrapper<IotThingModelIdentifier>();
        updateWrapper.eq(IotThingModelIdentifier::getId,"110");
        updateWrapper.last("AND JSON_EXTRACT(properties, '$[0]') IS NOT NULL");
        updateWrapper.setSql("properties=JSON_REMOVE(properties, '$[0]')");
        iotThingModelIdentifierService.update(updateWrapper);
    }

}
