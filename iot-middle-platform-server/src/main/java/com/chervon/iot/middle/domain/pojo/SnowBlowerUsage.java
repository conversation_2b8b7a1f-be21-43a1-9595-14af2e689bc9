package com.chervon.iot.middle.domain.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 扫雪机上次设备使用情况记录
 * product_1717837495206232066
 */
@Data
@ApiModel(value = "设备使用数据", description = "扫雪机上次设备使用情况记录")
public class SnowBlowerUsage implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("上报的时间戳：毫秒")
    private Long ts;

    @ApiModelProperty("1016:设备使用时长：：秒")
    private String usageTime;

    @ApiModelProperty("1032:设备开始工作时间戳：秒")
    private String workingTimeStart;

    @ApiModelProperty("1033：设备停止工作时间戳：秒")
    private String workingTimeEnd;

    @ApiModelProperty("1034：当前工作的总耗电量")
    private String powerConsumed;
    /**
     * 原始数据：{"4004":[{"ts_ms":1700637480000,"load":80},{"ts_ms":1700637480200,"load":70},{"ts_ms":1700637480400,"load":60}]}
     * 返回数组数据：[56,73,87,34,87,24,78,97,24,75,86,98,23,54,56,67,23,76,82,37,86,23,65,78,87,24,34,2,8,58]
     */
    @ApiModelProperty("4004：工作期间的负载数据(取工作时间段内50个采样平均值)")
    private String workLoad;
}
