package com.chervon.iot.middle.domain.constant;

/**
 * 数据常量
 * <AUTHOR>
 * @date 14:50 2022/3/7
 **/
public interface DataConstant {

    /**
     *  路由的key
     */
    String STABLE_ROUTE = "route";

    /**
     *  负载曲线的key
     */
    String LOAD_CURVE = "4004";

    /**
     *  负载曲线开始时间的key
     */
    String LOAD_CURVE_START_TIME = "load_curve_start_time";

    /**
     * 路由分隔符
     */
    String STABLE_ROUTE_SPLIT = "/";

    /**
     * 路由设备与通信方式分隔符
     */
    String STABLE_ROUTE_TYPE_SPLIT = "\\.";

    /**
     * 设备工况（APP及数据分析使用）采集超级表前缀
     */
    String STABLE_PRODUCT_LOG = "product_";

    /**
     * 设备工况（APP及数据分析使用）采集子表名前缀
     */
    String STABLE_SUB_PRODUCT_LOG = "sub_product_";

    /**
     * 列名称前缀
     *
     */
    String STABLE_IDENTIFIER = "idf_";

    /**
     * 设备扩展物模型数据（主机研发分析自用）采集超级表前缀
     */
    String STABLE_PRODUCT_LOG_EXP = "product_exp_";

    /**
     * 设备扩展物模型数据（主机研发分析自用）采集子表前缀
     */
    String SUB_TABLE_PRODUCT_LOG_EXP = "sub_product_exp_";

    /**
     * 设备工况数据（APP及数据分析使用）
     */
    String STATUS_DATA_TYPE = "1";

    /**
     * 设备扩展物模型数据（主机研发分析自用）
     */
    String STATUS_DATA_TYPE_EXP = "2";

    /**
     * reported属性
     */
    String REPORTED = "reported";

    /**
     * desired属性
     */
    String DESIRED = "desired";

    /**
     * state属性
     */
    String STATE = "state";

    /**
     * timestamp属性
     */
    String TIMESTAMP = "timestamp";

    /**
     * 1024 属性
     */
    String TIMESTAMP1024 = "1024";

    /**
     * 批量接收topic后缀
     */
    String DEVICE_ID = "deviceId";

    /**
     * 影子topic后缀
     */
    String SHADOW_UPDATE_TOPIC_SUFFIX = "/shadow/update/accepted";

    /**
     * 批量扩展物模型接收topic后缀
     */
    String EXP_BATCH_DATA_TOPIC = "/exp/data/batch";

    /**
     * 批量历史工况接收topic后缀
     */
    String SHADOW_BATCH_DATA_TOPIC = "/shadow/data/batch";

    /**
     * 拒绝接收topic后缀
     */
    String REJECTED_TOPIC = "/shadow/update/rejected";

    /**
     * 设备工况筛选标识
     */
    String LOG_MARKER = "sift";


    /**
     * 主机研发自用设备日志筛选标识
     */
    String LOG_MARKER_EXP = "sift_exp";

    /**
     * 时间format格式
     */
    String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * keyList
     */
    String KEY_LIST = "keyList";

    /**
     * valueList
     */
    String VALUE_LIST = "valueList";

}
