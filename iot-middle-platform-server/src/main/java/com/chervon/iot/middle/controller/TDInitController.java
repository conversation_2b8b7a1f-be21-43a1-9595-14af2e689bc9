package com.chervon.iot.middle.controller;

import com.chervon.iot.middle.api.pojo.thingmodel.IotThingModel;
import com.chervon.iot.middle.service.IotDataService;
import com.chervon.iot.middle.service.IotThingModelIdentifierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/td")
@Slf4j
@Api(tags = "TDengine表初始化接口")
public class TDInitController {

    @Autowired
    private IotThingModelIdentifierService iotThingModelIdentifierService;

    @Autowired
    private IotDataService iotDataService;

    @PostMapping("/createSTable")
    @ApiOperation(value = "创建表数据")
    public String createSTable(@RequestBody List<String> productKeys) {
        if (!ObjectUtils.isEmpty(productKeys)) {
            for(String pk : productKeys){
                IotThingModel iotThingModel = iotThingModelIdentifierService.getProductThingModelAll(pk);
                if (iotThingModel != null) {
                    iotDataService.createSTable(iotThingModel);
                }
            }
        } else {
            log.error("productKey list should not null!");
            return "failed";
        }
        return "success";

    }



}
