package com.chervon.iot.middle.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.iot.middle.domain.pojo.DeviceCertificate;
import org.apache.ibatis.annotations.Mapper;

/**
 * (t_device_certificate)数据Mapper
 *
 * <AUTHOR>
 * @since 2024-01-09 10:50:24
 * @description 设备证书关系
*/
@Mapper
@DS("second")
public interface DeviceCertificateMapper extends BaseMapper<DeviceCertificate> {

}
