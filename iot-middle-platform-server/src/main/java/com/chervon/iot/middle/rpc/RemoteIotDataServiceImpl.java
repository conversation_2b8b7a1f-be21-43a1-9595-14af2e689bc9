package com.chervon.iot.middle.rpc;

import com.chervon.iot.middle.api.service.RemoteIotDataService;
import com.chervon.iot.middle.domain.constant.DataConstant;
import com.chervon.iot.middle.service.IotDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/23 15:41
 */
@Service
@Slf4j
@DubboService
public class RemoteIotDataServiceImpl implements RemoteIotDataService {

    @Autowired
    private IotDataService iotDataService;


    @Override
    public void saveReportedLog(String deviceId, Object reported) {
        iotDataService.saveReportedLog(DataConstant.STATUS_DATA_TYPE, deviceId, reported);
    }

    /**
     * 获取指定列最后一条记录
     * @param productKey
     * @param deviceId
     * @param columns
     * @return
     */
    @Override
    public Map<String,Object> getLatestColumnLog(String productKey, String deviceId, String columns, String condition){
        return iotDataService.getLatestDataByTs(productKey,deviceId,columns,condition);
    }

    /**
     * 获取指定时间范围内最后一条数据值（order by idf_1024 desc)
     * @param productKey 产品id
     * @param deviceId 设备id
     * @param columns 指定查询的列值
     * @param condition 过滤条件
     * @return 指定列的最后一条数据值
     */
    @Override
    public Map<String,Object> getLatestDataBy1024(String productKey, String deviceId, String columns, String condition){
        return iotDataService.getLatestDataBy1024(productKey,deviceId,columns,condition);
    }

    /**
     * 获取挂载的电池包接口
     *
     * @param productKey
     * @param deviceId
     * @return String 电池包设备id
     */
    @Override
    public String getMountedBattery(String productKey, String deviceId) {
        final String batteryDeviceId = iotDataService.getMountedBattery(productKey, deviceId);
        return batteryDeviceId;
    }

}
