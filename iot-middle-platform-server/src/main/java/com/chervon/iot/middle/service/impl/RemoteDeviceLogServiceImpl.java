package com.chervon.iot.middle.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.middle.api.dto.log.DeviceLogParamDto;
import com.chervon.iot.middle.api.dto.log.LogPageDto;
import com.chervon.iot.middle.api.dto.log.TopologyPageDto;
import com.chervon.iot.middle.api.enums.ThingLogStatus;
import com.chervon.iot.middle.api.enums.ThingLogTransferType;
import com.chervon.iot.middle.api.exception.IotMiddleErrorCode;
import com.chervon.iot.middle.api.service.RemoteDeviceLogService;
import com.chervon.iot.middle.api.vo.device.DeviceTopologyVo;
import com.chervon.iot.middle.api.vo.log.DeviceLogVo;
import com.chervon.iot.middle.api.vo.log.DeviceRunParamVo;
import com.chervon.iot.middle.domain.dto.DeviceLogDto;
import com.chervon.iot.middle.service.AwsIotService;
import com.chervon.iot.middle.service.IotDataService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className RemoteDeviceLogServiceImpl
 * @description
 * @date 2022/7/20 15:08
 */
@Slf4j
@Service
@DubboService
public class RemoteDeviceLogServiceImpl implements RemoteDeviceLogService {

    @Autowired
    private IotDataService iotDataService;

    @Autowired
    private AwsIotService awsIotService;


    @Override
    public Object getLastUpdateLog(String deviceId, String identifier, Boolean isReported) {
        if (!iotDataService.existedTable(deviceId)) {
            log.warn(String.format(IotMiddleErrorCode.TD_PRODUCT_LOG_TABLE_NOT_EXISTS.getErrorMessage(), deviceId));
            return new JSONObject();
        }
        try {
            if (isReported == null) {
                isReported = true;
            }
            String productKey = awsIotService.getProductKeyRedis(deviceId);
            String log = iotDataService.getLastUpdateLog(productKey, deviceId, identifier, isReported ? 1 : 0);
            if (StringUtils.isEmpty(log)) {
                return new JSONObject();
            }
            return JSONUtil.parseObj(log);
        } catch (Exception exception) {
            return new JSONObject();
        }

    }

    @Override
    public Map<String, Integer> getLastLogByIdentifier(DeviceRunParamVo deviceRunParamVo, List<String> identifierList) {
        //根据类型获取对应的id
        String productKey = awsIotService.getProductKeyRedis(deviceRunParamVo.getDeviceId());
        Map<String, Integer> result = Maps.newHashMap();
        return result;

    }

    @Override
    public Map<String, Integer> getMaxByIdentifier(DeviceRunParamVo deviceRunParamVo, String identifier) {
        //根据类型获取对应的id
        String productKey = awsIotService.getProductKeyRedis(deviceRunParamVo.getDeviceId());
        Map<String, Integer> result = Maps.newHashMap();
        return result;
    }

    @Override
    public PageResult<DeviceLogVo> listDeviceLogs(LogPageDto logPageDto) {
        PageResult pageResult = new PageResult(logPageDto.getPageNum(), logPageDto.getPageSize());
        if (!iotDataService.existedTable(logPageDto.getDeviceId())) {
            log.warn(String.format(IotMiddleErrorCode.TD_PRODUCT_LOG_TABLE_NOT_EXISTS.getErrorMessage(), logPageDto.getDeviceId()));
            return pageResult;
        }
        if (logPageDto.getLogTransferType() != null && ThingLogTransferType.valueOf(logPageDto.getLogTransferType()) != null) {
            logPageDto.setType(ThingLogTransferType.valueOf(logPageDto.getLogTransferType()).getStatus());
        }
        if (logPageDto.getLogStatus() != null && ThingLogStatus.valueOf(logPageDto.getLogStatus()) != null) {
            logPageDto.setStatus(ThingLogStatus.valueOf(logPageDto.getLogStatus()).getStatus());
        }
        String productKey = awsIotService.getProductKeyRedis(logPageDto.getDeviceId());
        Long total = iotDataService.countDeviceLogs(productKey, logPageDto);
        if (total == null || total == 0) {
            return pageResult;
        }
        List<DeviceLogDto> deviceLogDtos = iotDataService.listDeviceLogs(productKey, logPageDto,
                (logPageDto.getPageNum() - 1) * logPageDto.getPageSize(), logPageDto.getPageSize());
        pageResult.setTotal(total);
        List<DeviceLogVo> deviceLogs = new ArrayList<>();
        for (DeviceLogDto deviceLog : deviceLogDtos) {
            DeviceLogVo convert = ConvertUtil.convert(deviceLog, DeviceLogVo.class);
            convert.setType(deviceLog.getType() != null ? ThingLogTransferType.getType(deviceLog.getType()) : ThingLogTransferType.UNKNOWN);
            convert.setStatus(deviceLog.getStatus() != null ? ThingLogStatus.getStatusEnum(deviceLog.getStatus()) : ThingLogStatus.FAIL);
            deviceLogs.add(convert);
        }
        pageResult.setList(deviceLogs);
        pageResult.setPages((long) Math.ceil((double) total / (double) logPageDto.getPageSize()));
        return pageResult;
    }

    @Override
    public List<DeviceLogVo> getDeviceLogList(DeviceLogParamDto logPageDto) {
        List<DeviceLogVo> deviceLogs = new ArrayList<>();
        if (!iotDataService.existedTable(logPageDto.getDeviceId())) {
            log.warn(String.format(IotMiddleErrorCode.TD_PRODUCT_LOG_TABLE_NOT_EXISTS.getErrorMessage(),logPageDto.getDeviceId()));
            return deviceLogs;
        }
        try {
            if (logPageDto.getStartTime() == null) {
                logPageDto.setStartTime(System.currentTimeMillis());
            }
            String productKey = awsIotService.getProductKeyRedis(logPageDto.getDeviceId());
            List<DeviceLogDto> deviceLogList = iotDataService.getDeviceLogList(productKey, logPageDto);
            for (DeviceLogDto deviceLog : deviceLogList) {
                DeviceLogVo convert = ConvertUtil.convert(deviceLog, DeviceLogVo.class);
                convert.setType(deviceLog.getType() != null ? ThingLogTransferType.getType(deviceLog.getType()) : ThingLogTransferType.UNKNOWN);
                convert.setStatus(deviceLog.getStatus() != null ? ThingLogStatus.getStatusEnum(deviceLog.getStatus()) : ThingLogStatus.FAIL);
                deviceLogs.add(convert);
            }
        } catch (Exception e) {
            log.error("get device log list error:{}", e.getMessage(), e);
        }
        return deviceLogs;
    }

    @Override
    public PageResult<DeviceLogVo> pageDeviceLogs(LogPageDto logPageDto) {
        PageResult pageResult = new PageResult(logPageDto.getPageNum(), logPageDto.getPageSize());
        if (!iotDataService.existedTable(logPageDto.getDeviceId())) {
            log.warn(String.format(IotMiddleErrorCode.TD_PRODUCT_LOG_TABLE_NOT_EXISTS.getErrorMessage(),logPageDto.getDeviceId()));
            return pageResult;
        }
        Integer pageNum = logPageDto.getPageNum();
        Integer pageSize = logPageDto.getPageSize();
        String productKey = awsIotService.getProductKeyRedis(logPageDto.getDeviceId());
        Long total = iotDataService.countDeviceLogs(productKey, logPageDto);
        if (total == null || total == 0) return pageResult;
        List<DeviceLogDto> logDtos = iotDataService.pageDeviceLogs(productKey, (pageNum - 1) * pageSize, pageSize, logPageDto);
        List<DeviceLogVo> deviceLogs = new ArrayList<>();
        for (DeviceLogDto deviceLog : logDtos) {
            DeviceLogVo convert = ConvertUtil.convert(deviceLog, DeviceLogVo.class);
            convert.setType(deviceLog.getType() != null ? ThingLogTransferType.getType(deviceLog.getType()) : ThingLogTransferType.UNKNOWN);
            convert.setStatus(deviceLog.getStatus() != null ? ThingLogStatus.getStatusEnum(deviceLog.getStatus()) : ThingLogStatus.FAIL);
            deviceLogs.add(convert);
        }
        pageResult.setPages((long) Math.ceil((double) total / (double) pageSize));
        pageResult.setTotal(total);
        pageResult.setList(deviceLogs);
        return pageResult;
    }

    @Override
    public List<DeviceLogVo> getDeviceLogList(LogPageDto logPageDto) {
        if (!iotDataService.existedTable(logPageDto.getDeviceId())) {
            log.warn(String.format(IotMiddleErrorCode.TD_PRODUCT_LOG_TABLE_NOT_EXISTS.getErrorMessage(), logPageDto.getDeviceId()));
            return Lists.newArrayList();
        }
        String productKey = awsIotService.getProductKeyRedis(logPageDto.getDeviceId());
        List<DeviceLogDto> logDtos = iotDataService.getDeviceLogs(productKey, logPageDto);
        List<DeviceLogVo> deviceLogs = new ArrayList<>();
        for (DeviceLogDto deviceLog : logDtos) {
            DeviceLogVo convert = ConvertUtil.convert(deviceLog, DeviceLogVo.class);
            convert.setType(deviceLog.getType() != null ? ThingLogTransferType.getType(deviceLog.getType()) : ThingLogTransferType.UNKNOWN);
            convert.setStatus(deviceLog.getStatus() != null ? ThingLogStatus.getStatusEnum(deviceLog.getStatus()) : ThingLogStatus.FAIL);
            deviceLogs.add(convert);
        }
        return deviceLogs;
    }

    @Override
    public PageResult<DeviceTopologyVo> pageDeviceTopology(TopologyPageDto topologyPageDto) {
        PageResult pageResult = new PageResult(topologyPageDto.getPageNum(), topologyPageDto.getPageSize());
        if (!iotDataService.existedTable(topologyPageDto.getDeviceId())) {
            log.warn(String.format(IotMiddleErrorCode.TD_PRODUCT_LOG_TABLE_NOT_EXISTS.getErrorMessage(), topologyPageDto.getDeviceId()));
            return pageResult;
        }
        return iotDataService.pageDeviceTopology(topologyPageDto);
    }
}
