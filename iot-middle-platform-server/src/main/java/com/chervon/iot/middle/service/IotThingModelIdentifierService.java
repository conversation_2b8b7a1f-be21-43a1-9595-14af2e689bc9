package com.chervon.iot.middle.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageRequest;
import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.middle.api.dto.model.EventDto;
import com.chervon.iot.middle.api.dto.model.PropertyDto;
import com.chervon.iot.middle.api.dto.model.ServiceDto;
import com.chervon.iot.middle.api.pojo.thingmodel.*;
import com.chervon.iot.middle.api.vo.product.IotThingModelDto;
import com.chervon.iot.middle.domain.pojo.IotThingModelIdentifier;

import java.util.List;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @since 2024-07-08 13:58:19
 * @description 设备证书关系
 */
public interface IotThingModelIdentifierService extends IService<IotThingModelIdentifier> {

    /**
     * 获取单个物模型详情
     * @param productKey
     * @param modelType
     * @param identifier
     * @return
     */
    IotThingModelIdentifier getIdentifier(String productKey, String modelType, String identifier);

    /**
     * 获取产品全属性物模型值
     * @param productKey 产品key
     * @return
     */
    IotThingModel getProductThingModelAll(String productKey);


    /**
     * 判断是否存在某个物模型id
     *
     * @param productKey
     * @param identifier
     * @return
     */
    boolean existIdentifierId(String productKey, String identifier);

    /**
     * 判断是否存在某个物模型名称
     *
     * @param productKey
     * @param name
     * @return
     */
    boolean existIdentifierName(String productKey, String name);

    /**
     * 添加物模型属性
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param propertyDto:
     * @return: java.lang.Boolean
     **/
    Boolean addProperty(PropertyDto propertyDto);

    /**
     * 获取物模型属性
     * @param productKey 产品ID
     * @param identifier 物模型id
     * @return 属性信息
     */
    Property getProperty(String productKey, String identifier);
    /**
     * 编辑物模型属性
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param propertyDto:
     * @return: java.lang.Boolean
     **/
    Boolean editProperty(PropertyDto propertyDto);

    /**
     * 删除物模型属性
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型属性id
     * @return: java.lang.Boolean
     **/
    Boolean deleteProperty(String productKey, String identifier);

    /**
     * 分页获取功能属性列表
     * <AUTHOR>
     * @date 17:47 2022/7/12
     * @param pageRequest:
     * @param productKey:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.product.PropertyVo>
     **/
    PageResult<Property> pageProperties(PageRequest pageRequest,
                                        String productKey);

    /**
     * 属性列表
     * @param productKey
     * @return
     */
    List<Property> listProperties(String productKey);


    /**
     * 添加物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param serviceDto:
     * @return: java.lang.Boolean
     **/
    Boolean addService(ServiceDto serviceDto);

    /**
     * 添加物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型服务id
     * @return: java.lang.Boolean
     **/
    Service getService(String productKey, String identifier);

    /**
     * 编辑物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param serviceDto:
     * @return: java.lang.Boolean
     **/
    Boolean editService(ServiceDto serviceDto);

    /**
     * 删除物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型服务id
     * @return: java.lang.Boolean
     **/
    Boolean deleteService(String productKey, String identifier);

    /**
     * 分页获取功能事件列表
     * <AUTHOR>
     * @date 17:47 2022/7/12
     * @param pageRequest:
     * @param productKey:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.product.ServiceVo>
     **/
    PageResult<Service> pageServices(PageRequest pageRequest, String productKey);

    /**
     * 功能事件列表
     * @param productKey:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.product.ServiceVo>
     */
    List<Service> listServices(String productKey);
    /**
     * 添加物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param serviceDto:
     * @return: java.lang.Boolean
     **/
    Boolean addEvent(EventDto serviceDto);

    /**
     * 添加物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型服务id
     * @return: java.lang.Boolean
     **/
    Event getEvent(String productKey, String identifier);

    Event getSimpleEvent(String productKey, String identifier);
    /**
     * 编辑物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param serviceDto:
     * @return: java.lang.Boolean
     **/
    Boolean editEvent(EventDto serviceDto);

    /**
     * 删除物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型服务id
     * @return: java.lang.Boolean
     **/
    Boolean deleteEvent(String productKey, String identifier);

    /**
     * 分页获取功能事件列表
     * <AUTHOR>
     * @date 17:47 2022/7/12
     * @param pageRequest:
     * @param productKey:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.product.EventVo>
     **/
    PageResult<Event> pageEvents(PageRequest pageRequest, String productKey);

    /**
     * 功能事件列表
     * @param productKey
     * @return
     */
    List<Event> listEvents(String productKey);

    /**
     * 批量添加物模型属性（导入）
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param iotThingModelDto:
     * @return: java.lang.Boolean
     **/
    Boolean addThingModel(IotThingModelDto iotThingModelDto);

    /**
     * 根据名称模糊查询物模型
     * @param productId
     * @param name
     * @return
     */
    List<BaseThingModelItem> listThingModelIdentifiersLikeName(Long productId, String name);

    /**
     * 根据名称模糊查询物模型
     * @param productId
     * @param identifier
     * @return
     */
    BaseThingModelItem getThingModelByIdentifier(Long productId, String identifier);
}
