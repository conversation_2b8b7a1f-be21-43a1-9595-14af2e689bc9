package com.chervon.iot.middle.domain.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * (t_device_certificate)实体类
 *
 * <AUTHOR>
 * @since 2024-01-09 10:50:24
 * @description 设备证书关系
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_device_certificate")
public class DeviceCertificate  extends BaseDo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 证书id
     */
    private String certificateId;
    /**
     * 证书公钥
     */
    private String certificatePem;
    /**
     * 证书私钥
     */
    private String privateKey;
    /**
     * 证书arn角色
     */
    private String certificateArn;

}