package com.chervon.iot.middle.service.data_analysis.entity;


import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 设备基础信息
 * <AUTHOR>
 * @date 2023/7/12
 */
@AllArgsConstructor
@Data
public class DeviceInfoDto implements Serializable {

    /**
     * 硬件网关对应的设备id
     */
    private String deviceId;
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 上报的对象
     */
    private Map<String, Object> reportedObject;
}
