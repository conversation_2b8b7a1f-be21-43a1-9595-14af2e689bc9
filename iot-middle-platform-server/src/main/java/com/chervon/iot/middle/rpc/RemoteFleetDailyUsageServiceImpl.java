package com.chervon.iot.middle.rpc;

import com.chervon.fleet.web.api.entity.enums.ChargerCategoryEnum;
import com.chervon.iot.middle.api.dto.query.FleetUsageQuery;
import com.chervon.iot.middle.api.service.RemoteFleetDailyUsageService;
import com.chervon.iot.middle.api.service.RemoteIotDataService;
import com.chervon.iot.middle.api.vo.usage.FleetBatteryDailyUsageVo;
import com.chervon.iot.middle.api.vo.usage.FleetChargerDailyUsageVo;
import com.chervon.iot.middle.api.vo.usage.FleetToolDailyUsageVo;
import com.chervon.iot.middle.mapper.FleetDailyUsageMapper;
import com.chervon.iot.middle.service.AwsIotService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;

/**
 * 61001设备使用数据服务
 * <AUTHOR>
 * @date 2023/4/23 15:41
 */
@Service
@Slf4j
@DubboService
public class RemoteFleetDailyUsageServiceImpl implements RemoteFleetDailyUsageService {
    @Autowired
    private FleetDailyUsageMapper fleetDailyUsageMapper;
    @Autowired
    private AwsIotService awsIotService;
    @Autowired
    private RemoteIotDataService remoteIotDataService;

    /**
     * 查询FLEET工具设备日使用数据汇总
     * @param requestList 查询条件
     * @return FleetToolDailyUsageVo
     */
    public List<FleetToolDailyUsageVo> getToolDayUsage(List<FleetUsageQuery> requestList) {
        List<FleetToolDailyUsageVo> listVo = new ArrayList<>();
        for (FleetUsageQuery query : requestList) {
            try {
                String productKey = awsIotService.getProductKey(query.getDeviceId());
                FleetToolDailyUsageVo toolDayUsage = fleetDailyUsageMapper.getToolDayUsage(productKey, query);
                if (!Objects.isNull(toolDayUsage)) {
                    toolDayUsage.setCompanyId(query.getCompanyId());
                    toolDayUsage.setFirstCategoryCode(query.getFirstCategoryCode());
                    toolDayUsage.setDate(query.getDate());
                    Long start=query.getTimeStart()-2592000L;
                    Long end = query.getTimeStart();
                    final Long lastUsageTime = getLastValueByTimeSpan(query.getDeviceId(), "idf_1016", start, end);
                    if (lastUsageTime > 0) {
                        toolDayUsage.setMin1016(lastUsageTime.intValue());
                    }
                    final Long lastUsageNumber = getLastValueByTimeSpan(query.getDeviceId(), "idf_1018", start, end);
                    if (lastUsageNumber > 0) {
                        toolDayUsage.setMin1018(lastUsageNumber.intValue());
                    }
                    final Long lastUsageEnergy = getLastValueByTimeSpan(query.getDeviceId(), "idf_1025", start, end);
                    if (lastUsageEnergy > 0) {
                        toolDayUsage.setMin1025(lastUsageEnergy.intValue());
                    }
                    listVo.add(toolDayUsage);
                }
            } catch (Exception e) {
                log.error("getToolDayUsage data error，query:{},{}", query, e);
            }
        }
        return listVo;
    }
    /**
     * 查询FLEET工具设备日使用数据汇总
     * @param requestList 查询条件
     * @return FleetToolDailyUsageVo
     */
    public List<FleetChargerDailyUsageVo> getChargerDayUsage(List<FleetUsageQuery> requestList){
        List<FleetChargerDailyUsageVo> listVo = new ArrayList<>();
        for (FleetUsageQuery query : requestList) {
            try {
                String productKey = awsIotService.getProductKey(query.getDeviceId());
                FleetChargerDailyUsageVo chargerDailyUsageVo = null;
                String queryField="idf_2004";
                if (query.getSecondCategoryCode().equals(ChargerCategoryEnum.PGX_HUB.getCode())) {
                    chargerDailyUsageVo = fleetDailyUsageMapper.getChargerHubDayUsage(productKey, query);
                }else{
                    chargerDailyUsageVo = fleetDailyUsageMapper.getChargerBankDayUsage(productKey, query);
                    queryField="idf_2023";
                }
                if (!Objects.isNull(chargerDailyUsageVo)) {
                    chargerDailyUsageVo.setCompanyId(query.getCompanyId());
                    chargerDailyUsageVo.setFirstCategoryCode(query.getFirstCategoryCode());
                    chargerDailyUsageVo.setSecondCategoryCode(query.getSecondCategoryCode());
                    chargerDailyUsageVo.setDate(query.getDate());
                    Long start=query.getTimeStart()-2592000L;
                    Long end = query.getTimeStart();
                    Long lastUsageTime = getLastValueByTimeSpan(query.getDeviceId(), queryField, start, end);
                    if (lastUsageTime > 0) {
                        chargerDailyUsageVo.setMinChargeTime(lastUsageTime.intValue());
                    }
                    final Long lastUsageNumber = getLastValueByTimeSpan(query.getDeviceId(), "idf_2027", start, end);
                    if (lastUsageNumber > 0) {
                        chargerDailyUsageVo.setMin2027(lastUsageNumber.intValue());
                    }
                    if (query.getSecondCategoryCode().equals(ChargerCategoryEnum.PGX_HUB.getCode())) {
                        chargerDailyUsageVo.setMinChargeTime(chargerDailyUsageVo.getMinChargeTime()*3600);
                        chargerDailyUsageVo.setMaxChargeTime(chargerDailyUsageVo.getMaxChargeTime()*3600);
                    }
                    listVo.add(chargerDailyUsageVo);
                }
            } catch (Exception e) {
                log.error("getChargerDayUsage data error，query:{},{}", query, e);
            }
        }
        return listVo;
    }

    /**
     * 查询FLEET工具设备日使用数据汇总
     * @param requestList 查询条件
     * @return FleetToolDailyUsageVo
     */
    public List<FleetBatteryDailyUsageVo> getBatteryDayUsage(List<FleetUsageQuery> requestList) {
        List<FleetBatteryDailyUsageVo> listVo = new ArrayList<>();
        for (FleetUsageQuery query : requestList) {
            try {
                String productKey = awsIotService.getProductKey(query.getDeviceId());
                FleetBatteryDailyUsageVo batteryDailyUsageVo = fleetDailyUsageMapper.getBatteryDayUsage(productKey, query);
                if (!Objects.isNull(batteryDailyUsageVo)) {
                    batteryDailyUsageVo.setCompanyId(query.getCompanyId());
                    batteryDailyUsageVo.setFirstCategoryCode(query.getFirstCategoryCode());
                    batteryDailyUsageVo.setSecondCategoryCode(query.getSecondCategoryCode());
                    batteryDailyUsageVo.setDate(query.getDate());
                    Long start=query.getTimeStart()-2592000L;
                    Long end = query.getTimeStart();
                    final Long lastUsageTime = getLastValueByTimeSpan(query.getDeviceId(), "idf_2026", start, end);
                    if (lastUsageTime > 0) {
                        batteryDailyUsageVo.setMin2026(lastUsageTime.intValue());
                    }
                    final Long lastUsageNumber = getLastValueByTimeSpan(query.getDeviceId(), "idf_2027", start, end);
                    if (lastUsageNumber > 0) {
                        batteryDailyUsageVo.setMin2027(lastUsageNumber.intValue());
                    }
                    final Long lastUsageEnergy = getLastValueByTimeSpan(query.getDeviceId(), "idf_2031", start, end);
                    if (lastUsageEnergy > 0) {
                        batteryDailyUsageVo.setMin2031(lastUsageEnergy.intValue());
                    }
                    final Long lastDischargingEnergy = getLastValueByTimeSpan(query.getDeviceId(), "idf_2035", start, end);
                    if (lastDischargingEnergy > 0) {
                        batteryDailyUsageVo.setMin2035(lastDischargingEnergy.intValue());
                    }
                    listVo.add(batteryDailyUsageVo);
                }
            } catch (Exception e) {
                log.error("getBatteryDayUsage data error，query:{},{}", query, e);
            }
        }
        return listVo;
    }

    private Long getLastValueByTimeSpan(String deviceId, String field,Long timeBegin,Long timeEnd) {
        try {
            String productKey = awsIotService.getProductKey(deviceId);
            String condition = field + " is not null and idf_1024 >= " + timeBegin+" and idf_1024 < "+timeEnd;
            final Map<String, Object> latestColumnLog = remoteIotDataService.getLatestDataBy1024(productKey, deviceId, field, condition);
            if (Objects.isNull(latestColumnLog) || Objects.isNull(latestColumnLog.get(field))) {
                return 0L;
            }
            return Long.valueOf(latestColumnLog.get(field).toString());
        } catch (Exception e) {
            return 0L;
        }
    }

}
