package com.chervon.iot.middle;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = {"com.chervon.common.log.aspect","com.chervon.iot.middle"})
@EnableDubbo
@EnableAsync
public class IotMiddlePlatformApplication {

    public static void main(String[] args) {
        SpringApplication.run(IotMiddlePlatformApplication.class, args);
    }

}
