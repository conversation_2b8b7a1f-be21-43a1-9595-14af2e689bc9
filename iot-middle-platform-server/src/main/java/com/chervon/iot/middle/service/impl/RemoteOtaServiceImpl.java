package com.chervon.iot.middle.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.amazonaws.services.iot.AWSIot;
import com.amazonaws.services.iot.model.*;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.middle.api.dto.device.IotPublishDto;
import com.chervon.iot.middle.api.exception.IotMiddleErrorCode;
import com.chervon.iot.middle.api.pojo.ota.JobDocument;
import com.chervon.iot.middle.api.service.RemoteDeviceShadowService;
import com.chervon.iot.middle.api.service.RemoteOtaService;
import com.chervon.iot.middle.config.ExceptionMessageUtil;
import com.chervon.iot.middle.service.AwsIotService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @className RemoteOtaServiceImpl
 * @description
 * @date 2022/7/18 15:01
 */
@DubboService
@Service
@Slf4j
public class RemoteOtaServiceImpl implements RemoteOtaService {

    @Autowired
    private AwsIotService awsIotService;

    @Autowired
    private RemoteDeviceShadowService remoteDeviceShadowService;


    @Override
    public void pushOtaJob(String jobId, List<String> targets, JobDocument jobDocument) {
        if (CollectionUtil.isEmpty(targets)) {
            return;
        }
        try {
            // 将分组添加到job，若添加失败则创建job
            Boolean result = addTarget2Job(jobId, targets);
            if (!result) {
                AWSIot awsIot = awsIotService.getAwsIot();
                CreateJobRequest createJobRequest = new CreateJobRequest();
                createJobRequest.setTargets(targets);
                createJobRequest.setTargetSelection(TargetSelection.CONTINUOUS.toString());
                createJobRequest.setJobId(jobId);
                //设置重试次数 aws限制最高10次
                RetryCriteria retryCriteria = new RetryCriteria();
                retryCriteria.setFailureType(RetryableFailureType.ALL.toString());
                retryCriteria.setNumberOfRetries(10);
                JobExecutionsRetryConfig jobExecutionsRetryConfig = new JobExecutionsRetryConfig();
                jobExecutionsRetryConfig.setCriteriaList(Lists.newArrayList(retryCriteria));
                createJobRequest.setJobExecutionsRetryConfig(jobExecutionsRetryConfig);

                String jsonStr = JSONUtil.toJsonStr(jobDocument);
                createJobRequest.setDocument(jsonStr);
                awsIot.createJob(createJobRequest);
            }
        } catch (ResourceAlreadyExistsException ex) {
            log.warn(String.format(IotMiddleErrorCode.JOB_EXISTS.getErrorMessage(), jobId));
        } catch (AWSIotException ex) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.OPERATE_OTA_JOB_EXCEPTION, ex.getMessage());
        }
    }

    @Override
    public void publishOtaNotifyMessage(Long jobId, String productSnCode) {
        IotPublishDto iotPublishDto = new IotPublishDto();
        iotPublishDto.setTopic(String.format("aws/things/%s/jobs/notify",productSnCode));
        Map otaNotifyMessage=new HashMap();
        otaNotifyMessage.put("jobId",jobId);
        otaNotifyMessage.put("timestamp",System.currentTimeMillis());
        iotPublishDto.setPayLoad(otaNotifyMessage);
        remoteDeviceShadowService.publish(iotPublishDto);
    }

    @Override
    public void cancelOtaJob(String jobId) {
        /**待设备端、app更新后，删除以下代码,增加设备任务表状态变更操作**/
        try {
            AWSIot awsIot = awsIotService.getAwsIot();
            CancelJobRequest request = new CancelJobRequest();
            request.setJobId(jobId);
            awsIot.cancelJob(request);
        } catch (ResourceNotFoundException ex) {
            log.warn(String.format(IotMiddleErrorCode.JOB_NOT_EXISTS.getErrorMessage(), jobId));
        } catch (AWSIotException ex) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.OPERATE_OTA_JOB_EXCEPTION, ex.getMessage());
        }
        /**待设备端、app更新后，删除以上代码,增加设备任务表状态变更操作**/

        //自定义主题：取消任务通知
        IotPublishDto iotPublishDto = new IotPublishDto();
        iotPublishDto.setTopic(String.format("aws/events/job/%s/canceled",jobId));
        Map otaNotifyMessage=new HashMap();
        otaNotifyMessage.put("jobId",jobId);
        otaNotifyMessage.put("timestamp",System.currentTimeMillis());
        iotPublishDto.setPayLoad(otaNotifyMessage);
        remoteDeviceShadowService.publish(iotPublishDto);
    }

    @Override
    public String getFirmwareVersion(String deviceId, String componentNumber) {
        try {
            String componentVersion = awsIotService.getShadowString("componentVersion", deviceId);
            Map<String, Object> desiredMetadata = JSON.parseObject(componentVersion).getJSONObject(
                    "state").getJSONObject("reported");
            if (desiredMetadata != null) {
                return (String) desiredMetadata.get(componentNumber);
            }
        } catch (com.amazonaws.services.iotdata.model.ResourceNotFoundException exception) {
            log.error("No firmware-related device shadows, deviceId:{}, componentNumber:{}", deviceId, componentNumber);
            return null;
        }
        return null;
    }

    @Override
    public Map<String, String> getAllFirmwareVersion(String deviceId) {
        try {
            String componentVersion = awsIotService.getShadowString("componentVersion", deviceId);
            Map<String, Object> reportedMap = JSON.parseObject(componentVersion).getJSONObject(
                    "state").getJSONObject("reported");
            final Map<String, Object> reportedMetadata = JSON.parseObject(componentVersion).getJSONObject(
                    "metadata").getJSONObject("reported");
            List<String> keys = new ArrayList<>();
            keys.addAll(reportedMetadata.keySet());
            Collections.sort(keys, (o1, o2) -> {
                Integer timestamp1 = ((Map<String, Integer>) reportedMetadata.get(o1)).get(
                        "timestamp");
                Integer timestamp2 = ((Map<String, Integer>) reportedMetadata.get(o2)).get(
                        "timestamp");
                return timestamp2.compareTo(timestamp1);
            });
            Map<String, String> returnMap = new LinkedHashMap<>(reportedMap.size());
            for (String key : keys) {
                returnMap.put(key, reportedMap.get(key).toString());
            }
            return returnMap;
        } catch (com.amazonaws.services.iotdata.model.ResourceNotFoundException exception) {
            log.error("device not reported firmware version number, deviceId：{}", deviceId);
            return null;
        } catch (AWSIotException exception) {
            log.error("get firmware version number error!", exception);
            return null;
        }
    }

    @Override
    public Map<String, String> getFirmwareVersionMap(String deviceId, List<String> componentNumbers) {
        Map<String, Object> desiredMetadata = null;
        try {
            String componentVersion = awsIotService.getShadowString("componentVersion", deviceId);
            desiredMetadata = JSON.parseObject(componentVersion).getJSONObject(
                    "state").getJSONObject("reported");
        } catch (com.amazonaws.services.iotdata.model.ResourceNotFoundException exception) {
            log.error("device not reported firmware version number, deviceId：{}", deviceId);
        } catch (AWSIotException exception) {
            log.error("get firmware version number error!", exception);
        }
        Map<String, String> target = new HashMap<>(CommonConstant.SIXTEEN);
        if (desiredMetadata != null) {
            for (String componentNumber : componentNumbers) {
                target.put(componentNumber, (String) desiredMetadata.get(componentNumber));
            }
        } else {
            for (String componentNumber : componentNumbers) {
                target.put(componentNumber, StringUtils.EMPTY);
            }
        }
        return target;
    }

    @Override
    public void stopJob(List<Long> jobIds) {
        if (CollectionUtil.isNotEmpty(jobIds)) {
            for (Long jobId : jobIds) {
                this.cancelOtaJob(jobId.toString());
            }
        }
    }

    /**
     * 将分组添加到job
     *
     * @param jobId:
     * @param targets:
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 14:32 2022/8/16
     **/
    private Boolean addTarget2Job(String jobId, List<String> targets) {
        try {
            AWSIot awsIot = awsIotService.getAwsIot();
            AssociateTargetsWithJobRequest request = new AssociateTargetsWithJobRequest();
            request.setJobId(jobId);
            request.setTargets(targets);
            awsIot.associateTargetsWithJob(request);
            return true;
        } catch (AWSIotException exception) {
            return false;
        }
    }
}
