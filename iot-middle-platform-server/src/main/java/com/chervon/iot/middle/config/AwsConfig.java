package com.chervon.iot.middle.config;

import com.chervon.common.core.prop.AwsProperties;
import com.chervon.iot.middle.service.AwsIotService;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName AwsConfig.
 * @Description AWS 配置类
 * <AUTHOR>
 * @Date 2021/6/8 16:57
 */
@Configuration
@EnableConfigurationProperties(AwsProperties.class)
public class AwsConfig {

    @Bean
    public AwsIotService awsUtil(AwsProperties awsProperties) {
        return new AwsIotService(awsProperties);
    }

}
