package com.chervon.iot.middle.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.iot.middle.domain.pojo.DeviceCertificate;

import java.util.List;

/**
 * 设备证书申请服务接口
 *
 * <AUTHOR>
 * @since 2024-01-09 10:50:24
 * @description 设备证书关系
 */
public interface DeviceCertificateService extends IService<DeviceCertificate> {

    /**
     * 根据多码信息申请证书（已申请过，直接从数据库中返回现有证书），未申请过则创建证书并入库保存；
     * @param listDeviceId
     * @return 返回请求证书列表
     */
    List<DeviceCertificate> applyForCertificate(List<String> listDeviceId);

    /**
     * 异步申请证书（已申请过，直接从数据库中返回现有证书）
     * @param listDeviceId 设备id
     */
    void asyncApplyForCertificate(List<String> listDeviceId);
}
