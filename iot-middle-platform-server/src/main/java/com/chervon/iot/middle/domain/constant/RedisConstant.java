package com.chervon.iot.middle.domain.constant;

/**
 * redis key 常量
 * <AUTHOR>
 * @date 14:50 2022/3/7
 **/
public interface RedisConstant {
    /**
     * 同步任务阻塞队列
     * <AUTHOR>
     * @date 14:53 2022/3/7
     **/
    String IOT_TASK_QUEUE_PRE = "iot:task:queue:";

    /**
     * 同步任务标识
     * <AUTHOR>
     * @date 14:53 2022/3/7
     **/
    String IOT_TASK_KEY_PRE = "iot:task:key:";

    /**
     * 同步任务标识
     * <AUTHOR>
     * @date 14:53 2022/3/7
     **/
    String IOT_JOB_PRE = "iot:job:";

    /**
     * 物模型属性自增id
     **/
    String IOT_THING_MODEL_NEXT_ID = "iot:thingModel:nextId:";

    /**
     * 物模型参数自增id
     **/
    String IOT_THING_MODEL_PARAM_IN_NEXT_ID = "iot:thingModel:param:in:nextId:";

    /**
     * 物模型参数自增id
     **/
    String IOT_THING_MODEL_PARAM_OUT_NEXT_ID = "iot:thingModel:param:out:nextId:";

    /**
     * 设备的deviceId
     **/
    String IOT_THING_MODEL_DEVICE_ID = "iot:thingModel:deviceId:";
    /**
     * 设备故障状态map缓存大key
     **/
    String DEVICE_FAULT_STATUS = "device:fault:status:{0}" ;
    /**
     * 设备故障状态map缓存大key
     **/
    Long DEVICE_FAULT_EXPIRE = 60 * 60 * 12L ;

    /**
     * product 物模型ID缓存key
     **/
    String IOT_PRODUCT_ID_KEY = "iot:product:modelId:";

    /**
     * product 扩展物模型ID缓存key
     **/
    String IOT_PRODUCT_ID_KEY_EXP = "iot:product:modelId:expand:";
}
