package com.chervon.iot.middle.service.impl;

import com.amazonaws.services.iot.AWSIot;
import com.amazonaws.services.iot.model.*;
import com.chervon.iot.middle.api.dto.device.IotDynamicGroupDto;
import com.chervon.iot.middle.api.dto.group.AddAwsStaticGroupDto;
import com.chervon.iot.middle.api.exception.IotMiddleErrorCode;
import com.chervon.iot.middle.api.service.RemoteAwsGroupService;
import com.chervon.iot.middle.api.service.RemoteDeviceService;
import com.chervon.iot.middle.api.service.RemoteProductService;
import com.chervon.iot.middle.api.vo.product.IotProductListVo;
import com.chervon.iot.middle.api.vo.product.IotProductVo;
import com.chervon.iot.middle.config.ExceptionMessageUtil;
import com.chervon.iot.middle.service.AwsIotService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @className IotDataServiceImpl
 * @description
 * @date 2022/5/16 16:37
 */
@DubboService
@Service
@Slf4j
public class RemoteProductServiceImpl implements RemoteProductService {
    @Autowired
    private AwsIotService awsIotService;
    @Autowired
    private RemoteAwsGroupService remoteAwsGroupService;

    @Override
    public void createAwsProduct(IotProductVo iotProductVo) {
        if (awsIotService.checkProductExisted(iotProductVo.getProductKey())) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.PRODUCT_KEY_EXISTED);
        }
        AWSIot awsIot = awsIotService.getAwsIot();
        CreateThingTypeRequest createThingTypeRequest = new CreateThingTypeRequest();
        createThingTypeRequest.setThingTypeName(iotProductVo.getProductKey());
        ThingTypeProperties thingTypeProperties = new ThingTypeProperties();
        thingTypeProperties.setThingTypeDescription(iotProductVo.getRemark());
        createThingTypeRequest.setThingTypeProperties(thingTypeProperties);
        awsIot.createThingType(createThingTypeRequest);
        //创建默认分组 包含此产品下所有设备
        AddAwsStaticGroupDto addAwsStaticGroupDto=new AddAwsStaticGroupDto();
        addAwsStaticGroupDto.setGroupName(iotProductVo.getProductKey());
        String thingGroupArn = remoteAwsGroupService.addStaticGroup(addAwsStaticGroupDto);
        if(!StringUtils.hasText(thingGroupArn)){
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.GROUP_NAME_CREATE_ERROR);
        }
    }

    @Override
    public void deleteProduct(String productKey) {
        // 删除产品前需要在废弃该产品5分钟后操作
        AWSIot awsIot = awsIotService.getAwsIot();
        DeleteThingTypeRequest deleteThingTypeRequest = new DeleteThingTypeRequest();
        deleteThingTypeRequest.setThingTypeName(productKey);
        try {
            awsIot.deleteThingType(deleteThingTypeRequest);
        } catch (Exception ex) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.OPERATE_PRODUCT_EXCEPTION, productKey);
        }
    }

    @Override
    public IotProductListVo listProducts() {
        AWSIot awsIot = awsIotService.getAwsIot();
        ListThingTypesRequest listThingTypesRequest = new ListThingTypesRequest();
        ListThingTypesResult listThingTypesResult = awsIot.listThingTypes(listThingTypesRequest);
        List<ThingTypeDefinition> thingTypes = listThingTypesResult.getThingTypes();
        List<IotProductVo> iotProductVos = new ArrayList<>();
        for (ThingTypeDefinition thingTypeDefinition : thingTypes) {
            IotProductVo iotProductVo = new IotProductVo();
            iotProductVo.setProductKey(thingTypeDefinition.getThingTypeName());
            iotProductVo.setRemark(thingTypeDefinition.getThingTypeProperties().getThingTypeDescription());
            iotProductVos.add(iotProductVo);
        }
        IotProductListVo iotProductListVo = new IotProductListVo();
        iotProductListVo.setProducts(iotProductVos);
        iotProductListVo.setNextToken(listThingTypesResult.getNextToken());
        return iotProductListVo;
    }

    @Override
    public void deprecateProduct(String productKey) {
        AWSIot awsIot = awsIotService.getAwsIot();
        DeprecateThingTypeRequest deprecateThingTypeRequest = new DeprecateThingTypeRequest();
        deprecateThingTypeRequest.setThingTypeName(productKey);
        awsIot.deprecateThingType(deprecateThingTypeRequest);
    }
}
