<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.message.mapper.UserDeviceMessageMapper">
    <sql id="tableName">
        t_user_device_message
    </sql>

    <sql id="baseColumn">
        id,title,content,payload_data,user_id,token,device_type,system_message_id,uuid,product_id,device_id,create_time,if_read,is_deleted,push_type,push_types,push_result
    </sql>

    <select id="countPushResultNum" resultType="com.chervon.technology.api.dto.FaultMessageResultCountDto">
        select system_message_id as systemMessageId,
        SUM(CASE WHEN push_result = 1 THEN 1 ELSE 0 END) AS successNum,
        SUM(CASE WHEN push_result = 0 THEN 1 ELSE 0 END) AS failNum
        FROM t_user_device_message where is_deleted = 0
        <if test="countMessageDto.listSystemMessageId != null and countMessageDto.listSystemMessageId.size() > 0">
            and system_message_id in
            <foreach collection="countMessageDto.listSystemMessageId" item="messageId" open="(" separator="," close=")">
                #{messageId}
            </foreach>
        </if>
        <if test="countMessageDto.beginDate != null ">
            and create_time &gt;= #{countMessageDto.beginDate}
        </if>
        <if test="countMessageDto.endDate != null ">
            and create_time &lt;= #{countMessageDto.endDate}
        </if>
        GROUP by system_message_id
    </select>

    <select id="queryDeviceLastMessage" resultType="com.chervon.message.domain.entity.UserDeviceMessage">
        WITH RankedMessages AS (
        SELECT user_id, device_id, product_id, title, content, uuid, create_time,
        ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY id DESC) AS rn
        FROM t_user_device_message WHERE user_id = #{userId}
        AND device_id IN
        <foreach collection="listDeviceId" item="deviceId" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
        AND create_time >= #{dateBegin} AND app_show = 1 AND if_read = 0
        AND is_deleted = 0
        )
        SELECT user_id, device_id, product_id, title, content, uuid, create_time
        FROM RankedMessages WHERE rn = 1;
    </select>

</mapper>