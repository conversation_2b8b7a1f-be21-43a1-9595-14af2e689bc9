package com.chervon.message.domain.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import java.util.Map;

import com.chervon.message.service.JsonTypeHandler.PayloadHandler;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * (t_market_message)实体类
 *
 * <AUTHOR>
 * @since 2024-08-26 19:01:35
 * @description 营销消息表
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "t_market_message",autoResultMap = true)
public class MarketMessage extends Model<MarketMessage> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
	private Long id;
    /**
     * title
     */
    private String title;
    /**
     * content
     */
    private String content;
    /**
     * payloadData
     */
    @TableField(value="payload_data",typeHandler = PayloadHandler.class)
    private Map<String, String> payloadData;
    /**
     * deviceType
     */
    private String deviceType;
    /**
     * systemMessageId
     */
    private String systemMessageId;
    /**
     * uuid
     */
    private String uuid;
    /**
     * createTime
     */
    private Date createTime;
    /**
     * pushTypes
     */
    private String pushTypes;
    /**
     * isDeleted
     */
    private Integer isDeleted;
    /**
     * 用户id
     */
    private Long userId;
}