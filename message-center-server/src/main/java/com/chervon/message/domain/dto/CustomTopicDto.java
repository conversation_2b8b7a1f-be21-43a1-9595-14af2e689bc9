package com.chervon.message.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-08-26
 */
@Data
public class CustomTopicDto implements Serializable {

    @ApiModelProperty(value = "推送消息标题", required = true)
    private String title;

    @ApiModelProperty(value = "消息默认body")
    private String content;

    @ApiModelProperty(value = "推送消息负载，设备消息时添加deviceId和productId")
    private Map<String, String> payloadData;

    @ApiModelProperty(value = "消息类型：0系统消息，1营销消息,2设备消息")
    private Integer messageType;

    @ApiModelProperty("消息的唯一标识")
    private String uuid;

    @ApiModelProperty("消息推送方式：0墓碑消息，1APP弹窗，2APP banner, 3消息管理")
    @NotNull
    private List<Integer> pushTypes;
}
