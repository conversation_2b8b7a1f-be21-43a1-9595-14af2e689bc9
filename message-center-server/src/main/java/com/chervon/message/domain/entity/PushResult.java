package com.chervon.message.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 通用消息结果对象
 * <AUTHOR>
 * @date 2024-08-19
 */
@Data
public class PushResult implements Serializable {
    public PushResult(Boolean pushResult, String reason) {
        this.pushResult = pushResult;
        this.reason = reason;
    }

    @ApiModelProperty("推送结果，true:成功， false:失败")
    private Boolean pushResult;

    @ApiModelProperty("推送失败原因")
    private String reason;

    public static PushResult ofFailed(String reason) {
        return new PushResult(false,reason);
    }

    public static PushResult success() {
        return new PushResult(true,"");
    }
    public static PushResult success(String info) {
        return new PushResult(true,info);
    }

}
