package com.chervon.message.config;

import cn.hutool.extra.spring.SpringUtil;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.message.api.exception.MessageErrorCode;
import com.chervon.message.api.exception.MessageException;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/17 11:18
 */
public class ExceptionMessageUtil {

    private static final RemoteMultiLanguageService remoteMultiLanguageService = SpringUtil.getBean("remoteMultiLanguageService");

    public static MessageException getException(MessageErrorCode errorCode, Object... args) {
        MessageException exception = new MessageException(errorCode, args);
        String code = errorCode.getCode();
        if (code != null) {
            String language = remoteMultiLanguageService.simpleFindMultiLanguageByCode(code);
            if (language != null) {
                exception.setTempMessage(language);
            }
        }
        return exception;
    }

}
