package com.chervon.message.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022-08-26
 */
@Slf4j
@Component
public class PushConfig {

    private static String iosKeyFilePassword;

    private static String iosKeyFileName;

    private static String iosPushTopic;

    private static String iosEnvironment;

    private static String androidAppName;

    private static String fcmPushFile;

    @Value("${push.apns.keyFilePassword}")
    public void setIosKeyFilePassword(String iosKeyFilePassword) {
        PushConfig.iosKeyFilePassword = iosKeyFilePassword;
    }

    public static String getIosKeyFilePassword() {
        return iosKeyFilePassword;
    }

    @Value("${push.apns.keyFileName}")
    public void setIosKeyFileName(String iosKeyFileName) {
        PushConfig.iosKeyFileName = iosKeyFileName;
    }

    public static String getIosKeyFileName() {
        return iosKeyFileName;
    }

    @Value("${push.android.androidAppName}")
    public void setAndroidAppName(String androidAppName) {
        PushConfig.androidAppName = androidAppName;
    }

    public static String getAndroidAppName() {
        return androidAppName;
    }

    @Value("${push.android.fcmPushFile}")
    public void setFcmPushFile(String fcmPushFile) {
        PushConfig.fcmPushFile = fcmPushFile;
    }

    public static String getFcmPushFile() {
        return fcmPushFile;
    }

    @Value("${push.apns.environment}")
    public void setIosEnvironment(String iosEnvironment) {
        PushConfig.iosEnvironment = iosEnvironment;
    }

    public static String getIosEnvironment() {
        return iosEnvironment;
    }

    public static String getIosPushTopic() {
        return iosPushTopic;
    }

    @Value("${push.apns.pushTopic}")
    public void setIosPushTopic(String iosPushTopic) {
        PushConfig.iosPushTopic = iosPushTopic;
    }

}
