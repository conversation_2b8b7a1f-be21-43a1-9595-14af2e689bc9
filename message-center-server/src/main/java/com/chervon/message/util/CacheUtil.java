package com.chervon.message.util;

import com.chervon.common.redis.utils.RedisUtils;
import lombok.Data;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 通用本地缓存工具类
 * <AUTHOR> 2024/10/14
 */
public class CacheUtil {

    // 存缓存数据
    private final static Map<String, CacheEntity> CACHE_MAP = new ConcurrentHashMap<>();
    public final static Long DEFAULT_EXPIRE = 5L;
    public final static Long DEFAULT_EXPIRE_REDIS_SECOND = 300L;

    // 定时器线程池，用于清理过期缓存
    private static ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();

    static {
        // 注册一个定时任务，服务启动 60秒后，每隔 60秒执行一次清理过期缓存的任务
        Runnable task = CacheUtil::clear;
        executorService.scheduleAtFixedRate(task, 60000L, 300L, TimeUnit.SECONDS);
    }

    // 添加缓存(过期：取默认过期时间)
    public static void putLocal(String key, Object value) {
        putLocal(key, value, DEFAULT_EXPIRE);
    }

    public static void putRedis(String key, Object value){
        RedisUtils.setCacheObject(key,value);
    }
    public static void putRedisExpire(String key,Object value,Long expireSecond){
        RedisUtils.setWithExpire(key,value,expireSecond);
    }

    // 添加缓存
    public static void putLocal(String key, Object value, Long expire) {
        CacheEntity cacheEntity = new CacheEntity();
        cacheEntity.setKey(key);
        cacheEntity.setValue(value);
        if (expire > 0) {
            // 计算过期时间
            Long expireTime = System.currentTimeMillis() + Duration.ofSeconds(expire).toMillis();
            cacheEntity.setExpireTime(expireTime);
        }
        CACHE_MAP.put(key, cacheEntity);
    }

    // 获取
    public static Object getLocal(String key) {
        if (CACHE_MAP.containsKey(key)) {
            return CACHE_MAP.get(key);
        }
        return null;
    }

    public static <T> T getRedis(String key) {
        return RedisUtils.getCacheObject(key);
    }

    // 删除
    public static void removeLocal(String key) {
        CACHE_MAP.remove(key);
    }

    public static void removeRedis(String key) {
        RedisUtils.deleteObject(key);
    }

    // 清除过期缓存
    public static void clear() {
        if (CACHE_MAP.isEmpty()) {
            return;
        }
        CACHE_MAP.entrySet().removeIf(entityEntry -> entityEntry.getValue().getExpireTime() != null && entityEntry.getValue().getExpireTime() > System.currentTimeMillis());
    }
}
@Data
class CacheEntity {
    // 缓存键
    private String key;
    // 缓存键
    private Object value;
    // 过期时间
    private Long expireTime;
}

