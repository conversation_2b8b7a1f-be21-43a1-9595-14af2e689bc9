package com.chervon.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.message.api.MessageConstant;
import com.chervon.message.api.dto.*;
import com.chervon.message.api.enums.*;
import com.chervon.message.api.vo.MessageVo;
import com.chervon.message.domain.entity.UserMarketMessage;
import com.chervon.message.mapper.UserMarketMessageMapper;
import com.chervon.message.service.UserMarketMessageService;
import com.chervon.message.util.CacheUtil;
import com.chervon.operation.api.dto.MessagePushResultCountDto;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 服务接口实现
 *
 * <AUTHOR>
 * @since 2024-07-18 17:10:17
 * @description 用户营销消息
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserMarketMessageServiceImpl extends ServiceImpl<UserMarketMessageMapper, UserMarketMessage> implements UserMarketMessageService {

    @Override
    public MessageVo getUserLastMessage(LastMessageDto requestDto) {
        LambdaQueryWrapper<UserMarketMessage> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(UserMarketMessage::getUserId,requestDto.getUserId())
                .eq(UserMarketMessage::getAppShow, AppShowEnum.YES.getType())
                .eq(UserMarketMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType())
                .eq(UserMarketMessage::getIfRead, ReadFlagEnum.NO.getType())
                .orderByDesc(UserMarketMessage::getId)
                .last(" limit 1")
                .select(UserMarketMessage::getUserId,UserMarketMessage::getTitle, UserMarketMessage::getContent,
                        UserMarketMessage::getCreateTime, UserMarketMessage::getUuid);
        final UserMarketMessage lastMessage = getOne(queryWrapper);
        MessageVo messageSystem=null;
        if (!Objects.isNull(lastMessage)) {
            messageSystem = ConvertUtil.convert(lastMessage, MessageVo.class);
            messageSystem.setMessageType(MessageTypeEnum.MARKETING_MSG.getValue());
        }
        return messageSystem;
    }

    /**
     * 更新消息为已读
     * @param requestDto
     * @return
     */
    @Override
    public boolean updateMessageRead(MessageEditDto requestDto){
        if(Objects.isNull(requestDto.getId()) && CollectionUtils.isEmpty(requestDto.getListUuid()) &&
                StringUtils.isEmpty(requestDto.getUuid()) && StringUtils.isEmpty(requestDto.getSystemMessageId())){
            return false;
        }
        LambdaUpdateWrapper<UserMarketMessage> updateWrapper=new LambdaUpdateWrapper<UserMarketMessage>()
                .eq(!Objects.isNull(requestDto.getId()),UserMarketMessage::getId,requestDto.getId())
                .eq(StringUtils.isNotEmpty(requestDto.getUuid()),UserMarketMessage::getUuid,requestDto.getUuid())
                .eq(UserMarketMessage::getUserId,requestDto.getUserId())
                .in(!CollectionUtils.isEmpty(requestDto.getListUuid()),UserMarketMessage::getUuid,requestDto.getListUuid())
                .eq(StringUtils.isNotEmpty(requestDto.getSystemMessageId()),UserMarketMessage::getSystemMessageId,requestDto.getSystemMessageId())
                .eq(UserMarketMessage::getIfRead, ReadFlagEnum.NO.getType())
                .eq(UserMarketMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType())
                .set(UserMarketMessage::getIfRead, ReadFlagEnum.YES.getType());
        return update(updateWrapper);
    }

    @Override
    public PageResult<MessageVo> getPageList(SearchMessageInfoDto pageRequest) {
        //构建查询条件
        LambdaQueryWrapper<UserMarketMessage> queryWrapper=new LambdaQueryWrapper<UserMarketMessage>()
                .eq(UserMarketMessage::getUserId,pageRequest.getUserId())
                .eq(UserMarketMessage::getAppShow, AppShowEnum.YES.getType())
                .eq(UserMarketMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType())
                .lt(!Objects.isNull(pageRequest.getCreateTime()), UserMarketMessage::getCreateTime,pageRequest.getCreateTime())
                .orderByDesc(UserMarketMessage::getCreateTime);
        //分页请求对象
        Page<UserMarketMessage> pageReq = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        //分页查询
        final Page<UserMarketMessage> page = this.page(pageReq, queryWrapper);
        //构建返回结果
        PageResult<MessageVo> pageResult = new PageResult<>(pageRequest.getPageNum(), pageRequest.getPageSize(), page.getTotal());

        pageResult.setPages(page.getPages());
        final List<MessageVo> messageVoList = convertToResultList(page.getRecords());
        pageResult.setList(messageVoList);
        return pageResult;
    }
    /**
     * 根据综合查询条件查询分页消息列表
     * @param pageRequest 搜索对象
     * @return
     */
    @Override
    public PageResult<MessageVo> SearchPageList(SearchMessageDto pageRequest) {
        //构建查询条件
        final LambdaQueryWrapper<UserMarketMessage> queryWrapper = buildQueryCondition(pageRequest);
        //分页请求对象
        Page<UserMarketMessage> pageReq = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        //分页查询
        final Page<UserMarketMessage> page = this.page(pageReq, queryWrapper);
        //构建返回结果
        PageResult<MessageVo> pageResult = new PageResult<>(pageRequest.getPageNum(),
                pageRequest.getPageSize(), page.getTotal());
        pageResult.setPages(page.getPages());
        final List<MessageVo> messageVoList = convertToResultList(page.getRecords());
        pageResult.setList(messageVoList);
        return pageResult;
    }

    @Override
    public List<MessageVo> getList(SearchMessageDto searchMessageDto) {
        //构建查询条件
        final LambdaQueryWrapper<UserMarketMessage> queryWrapper = buildQueryCondition(searchMessageDto);
        //执行查询
        final List<UserMarketMessage> list = this.list(queryWrapper);
        return convertToResultList(list);
    }

    @Override
    public List<MessagePushResultCountDto> countPushResultNum(CountMessageDto countMessageDto) {
        return this.baseMapper.countPushResultNum(countMessageDto);
    }

    @NotNull
    private static List<MessageVo> convertToResultList(List<UserMarketMessage> list) {
        List<MessageVo> messageVoList = new ArrayList<>();
        //返回对象类型转换
        for (UserMarketMessage message : list) {
            final MessageVo messageVo = BeanCopyUtils.copy(message, MessageVo.class);
            messageVo.setPushType(PushMethodEnum.getValueByPushTypes(message.getPushType()));
            messageVo.setMessageType(MessageTypeEnum.MARKETING_MSG.getValue());
            messageVo.setPushResult(message.getPushResult().equals(PushResultEnum.SUCCESS.getType())?true:false);
            messageVoList.add(messageVo);
        }
        return messageVoList;
    }

    private static LambdaQueryWrapper<UserMarketMessage> buildQueryCondition(SearchMessageDto pageRequest) {
        LambdaQueryWrapper<UserMarketMessage> queryWrapper = new LambdaQueryWrapper<UserMarketMessage>()
                .eq(UserMarketMessage::getSystemMessageId, pageRequest.getSystemMessageId())
                .eq(UserMarketMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType())
                .eq(StringUtils.isNotEmpty(pageRequest.getUuid()), UserMarketMessage::getUuid, pageRequest.getUuid())
                .eq(StringUtils.isNotEmpty(pageRequest.getTitle()), UserMarketMessage::getTitle, pageRequest.getTitle())
                .eq(!Objects.isNull(pageRequest.getPushType()), UserMarketMessage::getPushType, pageRequest.getPushType())
                .eq(!Objects.isNull(pageRequest.getPushResult()), UserMarketMessage::getPushResult, (pageRequest.getPushResult()==null|| pageRequest.getPushResult()==false)?0:1)
                .eq(StringUtils.isNotEmpty(pageRequest.getUserId()), UserMarketMessage::getUserId, pageRequest.getUserId())
                .in(!CollectionUtils.isEmpty(pageRequest.getUserIds()), UserMarketMessage::getUserId, pageRequest.getUserIds())
                .orderByDesc(UserMarketMessage::getCreateTime);
        //根据开始结束时间进行过滤
        filterByStartEndTime(pageRequest, queryWrapper);
        return queryWrapper;
    }

    @Override
    public MessageVo getDetail(MessageDetailDto messageDetail) {
        //构建查询条件
        LambdaQueryWrapper<UserMarketMessage> queryWrapper=new LambdaQueryWrapper<UserMarketMessage>()
                .eq(UserMarketMessage::getUserId,messageDetail.getUserId())
                .eq(UserMarketMessage::getUuid,messageDetail.getUuid())
                .eq(UserMarketMessage::getAppShow, AppShowEnum.YES.getType())
                .eq(UserMarketMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType());
        //查询结果
        List<UserMarketMessage> list = this.list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        UserMarketMessage message=list.get(0);
        final MessageVo messageVo = BeanCopyUtils.copy(message, MessageVo.class);
        if(message.getIfRead().equals(ReadFlagEnum.NO.getType())){
            Boolean success = updateMessageRead(new MessageEditDto().setUuid(message.getUuid()).setUserId(messageDetail.getUserId()));
            if(success){
                messageVo.setIfRead(ReadFlagEnum.YES.getType());
            }
        }
        return messageVo;
    }

    @Override
    public void delete(MessageDetailDto deleteDetail) {
        //构建更新条件
        LambdaUpdateWrapper<UserMarketMessage> updateWrapper=new LambdaUpdateWrapper<UserMarketMessage>()
                .eq(UserMarketMessage::getUserId,deleteDetail.getUserId())
                .eq(UserMarketMessage::getUuid,deleteDetail.getUuid())
                .eq(UserMarketMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType())
                .set(UserMarketMessage::getIsDeleted, DeletedFlagEnum.DELETED.getType());
        //执行更新操作
        this.update(updateWrapper);
    }

    @Async
    @Override
    public void deleteByUserId(Long userId) {
        //构建更新条件
        LambdaUpdateWrapper<UserMarketMessage> updateWrapper=new LambdaUpdateWrapper<UserMarketMessage>()
                .eq(UserMarketMessage::getUserId,userId)
                .eq(UserMarketMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType())
                .set(UserMarketMessage::getIsDeleted, DeletedFlagEnum.DELETED.getType());
        //执行更新操作
        this.update(updateWrapper);
    }

    @SneakyThrows
    public static void filterByStartEndTime(SearchMessageDto pageRequest, LambdaQueryWrapper<UserMarketMessage> queryWrapper) {
        SimpleDateFormat sdfV3 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (StringUtils.isNotEmpty(pageRequest.getCreateStartTime())) {
            Date startDate = sdfV3.parse(pageRequest.getCreateStartTime());
            queryWrapper.ge(StringUtils.isNotEmpty(pageRequest.getCreateStartTime()), UserMarketMessage::getCreateTime, startDate);
        }
        if (StringUtils.isNotEmpty(pageRequest.getCreateEndTime())) {
            Date endDate = sdfV3.parse(pageRequest.getCreateEndTime());
            queryWrapper.lt(StringUtils.isNotEmpty(pageRequest.getCreateEndTime()), UserMarketMessage::getCreateTime, endDate);
        }
    }
}