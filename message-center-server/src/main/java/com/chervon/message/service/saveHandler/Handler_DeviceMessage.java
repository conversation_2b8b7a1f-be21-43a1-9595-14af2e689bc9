package com.chervon.message.service.saveHandler;

import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.message.api.MessageConstant;
import com.chervon.message.api.dto.CountMessageDto;
import com.chervon.message.api.enums.MessageTypeEnum;
import com.chervon.message.api.enums.PushResultEnum;
import com.chervon.message.domain.entity.DeviceMessage;
import com.chervon.message.domain.entity.MessageResult;
import com.chervon.message.domain.entity.UserDeviceMessage;
import com.chervon.message.service.DeviceMessageService;
import com.chervon.message.service.UserDeviceMessageService;
import com.chervon.message.service.impl.MessageUtilService;
import com.chervon.message.util.CacheUtil;
import com.chervon.message.util.DateToolUtil;
import com.chervon.operation.api.dto.MessagePushResultCountDto;
import com.chervon.technology.api.RemoteFaultMessageService;
import com.chervon.technology.api.dto.FaultMessageResultCountDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备消息推送保存数据库并更新统计记录
 * <AUTHOR> 2024/8/19
 */
@Slf4j
@Service
public class Handler_DeviceMessage implements IMessageHandler {
    private UserDeviceMessageService userDeviceMessageService;
    private DeviceMessageService deviceMessageService;
    @DubboReference
    private RemoteFaultMessageService remoteFaultMessageService;

    public Handler_DeviceMessage(UserDeviceMessageService userDeviceMessageService, DeviceMessageService deviceMessageService) {
        this.userDeviceMessageService = userDeviceMessageService;
        this.deviceMessageService = deviceMessageService;
    }

    @Override
    public MessageTypeEnum getMessageType() {
        return MessageTypeEnum.DEVICE_MSG;
    }


    @Override
    public void saveMessageResult(List<MessageResult> messageResults) {
        List<UserDeviceMessage> userDeviceMessages = messageResults.stream().map(a->
                        new UserDeviceMessage()
                         .setId(SnowFlake.nextId())
                        .setUuid(a.getUuid())
                        .setDeviceId(a.getDeviceId())
                        .setProductId(Long.valueOf(a.getProductId()))
                        .setTitle(a.getTitle())
                        .setContent(a.getContent())
                        .setSystemMessageId(a.getSystemMessageId())
                        .setUserId(Long.valueOf(a.getUserId()))
                        .setPushType(a.getPushType())
                        .setDeviceType(a.getDeviceType())
                        .setPayloadData(a.getPayloadData())
                        .setPushTypes(a.getPushTypes().stream().map(String::valueOf).collect(Collectors.joining(",")))
                        .setPushResult(a.getPushResult()? PushResultEnum.SUCCESS.getType() :PushResultEnum.FAIL.getType())
                        .setReason(a.getReason())
                        .setIfRead(a.getIfRead())
                        .setIsDeleted(a.getIsDeleted())
                        .setCreateTime(a.getCreateTime())
                        .setToken(a.getToken())
                        .setAppShow(a.getAppShow()))
                .collect(Collectors.toList());
        userDeviceMessageService.saveBatch(userDeviceMessages);
        userDeviceMessages.stream().forEach(a->{
            CacheUtil.removeRedis(MessageConstant.LAST_DEVICE_MESSAGE_PREFIX + a.getUserId());
        });
        pushStatisticsEvent(messageResults);
    }

    @Async
    @Override
    public void saveMessage(List<MessageResult> messages) {
        try {
            Map<String,DeviceMessage> deviceMessageMap=new HashMap<>();
            for(MessageResult messageResult:messages){
                if(deviceMessageMap.containsKey(messageResult.getUuid())){
                    continue;
                }
                DeviceMessage deviceMessage = BeanCopyUtils.copy(messageResult, DeviceMessage.class);
                deviceMessage.setId(Long.valueOf(messageResult.getUuid()));
                deviceMessage.setPayloadData(messageResult.getPayloadData());
                deviceMessage.setProductId(Long.valueOf(messageResult.getProductId()));
                deviceMessage.setPushTypes(messageResult.getPushTypes().stream().map(String::valueOf).collect(Collectors.joining(",")));
                deviceMessage.setUserId(Objects.isNull(messageResult.getUserId())?0L:Long.valueOf(messageResult.getUserId()));
                deviceMessageMap.put(messageResult.getUuid(),deviceMessage);
            }
            deviceMessageService.saveBatch(deviceMessageMap.values());
        } catch (Exception e) {
            log.error("save device message error:{}",e);
        }
    }

    @Override
    public void pushStatisticsEvent(List<MessageResult> messageResults) {
        MessageUtilService.pushStatisticsEvent(messageResults);
    }

    @Async
    @Override
    public void updateMessageCount(String strMessageIdList) {
        List<String> messageIdList = new ArrayList<>();
        //查看是否有job外部输入参数：all代表初始化统计所有，其他代表指定systemmessageId进行统计，逗号分隔
        if(StringUtils.isEmpty(strMessageIdList)){
            messageIdList = MessageUtilService.getStatisticsMessageIdList(this.getMessageType().getValue());
            if(CollectionUtils.isEmpty(messageIdList)){
                return;
            }
        }else{
            //job窗口输入all代表统计所有systemMessageId
            if(strMessageIdList.toLowerCase().equals("all")){
                //初始化统计全部systemMessageId
            }else{
                //逗号分隔指定统计哪些systemMessageId
                final String[] split = strMessageIdList.split(",");
                Collections.addAll(messageIdList,split);
            }
        }
        //计算六个月内开始时间（当前月往前推5个月的1日开始）
        LocalDateTime sixMonthsAgo = DateToolUtil.getMonthBegin(5);
        final Date beginTime = Date.from(sixMonthsAgo.atZone(ZoneId.systemDefault()).toInstant());
        final CountMessageDto countMessageDto = new CountMessageDto()
                .setListSystemMessageId(messageIdList)
                .setBeginDate(beginTime).setEndDate(new Date());
        List<FaultMessageResultCountDto> messagePushResultCountDtos = userDeviceMessageService.countPushResultNum(countMessageDto);
        // 定义每批次的大小
        int batchSize = 100;
        // 计算总批次数
        int totalBatches = (int) Math.ceil((double) messagePushResultCountDtos.size() / batchSize);
        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            // 计算当前批次的起始和结束索引
            int fromIndex = batchIndex * batchSize;
            int toIndex = Math.min(fromIndex + batchSize, messagePushResultCountDtos.size());
            // 获取当前批次的子列表
            List<FaultMessageResultCountDto> batchList = messagePushResultCountDtos.subList(fromIndex, toIndex);
            // 执行更新操作
            remoteFaultMessageService.updateFaultMsgCount(batchList);
            //移除已统计的消息
            final List<String> collect = batchList.stream().map(FaultMessageResultCountDto::getSystemMessageId).collect(Collectors.toList());
            MessageUtilService.removeMessageIdList(this.getMessageType().getValue(),collect);
        }
    }

}
