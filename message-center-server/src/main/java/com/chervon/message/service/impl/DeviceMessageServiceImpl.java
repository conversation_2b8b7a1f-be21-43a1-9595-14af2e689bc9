package com.chervon.message.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.message.domain.entity.DeviceMessage;
import com.chervon.message.mapper.DeviceMessageMapper;
import com.chervon.message.service.DeviceMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 服务接口实现
 *
 * <AUTHOR>
 * @since 2024-07-18 17:10:17
 * @description 设备消息
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DeviceMessageServiceImpl extends ServiceImpl<DeviceMessageMapper, DeviceMessage> implements DeviceMessageService {


}