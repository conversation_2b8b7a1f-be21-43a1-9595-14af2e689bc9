package com.chervon.message.service.saveHandler;

import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.message.api.dto.CountMessageDto;
import com.chervon.message.api.enums.DeletedFlagEnum;
import com.chervon.message.api.enums.MessageTypeEnum;
import com.chervon.message.api.enums.PushResultEnum;
import com.chervon.message.domain.entity.MessageResult;
import com.chervon.message.domain.entity.SystemMessage;
import com.chervon.message.domain.entity.UserSystemMessage;
import com.chervon.message.service.SystemMessageService;
import com.chervon.message.service.UserSystemMessageService;
import com.chervon.message.service.impl.MessageUtilService;
import com.chervon.message.util.DateToolUtil;
import com.chervon.operation.api.RemoteOperationMessageService;
import com.chervon.operation.api.dto.MessagePushResultCountDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备分享消息推送记录保存
 * <AUTHOR> 2024/11/18
 */
@Slf4j
@Service
public class Handler_ShareMessage implements IMessageHandler {
    private UserSystemMessageService userSystemMessageService;
    private SystemMessageService systemMessageService;
    @DubboReference
    private RemoteOperationMessageService remoteOperationMessageService;
    @Override
    public MessageTypeEnum getMessageType() {
        return MessageTypeEnum.SHARE_MSG;
    }

    public Handler_ShareMessage(UserSystemMessageService userSystemMessageService, SystemMessageService systemMessageService) {
        this.userSystemMessageService = userSystemMessageService;
        this.systemMessageService = systemMessageService;
    }

    @Override
    public void saveMessageResult(List<MessageResult> messageResults) {
        List<UserSystemMessage> systemMessageList = messageResults.stream().map(a-> new UserSystemMessage().setId(SnowFlake.nextId())
                .setUuid(a.getUuid())
                .setTitle(a.getTitle())
                .setContent(a.getContent())
                .setSystemMessageId(a.getSystemMessageId())
                .setUserId(Long.valueOf(a.getUserId()))
                .setPushType(a.getPushType())
                .setDeviceType(a.getDeviceType())
                .setPayloadData(a.getPayloadData())
                .setPushTypes(a.getPushTypes().stream().map(String::valueOf).collect(Collectors.joining(",")))
                .setPushResult(a.getPushResult()? PushResultEnum.SUCCESS.getType() :PushResultEnum.FAIL.getType())
                .setReason(a.getReason())
                .setIfRead(a.getIfRead())
                .setAppShow(a.getAppShow())
                .setIsDeleted(DeletedFlagEnum.NORMAL.getType())
                .setCreateTime(a.getCreateTime())).collect(Collectors.toList());
        userSystemMessageService.saveBatch(systemMessageList);
        pushStatisticsEvent(messageResults);
    }

    @Async
    @Override
    public void saveMessage(List<MessageResult> messages) {
        try {
            Map<String,SystemMessage> messageHashMap=new HashMap<>();
            for(MessageResult messageResult:messages){
                if(messageHashMap.containsKey(messageResult.getUuid())){
                    continue;
                }
                SystemMessage systemMessage = BeanCopyUtils.copy(messageResult, SystemMessage.class);
                systemMessage.setId(Long.valueOf(messageResult.getUuid()));
                systemMessage.setPayloadData(messageResult.getPayloadData());
                systemMessage.setPushTypes(messageResult.getPushTypes().stream().map(String::valueOf).collect(Collectors.joining(",")));
                systemMessage.setUserId(Objects.isNull(messageResult.getUserId())?0L:Long.valueOf(messageResult.getUserId()));
                messageHashMap.put(messageResult.getUuid(),systemMessage);
            }
            systemMessageService.saveBatch(messageHashMap.values());
        } catch (Exception e) {
            log.error("save system message error:{}",e);
        }
    }


    @Override
    public void pushStatisticsEvent(List<MessageResult> messageResults) {
        MessageUtilService.pushStatisticsEvent(messageResults);
    }

    @Async
    @Override
    public void updateMessageCount(String strMessageIdList) {
        List<String> messageIdList = new ArrayList<>();
        //查看是否有job外部输入参数：all代表初始化统计所有，其他代表指定systemmessageId进行统计，逗号分隔
        if(StringUtils.isEmpty(strMessageIdList)){
            messageIdList = MessageUtilService.getStatisticsMessageIdList(this.getMessageType().getValue());
            if(CollectionUtils.isEmpty(messageIdList)){
                return;
            }
        }else{
            //job窗口输入all代表统计所有systemMessageId
            if(strMessageIdList.toLowerCase().equals("all")){
                //初始化统计全部systemMessageId
            }else{
                //逗号分隔指定统计哪些systemMessageId
                final String[] split = strMessageIdList.split(",");
                Collections.addAll(messageIdList,split);
            }
        }
        //计算六个月内开始时间（当前月往前推5个月的1日开始）
        LocalDateTime sixMonthsAgo = DateToolUtil.getMonthBegin(5);
        final Date beginTime = Date.from(sixMonthsAgo.atZone(ZoneId.systemDefault()).toInstant());
        final CountMessageDto countMessageDto = new CountMessageDto()
                .setListSystemMessageId(messageIdList)
                .setBeginDate(beginTime).setEndDate(new Date());
        List<MessagePushResultCountDto> messagePushResultCountDtos = userSystemMessageService.countPushResultNum(countMessageDto);

        // 定义每批次的大小
        int batchSize = 100;
        // 计算总批次数
        int totalBatches = (int) Math.ceil((double) messagePushResultCountDtos.size() / batchSize);
        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            // 计算当前批次的起始和结束索引
            int fromIndex = batchIndex * batchSize;
            int toIndex = Math.min(fromIndex + batchSize, messagePushResultCountDtos.size());
            // 获取当前批次的子列表
            List<MessagePushResultCountDto> batchList = messagePushResultCountDtos.subList(fromIndex, toIndex);
            // 执行更新操作
            remoteOperationMessageService.updateSysMsgCount(batchList);
            //移除已统计的消息
            final List<String> collect = batchList.stream().map(MessagePushResultCountDto::getSystemMessageId).collect(Collectors.toList());
            MessageUtilService.removeMessageIdList(this.getMessageType().getValue(),collect);
        }
    }
}
