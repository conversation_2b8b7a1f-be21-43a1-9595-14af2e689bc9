package com.chervon.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.message.api.MessageConstant;
import com.chervon.message.api.dto.*;
import com.chervon.message.api.enums.*;
import com.chervon.message.api.vo.MessageVo;
import com.chervon.message.domain.entity.UserDeviceMessage;
import com.chervon.message.mapper.UserDeviceMessageMapper;
import com.chervon.message.service.UserDeviceMessageService;
import com.chervon.message.util.CacheUtil;
import com.chervon.technology.api.dto.FaultMessageResultCountDto;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务接口实现
 *
 * <AUTHOR>
 * @since 2024-07-18 17:10:17
 * @description 用户设备消息
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserDeviceMessageServiceImpl extends ServiceImpl<UserDeviceMessageMapper, UserDeviceMessage> implements UserDeviceMessageService {

    @Override
    public List<MessageVo> getUserLastMessage(LastMessageDto requestDto) {
        if (CollectionUtils.isEmpty(requestDto.getDeviceInfoList())) {
            return Collections.emptyList();
        }
        final List<MessageVo> cacheMessage = CacheUtil.getRedis(MessageConstant.LAST_DEVICE_MESSAGE_PREFIX + requestDto.getUserId());
        if(Objects.nonNull(cacheMessage)){
            return cacheMessage;
        }
        Date date30DaysAgo = Date.from(LocalDateTime.now().minusMonths(3).atZone(ZoneId.systemDefault()).toInstant());
        List<String> listDeviceId = requestDto.getDeviceInfoList().parallelStream().map(DeviceInfoDto::getDeviceId).collect(Collectors.toList());
        final List<UserDeviceMessage> userDeviceMessages = baseMapper.queryDeviceLastMessage(requestDto.getUserId(), listDeviceId, date30DaysAgo);
        if(CollectionUtils.isEmpty(userDeviceMessages)){
            return Collections.emptyList();
        }
        List<MessageVo> listResult = new ArrayList<>();
        for (UserDeviceMessage lastMessage : userDeviceMessages) {
            MessageVo messageVo = ConvertUtil.convert(lastMessage, MessageVo.class);
            messageVo.setProductId(lastMessage.getProductId().toString());
            messageVo.setMessageType(MessageTypeEnum.DEVICE_MSG.getValue());
            listResult.add(messageVo);
        }
        CacheUtil.putRedisExpire(MessageConstant.LAST_DEVICE_MESSAGE_PREFIX + requestDto.getUserId(), listResult, 300L);
        return listResult;
    }

    @Override
    public boolean checkUserUnReadMessage(LastMessageDto requestDto) {
        if(CollectionUtils.isEmpty(requestDto.getDeviceInfoList())){
            return false;
        }
        Date date30DaysAgo = Date.from(LocalDateTime.now().minusMonths(3).atZone(ZoneId.systemDefault()).toInstant());
        final List<String> listDeviceId = requestDto.getDeviceInfoList().stream().map(a -> a.getDeviceId()).collect(Collectors.toList());
        LambdaQueryWrapper<UserDeviceMessage> countWrapper=new LambdaQueryWrapper<UserDeviceMessage>()
                .eq(UserDeviceMessage::getUserId,requestDto.getUserId())
                .in(!CollectionUtils.isEmpty(listDeviceId),UserDeviceMessage::getDeviceId,listDeviceId)
                .ge(UserDeviceMessage::getCreateTime, date30DaysAgo)
                .eq(UserDeviceMessage::getAppShow, AppShowEnum.YES.getType())
                .eq(UserDeviceMessage::getIfRead, ReadFlagEnum.NO.getType())
                .eq(UserDeviceMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType());
        final long count = count(countWrapper);
        return count>0?true:false;
    }

    /**
     * 更新消息为已读
     * @param requestDto
     * @return
     */
    @Override
    public boolean updateMessageRead(MessageEditDto requestDto){
        if(Objects.isNull(requestDto.getId()) && CollectionUtils.isEmpty(requestDto.getListUuid()) &&
                StringUtils.isEmpty(requestDto.getUuid()) && StringUtils.isEmpty(requestDto.getSystemMessageId())){
            return false;
        }
        CacheUtil.removeRedis(MessageConstant.LAST_DEVICE_MESSAGE_PREFIX + requestDto.getUserId());
        LambdaUpdateWrapper<UserDeviceMessage> updateWrapper=new LambdaUpdateWrapper<UserDeviceMessage>()
                .eq(!Objects.isNull(requestDto.getId()),UserDeviceMessage::getId,requestDto.getId())
                .eq(UserDeviceMessage::getUserId,requestDto.getUserId())
                .eq(UserDeviceMessage::getUuid,requestDto.getUuid())
                .in(!CollectionUtils.isEmpty(requestDto.getListUuid()), UserDeviceMessage::getUuid,requestDto.getListUuid())
                .eq(StringUtils.isNotEmpty(requestDto.getDeviceId()),UserDeviceMessage::getDeviceId,requestDto.getDeviceId())
                .eq(StringUtils.isNotEmpty(requestDto.getSystemMessageId()),UserDeviceMessage::getSystemMessageId,requestDto.getSystemMessageId())
                .eq(UserDeviceMessage::getIfRead, ReadFlagEnum.NO.getType())
                .eq(UserDeviceMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType())
                .set(UserDeviceMessage::getIfRead, ReadFlagEnum.YES.getType());
        return update(updateWrapper);
    }

    @Override
    public PageResult<MessageVo> getPageList(SearchMessageInfoDto pageRequest) {
        if (StringUtils.isEmpty(pageRequest.getDeviceId())) {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"pageRequest.deviceId");
        }
        //构建查询条件
        LambdaQueryWrapper<UserDeviceMessage> queryWrapper=new LambdaQueryWrapper<UserDeviceMessage>()
                .eq(UserDeviceMessage::getUserId,pageRequest.getUserId())
                .eq(UserDeviceMessage::getDeviceId,pageRequest.getDeviceId())
                .lt(!Objects.isNull(pageRequest.getCreateTime()),UserDeviceMessage::getCreateTime,pageRequest.getCreateTime())
                .eq(UserDeviceMessage::getAppShow, AppShowEnum.YES.getType())
                .eq(UserDeviceMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType())
                .orderByDesc(UserDeviceMessage::getCreateTime);
        //分页请求对象
        Page<UserDeviceMessage> pageReq = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        //分页查询
        final Page<UserDeviceMessage> page = this.page(pageReq, queryWrapper);
        //构建返回结果
        PageResult<MessageVo> pageResult = new PageResult<>(pageRequest.getPageNum(), pageRequest.getPageSize(), page.getTotal());
        pageResult.setPages(page.getPages());
        //返回对象类型转换
        final List<MessageVo> messageVoList = convertToResultList(page.getRecords());
        pageResult.setList(messageVoList);
        return pageResult;
    }
    /**
     * 根据综合查询条件查询分页消息列表
     * @param pageRequest 搜索对象
     * @return
     */
    @Override
    public PageResult<MessageVo> SearchPageList(SearchMessageDto pageRequest) {
        //构建查询条件
        final LambdaQueryWrapper<UserDeviceMessage> queryWrapper = buildQueryCondition(pageRequest);
        //分页请求对象
        Page<UserDeviceMessage> pageReq = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        //分页查询
        final Page<UserDeviceMessage> page = this.page(pageReq, queryWrapper);
        //构建返回结果
        PageResult<MessageVo> pageResult = new PageResult<>(pageRequest.getPageNum(),
                pageRequest.getPageSize(), page.getTotal());
        pageResult.setPages(page.getPages());
        final List<MessageVo> messageVoList = convertToResultList(page.getRecords());
        pageResult.setList(messageVoList);
        return pageResult;
    }

    /**
     * 查询消息列表（不分页）
     * @param searchMessageDto 搜索对象
     * @return
     */
    @Override
    public List<MessageVo> getList(SearchMessageDto searchMessageDto) {
        //构建查询条件
        final LambdaQueryWrapper<UserDeviceMessage> queryWrapper = buildQueryCondition(searchMessageDto);
        //执行查询
        final List<UserDeviceMessage> list = this.list(queryWrapper);
        return convertToResultList(list);
    }

    @Override
    public List<FaultMessageResultCountDto> countPushResultNum(CountMessageDto countMessageDto) {
        return this.baseMapper.countPushResultNum(countMessageDto);
    }

    @NotNull
    private static List<MessageVo> convertToResultList(List<UserDeviceMessage> list) {
        List<MessageVo> messageVoList = new ArrayList<>();
        //返回对象类型转换
        for (UserDeviceMessage message : list) {
            final MessageVo messageVo = BeanCopyUtils.copy(message, MessageVo.class);
            messageVo.setPushType(PushMethodEnum.getValueByPushTypes(message.getPushType()));
            messageVo.setMessageType(MessageTypeEnum.DEVICE_MSG.getValue());
            messageVo.setPushResult(message.getPushResult().equals(PushResultEnum.SUCCESS.getType())?true:false);
            messageVo.setPayloadData(message.getPayloadData());
            messageVoList.add(messageVo);
        }
        return messageVoList;
    }

    private static LambdaQueryWrapper<UserDeviceMessage> buildQueryCondition(SearchMessageDto pageRequest) {
        LambdaQueryWrapper<UserDeviceMessage> queryWrapper = new LambdaQueryWrapper<UserDeviceMessage>()
                .eq(UserDeviceMessage::getSystemMessageId, pageRequest.getSystemMessageId())
                .eq(UserDeviceMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType())
                .eq(StringUtils.isNotEmpty(pageRequest.getUuid()), UserDeviceMessage::getUuid, pageRequest.getUuid())
                .eq(StringUtils.isNotEmpty(pageRequest.getTitle()), UserDeviceMessage::getTitle, pageRequest.getTitle())
                .eq(!Objects.isNull(pageRequest.getPushType()), UserDeviceMessage::getPushType, pageRequest.getPushType())
                .eq(!Objects.isNull(pageRequest.getPushResult()), UserDeviceMessage::getPushResult, (pageRequest.getPushResult()==null|| pageRequest.getPushResult()==false)?0:1)
                .eq(StringUtils.isNotEmpty(pageRequest.getUserId()), UserDeviceMessage::getUserId, pageRequest.getUserId())
                .in(!CollectionUtils.isEmpty(pageRequest.getUserIds()), UserDeviceMessage::getUserId, pageRequest.getUserIds())
                .orderByDesc(UserDeviceMessage::getCreateTime);
        //根据开始结束时间进行过滤
        filterByStartEndTime(pageRequest, queryWrapper);
        return queryWrapper;
    }

    @SneakyThrows
    public static void filterByStartEndTime(SearchMessageDto pageRequest, LambdaQueryWrapper<UserDeviceMessage> queryWrapper) {
        SimpleDateFormat sdfV3 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (StringUtils.isNotEmpty(pageRequest.getCreateStartTime())) {
            Date startDate = sdfV3.parse(pageRequest.getCreateStartTime());
            queryWrapper.ge(StringUtils.isNotEmpty(pageRequest.getCreateStartTime()), UserDeviceMessage::getCreateTime, startDate);
        }
        if (StringUtils.isNotEmpty(pageRequest.getCreateEndTime())) {
            Date endDate = sdfV3.parse(pageRequest.getCreateEndTime());
            queryWrapper.lt(StringUtils.isNotEmpty(pageRequest.getCreateEndTime()), UserDeviceMessage::getCreateTime, endDate);
        }
    }

    /**
     * 消息详情：消息列表跳转详情通过UUID查询，RN首页轮播告警跳转到消息详情用systemMessageId请求
     * @param messageDetail 查询对象
     * @return
     */
    @Override
    public MessageVo getDetail(MessageDetailDto messageDetail) {
        if (StringUtils.isEmpty(messageDetail.getUuid()) && StringUtils.isEmpty(messageDetail.getSystemMessageId())) {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"UUID/systemMessageId");
        }
        //构建查询条件
        LambdaQueryWrapper<UserDeviceMessage> queryWrapper=new LambdaQueryWrapper<UserDeviceMessage>()
                .eq(StringUtils.isNotEmpty(messageDetail.getSystemMessageId()),UserDeviceMessage::getSystemMessageId,messageDetail.getSystemMessageId())
                .eq(StringUtils.isNotEmpty(messageDetail.getUuid()),UserDeviceMessage::getUuid,messageDetail.getUuid())
                .eq(UserDeviceMessage::getUserId,messageDetail.getUserId())
                .eq(StringUtils.isNotEmpty(messageDetail.getDeviceId()),UserDeviceMessage::getDeviceId,messageDetail.getDeviceId())
                .eq(UserDeviceMessage::getAppShow, AppShowEnum.YES.getType())
                .eq(UserDeviceMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType())
                .last("limit 1");
        //查询结果
        List<UserDeviceMessage> list = this.list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        UserDeviceMessage message=list.get(0);
        final MessageVo messageVo = BeanCopyUtils.copy(message, MessageVo.class);
        messageVo.setProductId(message.getProductId().toString());
        messageVo.setMessageType(MessageTypeEnum.DEVICE_MSG.getValue());
        if(message.getIfRead().equals(ReadFlagEnum.NO.getType())){
            Boolean success = updateMessageRead(new MessageEditDto()
                    .setUuid(message.getUuid())
                    .setUserId(messageDetail.getUserId())
                    .setDeviceId(messageDetail.getDeviceId()));
            if(success){
                messageVo.setIfRead(ReadFlagEnum.YES.getType());
            }
        }
        return messageVo;
    }

    @Override
    public void delete(MessageDetailDto deleteDetail) {
        if (StringUtils.isEmpty(deleteDetail.getDeviceId())) {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"deleteDetail.deviceId");
        }
        //构建更新条件
        LambdaUpdateWrapper<UserDeviceMessage> updateWrapper=new LambdaUpdateWrapper<UserDeviceMessage>()
                .eq(UserDeviceMessage::getUserId,deleteDetail.getUserId())
                .eq(UserDeviceMessage::getDeviceId,deleteDetail.getDeviceId())
                .eq(UserDeviceMessage::getUuid,deleteDetail.getUuid())
                .eq(UserDeviceMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType())
                .set(UserDeviceMessage::getIsDeleted, DeletedFlagEnum.DELETED.getType());
        //执行更新操作
        this.update(updateWrapper);
        //清除缓存
        CacheUtil.removeRedis(MessageConstant.LAST_DEVICE_MESSAGE_PREFIX + deleteDetail.getUserId());
    }

    @Async
    @Override
    public void deleteByUserId(Long userId, List<DeviceInfoDto> deviceInfoList) {
        if (!CollectionUtils.isEmpty(deviceInfoList)) {
            deviceInfoList.forEach(deviceInfo -> {
                //构建更新条件
                LambdaUpdateWrapper<UserDeviceMessage> updateWrapper=new LambdaUpdateWrapper<UserDeviceMessage>()
                        .eq(UserDeviceMessage::getUserId,userId)
                        .eq(UserDeviceMessage::getDeviceId,deviceInfo.getDeviceId())
                        .eq(UserDeviceMessage::getIsDeleted, DeletedFlagEnum.NORMAL.getType())
                        .set(UserDeviceMessage::getIsDeleted, DeletedFlagEnum.DELETED.getType());
                //执行更新操作
                this.update(updateWrapper);
            });
        }
    }
}