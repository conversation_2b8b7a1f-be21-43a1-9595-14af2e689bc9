package com.chervon.message.service.pushHandler;

import com.chervon.common.core.utils.SpringUtils;
import com.chervon.message.api.dto.MessageDto;
import com.chervon.message.api.enums.PushTypeHandlerEnum;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 消息推送工厂（根据消息类型获取对应处理器实例）
 * 可以根据消息类型获取多种服务实例
 * <AUTHOR> 2024/8/16
 */
public class PushFactory {
    private static ConcurrentHashMap<PushTypeHandlerEnum,IPushHandler> pushHandlerMap =new ConcurrentHashMap<>();
    private synchronized static void init() {
        final Map<String, IPushHandler> beansOfType = SpringUtils.getBeansOfType(IPushHandler.class);
        beansOfType.values().stream().forEach(bean-> pushHandlerMap.put(bean.getPushType(),bean));
    }

    public static IPushHandler getInstance(PushTypeHandlerEnum pushTypeHandlerEnum){
        if(CollectionUtils.isEmpty(pushHandlerMap)){
            init();
        }
        return pushHandlerMap.get(pushTypeHandlerEnum);
    }

    public static List<PushTypeHandlerEnum> getPushTypeByMessage(List<MessageDto> list){
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        HashSet<Integer> set=new HashSet<>();
        for(MessageDto messageDto:list){
            set.addAll(messageDto.getPushTypes());
        }
        if(set.size()==0){
            return Collections.emptyList();
        }
        List<PushTypeHandlerEnum> listEnum=new ArrayList<>();
        for (Integer i : set) {
            listEnum.add(PushTypeHandlerEnum.getByPushType(i));
        }
        return listEnum.stream().sorted(Comparator.comparing(PushTypeHandlerEnum::getPriority)).collect(Collectors.toList());
    }

}
