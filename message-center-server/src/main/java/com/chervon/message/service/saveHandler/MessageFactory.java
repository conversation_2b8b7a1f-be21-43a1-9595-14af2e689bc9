package com.chervon.message.service.saveHandler;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.SpringUtils;
import com.chervon.message.api.enums.MessageTypeEnum;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 消息实例工厂（根据消息类型获取对应消息保存处理器实例）
 * 可以根据消息类型获取多种服务实例
 * <AUTHOR> 2024/8/16
 */
public class MessageFactory {
    private static Map<Integer, IMessageHandler> messageServiceMap=new HashMap<>();

    public static IMessageHandler getMessageInstance(Integer messageType){
        if(CollectionUtils.isEmpty(messageServiceMap)){
            final Map<String, IMessageHandler> beansOfType = SpringUtils.getBeansOfType(IMessageHandler.class);
            beansOfType.values().stream().forEach(bean-> messageServiceMap.put(bean.getMessageType().getValue(),bean));
        }
        final IMessageHandler IMessageHandler = messageServiceMap.get(messageType);
        if(Objects.isNull(IMessageHandler)){
            final String messageTypeName = MessageTypeEnum.valueOfStatus(messageType);
            throw new ServiceException(ErrorCode.DATA_NOT_FOUND,"[bean instance:"+messageTypeName+"]");
        }
        return IMessageHandler;
    }

}
