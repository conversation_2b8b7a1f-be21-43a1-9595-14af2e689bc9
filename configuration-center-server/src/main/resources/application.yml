# Tomcat
server:
  port: 10004

# Spring
spring:
  application:
    # 应用名称
    name: configuration-center
  main:
    allow-bean-definition-overriding: true

--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      # server-addr: ${NACOS_SERVER_ADDR}
      discovery:
        # 注册组
        group: IOT_GROUP
        namespace: iot
      config:
        # 配置组
        group: IOT_GROUP
        namespace: iot
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:${spring.application.name}.yml












