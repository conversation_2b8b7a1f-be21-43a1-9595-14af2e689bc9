package com.chervon.configuration.service.impl;

import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.nacos.api.exception.NacosException;
import com.chervon.common.core.constant.StringPool;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.i18n.config.MessageConfig;
import com.chervon.configuration.api.exception.ConfigurationErrorCode;
import com.chervon.configuration.config.AppRnProperties;
import com.chervon.configuration.config.Constant;
import com.chervon.configuration.config.ExceptionMessageUtil;
import com.chervon.configuration.entity.MultiLanguageRead;
import com.chervon.configuration.req.StaticMultiLanguageExportDto;
import com.chervon.configuration.resp.StaticMultiLanguageBo;
import com.chervon.configuration.service.StaticMultiLanguageService;
import com.chervon.configuration.util.ExportUtil;
import com.chervon.configuration.util.FileUtils;
import com.chervon.configuration.util.NacosConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/8
 * @desc 静态多语言服务实现类
 */
@Service
@Slf4j
public class StaticMultiLanguageServiceImpl implements StaticMultiLanguageService {

    @Autowired
    private NacosConfigProperties nacosConfigProperties;

    @Autowired
    private AppRnProperties appRnProperties;

    @Autowired
    private MessageConfig messageConfig;

    public static final String LINE_BREAK = "\\r?\\n";

    @Override
    public void importMultiLanguage(List<MultiLanguageRead> data) {
        if (Objects.isNull(data)) {
            log.warn("import data is null !");
        }
        String[] appMultiFileName = appRnProperties.getAppMultiFileName().split("\\.");
        String[] rnMultiFileName = appRnProperties.getRnMultiFileName().split("\\.");

        // app 和 rn 多语言数据分别进行分组
        Map<String, List<MultiLanguageRead>> dataGroup = data.stream()
                .collect(Collectors.groupingBy(MultiLanguageRead::getSysCode));
        for (Map.Entry<String, List<MultiLanguageRead>> entry : dataGroup.entrySet()) {
            // app 多语言导入处理
            if (entry.getKey().equals(Constant.APP)) {
                importData(dataGroup, Constant.APP, appMultiFileName);
            }
            // rn 多语言导入处理
            if (entry.getKey().equals(Constant.RN)) {
                importData(dataGroup, Constant.RN, rnMultiFileName);
            }
            // dataGroup包含除app和rn之外的key，则默认如下多语言导入处理
            if (!entry.getKey().equals(Constant.APP) && !entry.getKey().equals(Constant.RN)) {
                String[] fileName = {entry.getKey(), "properties"};
                importData(dataGroup, entry.getKey(), fileName);
            }
        }
    }

    @Override
    public boolean exportMultiLanguage(HttpServletResponse response, StaticMultiLanguageExportDto req) {
        try {
            String sysCode = req.getSysCode();
            List<String> langList = req.getLang();
            List<String> panels = req.getPanels();
            if (Objects.isNull(langList) || langList.isEmpty()) {
                langList = Arrays.asList(messageConfig.getLangList().split(","));
            }
            // 过滤掉en和zh
            langList = langList.stream()
                    .filter(i -> !"en".equals(i))
                    .filter(j -> !"zh".equals(j))
                    .collect(Collectors.toList());
            // 拼接本地缓存多语言文件路径
            String path = System.getProperty("user.dir") + File.separator + messageConfig.getBaseFolder() + File.separator;
            StringBuilder fileName;
            String localFile;
            String delimiter = StringPool.UNDERSCORE;
            Map<String, List<StaticMultiLanguageBo>> appDataMap = null;
            Map<String, Map<String, Map<String, String>>> rnDataMap = null;
            if (StringUtils.isNotEmpty(sysCode) && Arrays.asList(Constant.ANDROID, Constant.IOS).contains(sysCode)) {
                fileName = new StringBuilder(appRnProperties.getAppMultiFileName().split("\\.")[0])
                        .append(delimiter).append("%s").append(StringPool.DOT)
                        .append(appRnProperties.getAppMultiFileName().split("\\.")[1]);
                localFile = path + fileName;
                appDataMap = getLanguageList(langList, localFile);
            } else if (StringUtils.isNotEmpty(sysCode) && sysCode.equals(Constant.RN)) {
                fileName = new StringBuilder(appRnProperties.getRnMultiFileName().split("\\.")[0])
                        .append(delimiter).append("%s").append(StringPool.DOT)
                        .append(appRnProperties.getRnMultiFileName().split("\\.")[1]);
                localFile = path + fileName;
                if (Objects.isNull(panels) || panels.isEmpty()) {
                    rnDataMap = getRnPanelLanguageList(langList, localFile);
                } else {
                    rnDataMap = getRnPanelLanguageList(langList, localFile, panels);
                }
            } else {
                log.warn("Invalid sysCode parameter!");
                return false;
            }
            // 导出文件名列表
            List<String> files = new ArrayList<>();
            // 存储导出文件的临时文件夹
            String tmpPath = path + "tmp" + File.separator;
            File tmpDir = new File(tmpPath);
            boolean isDirectoryCreated = tmpDir.exists();
            if (!isDirectoryCreated) {
                isDirectoryCreated = tmpDir.mkdirs();
            }
            ExportUtil<StaticMultiLanguageBo> utils = new ExportUtil<>();
            if (isDirectoryCreated) {
                switch (sysCode) {
                    case Constant.ANDROID:
                        for (Map.Entry<String, List<StaticMultiLanguageBo>> entry : Objects.requireNonNull(appDataMap).entrySet()) {
                            String lang = entry.getKey();
                            String file = sysCode + delimiter + lang + ".xml";
                            files.add(file);
                            utils.exportToXml(tmpPath, file, entry.getValue(), "code", "content");
                        }
                        break;
                    case Constant.IOS:
                        for (Map.Entry<String, List<StaticMultiLanguageBo>> entry : Objects.requireNonNull(appDataMap).entrySet()) {
                            String lang = entry.getKey();
                            String file = sysCode + delimiter + lang + ".geojson";
                            files.add(file);
                            utils.exportToJson(tmpPath, file, entry.getValue(), "code", "content");
                        }
                        break;
                    case Constant.RN:
                        for (Map.Entry<String, Map<String, Map<String, String>>> entry : Objects.requireNonNull(rnDataMap).entrySet()) {
                            String panel = entry.getKey();
                            String file = panel + ".json";
                            files.add(file);
                            utils.exportToJson2(tmpPath, file, entry.getValue());
                        }
                        break;
                    default:
                        break;
                }
                String file = sysCode + "_export.zip";
                File zipFilePath = new File(tmpPath + file);
                boolean zip = FileUtils.zipFiles(tmpPath, files, zipFilePath);
                if (zip) {
                    FileUtils.fileDownload(response, zipFilePath, Constant.ZIP);
                }
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean exportMultiLanguageExcel(HttpServletResponse response, StaticMultiLanguageExportDto req) {
        try {
            String sysCode = req.getSysCode();
            List<String> langList = req.getLang();
            List<String> panels = req.getPanels();
            if (Objects.isNull(langList) || langList.isEmpty()) {
                langList = Arrays.asList(messageConfig.getLangList().split(","));
            }
            // 过滤掉en和zh
            langList = langList.stream()
                    .filter(i -> !"en".equals(i))
                    .filter(j -> !"zh".equals(j))
                    .collect(Collectors.toList());
            // 拼接本地缓存多语言文件路径
            String path = System.getProperty("user.dir") + File.separator + messageConfig.getBaseFolder() + File.separator;
            StringBuilder fileName;
            String localFile;
            String delimiter = StringPool.UNDERSCORE;
            Map<String, List<StaticMultiLanguageBo>> appDataMap = null;
            Map<String, List<StaticMultiLanguageBo>> rnDataMap = null;
            if (StringUtils.isNotEmpty(sysCode) && sysCode.equalsIgnoreCase(Constant.APP)) {
                fileName = new StringBuilder(appRnProperties.getAppMultiFileName().split("\\.")[0])
                        .append(delimiter).append("%s").append(StringPool.DOT)
                        .append(appRnProperties.getAppMultiFileName().split("\\.")[1]);
                localFile = path + fileName;
                appDataMap = getLanguageList(langList, localFile);
            } else if (StringUtils.isNotEmpty(sysCode) && sysCode.equalsIgnoreCase(Constant.RN)) {
                fileName = new StringBuilder(appRnProperties.getRnMultiFileName().split("\\.")[0])
                        .append(delimiter).append("%s").append(StringPool.DOT)
                        .append(appRnProperties.getRnMultiFileName().split("\\.")[1]);
                localFile = path + fileName;
                if (Objects.isNull(panels) || panels.isEmpty()) {
                    rnDataMap = getLanguageList(langList, localFile);
                } else {
                    rnDataMap = getRnPanelLanguage(langList, localFile, panels);
                }
            } else {
                appDataMap = getLanguageList(langList, path + appRnProperties.getAppMultiFileName().split("\\.")[0]
                        + delimiter + "%s" + StringPool.DOT + appRnProperties.getAppMultiFileName().split("\\.")[1]);
                if (Objects.isNull(panels) || panels.isEmpty()) {
                    rnDataMap = getLanguageList(langList, path + appRnProperties.getRnMultiFileName().split("\\.")[0]
                            + delimiter + "%s" + StringPool.DOT + appRnProperties.getRnMultiFileName().split("\\.")[1]);
                } else {
                    rnDataMap = getRnPanelLanguage(langList, path + appRnProperties.getRnMultiFileName().split("\\.")[0]
                            + delimiter + "%s" + StringPool.DOT + appRnProperties.getRnMultiFileName().split("\\.")[1], panels);
                }
            }
            Map<String, List<StaticMultiLanguageBo>> dataMap = new HashMap<>(langList.size());
            List<StaticMultiLanguageBo> dataList;
            for (String lang : langList) {
                dataList = new ArrayList<>();
                if (!Objects.isNull(appDataMap) && appDataMap.containsKey(lang)) {
                    dataList.addAll(appDataMap.get(lang));
                }
                if (!Objects.isNull(rnDataMap) && rnDataMap.containsKey(lang)) {
                    dataList.addAll(rnDataMap.get(lang));
                }
                dataMap.put(lang, dataList);
            }

            log.info("langList: {}", langList);
            Map<String, List<Map<String, String>>> result = convertMap(langList, dataMap);
            log.info("result key set: {}", result.keySet());

            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String formattedDate = currentDate.format(formatter);

            // 导出文件名
            String exFile = "language_export_" + formattedDate + ".xlsx";
            // 存储导出文件的临时文件夹
            String tmpPath = path + "tmp" + File.separator;
            File tmpDir = new File(tmpPath);
            boolean isDirectoryCreated = tmpDir.exists();
            if (!isDirectoryCreated) {
                isDirectoryCreated = tmpDir.mkdirs();
            }
            if (isDirectoryCreated) {
                ExportUtil<StaticMultiLanguageBo> utils = new ExportUtil<>();
                // 写文件及导出文件
                utils.exportToExcel(tmpPath, exFile, result);
                FileUtils.fileDownload(response, new File(tmpPath + exFile), Constant.XLSX);
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 描述：Map数据转换
     **/
    public static Map<String, List<Map<String, String>>> convertMap(List<String> otherLanguages, Map<String,
            List<StaticMultiLanguageBo>> originalMap) {
        Map<String, List<Map<String, String>>> result = new LinkedHashMap<>();
        // 按照指定顺序对原始Map的键进行排序
        List<String> sortedKeys = originalMap.keySet().stream()
                .filter(otherLanguages::contains)
                .sorted(Comparator.comparingInt(otherLanguages::indexOf))
                .collect(Collectors.toList());

        for (String key : sortedKeys) {
            List<Map<String, String>> value = originalMap.get(key).stream()
                    .map(bo -> {
                        Map<String, String> map = new HashMap<>();
                        map.put(bo.getCode(), bo.getContent());
                        return map;
                    })
                    .collect(Collectors.toList());
            result.put(key, value);
        }

        return result;
    }


    /**
     * 描述：获取APP多语言数据
     *
     * @param langList 语种列表
     * @return java.util.Map<java.lang.String, java.util.List < com.chervon.configuration.resp.StaticMultiLanguageBo>>
     * @date 2024/3/28 16:35
     **/
    public Map<String, List<StaticMultiLanguageBo>> getLanguageList(List<String> langList, String localFile) {
        Map<String, List<StaticMultiLanguageBo>> resMap = new HashMap<>(langList.size());
        List<StaticMultiLanguageBo> data;
        for (String l : langList) {
            data = new ArrayList<>();
            String file = String.format(localFile, l);
            if (new File(file).exists()) {
                Properties properties = new Properties();
                try (FileInputStream fis = new FileInputStream(file);
                     InputStreamReader isr = new InputStreamReader(fis, StandardCharsets.UTF_8)) {
                    properties.load(isr);
                    for (String key : properties.stringPropertyNames()) {
                        String[] values = properties.getProperty(key).split("\\|");
                        if (values.length == 2) {
                            String value = values[0];
                            StaticMultiLanguageBo languageContentVo = new StaticMultiLanguageBo(key, value);
                            data.add(languageContentVo);
                        }
                    }
                } catch (IOException e) {
                    throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_LOCAL_CACHE_READ_ERROR, file);
                }
            }
            resMap.put(l, data);
        }
        return resMap;
    }

    /**
     * 描述：获取相应RN面板的多语言数据
     *
     * @param langList 语种列表
     * @param panels   面板列表
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.util.Map < java.lang.String, java.lang.String>>>
     * @date 2024/3/28 16:33
     **/
    public Map<String, List<StaticMultiLanguageBo>> getRnPanelLanguage(List<String> langList, String localFile, List<String> panels) {
        Map<String, List<StaticMultiLanguageBo>> resMap = new HashMap<>(langList.size());
        List<StaticMultiLanguageBo> data;
        for (String l : langList) {
            data = new ArrayList<>();
            String file = String.format(localFile, l);
            if (new File(file).exists()) {
                Properties properties = new Properties();
                try (FileInputStream fis = new FileInputStream(file);
                     InputStreamReader isr = new InputStreamReader(fis, StandardCharsets.UTF_8)) {
                    properties.load(isr);
                    for (String p : panels) {
                        for (String key : properties.stringPropertyNames()) {
                            String panel = String.join("_", key.split("_")[0], key.split("_")[1]);
                            String[] values = properties.getProperty(key).split("\\|");
                            if (values.length == 2 && p.equals(panel)) {
                                String value = values[0];
                                StaticMultiLanguageBo languageContentVo = new StaticMultiLanguageBo(p, l, key, value);
                                data.add(languageContentVo);
                            }
                        }
                    }
                } catch (IOException e) {
                    throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_LOCAL_CACHE_READ_ERROR, file);
                }
            }
            resMap.put(l, data);
        }
        return resMap;
    }

    /**
     * 描述：获取全量RN多语言数据
     *
     * @param langList 语种列表
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.util.Map < java.lang.String, java.lang.String>>>
     * @date 2024/3/28 16:33
     **/
    public Map<String, Map<String, Map<String, String>>> getRnPanelLanguageList(List<String> langList, String localFile) {
        List<StaticMultiLanguageBo> data = new ArrayList<>();
        for (String l : langList) {
            String file = String.format(localFile, l);
            if (new File(file).exists()) {
                Properties properties = new Properties();
                try (FileInputStream fis = new FileInputStream(file);
                     InputStreamReader isr = new InputStreamReader(fis, StandardCharsets.UTF_8)) {
                    properties.load(isr);
                    for (String key : properties.stringPropertyNames()) {
                        String panel = String.join("_", key.split("_")[0], key.split("_")[1]);
                        String[] values = properties.getProperty(key).split("\\|");
                        if (values.length == 2) {
                            String value = values[0];
                            StaticMultiLanguageBo languageContentVo = new StaticMultiLanguageBo(panel, l, key, value);
                            data.add(languageContentVo);
                        }
                    }
                } catch (IOException e) {
                    throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_LOCAL_CACHE_READ_ERROR, file);
                }
            }
        }
        return getStringMapMap(data.stream().collect(Collectors.groupingBy(StaticMultiLanguageBo::getPanel,
                Collectors.groupingBy(StaticMultiLanguageBo::getLang))));
    }

    /**
     * 描述：获取相应RN面板的多语言数据
     *
     * @param langList 语种列表
     * @param panels   面板列表
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.util.Map < java.lang.String, java.lang.String>>>
     * @date 2024/3/28 16:33
     **/
    public Map<String, Map<String, Map<String, String>>> getRnPanelLanguageList(List<String> langList, String localFile, List<String> panels) {
        List<StaticMultiLanguageBo> data = new ArrayList<>();
        ;
        for (String l : langList) {
            String file = String.format(localFile, l);
            if (new File(file).exists()) {
                Properties properties = new Properties();
                try (FileInputStream fis = new FileInputStream(file);
                     InputStreamReader isr = new InputStreamReader(fis, StandardCharsets.UTF_8)) {
                    properties.load(isr);
                    for (String p : panels) {
                        for (String key : properties.stringPropertyNames()) {
                            String panel = String.join("_", key.split("_")[0], key.split("_")[1]);
                            String[] values = properties.getProperty(key).split("\\|");
                            if (values.length == 2 && p.equals(panel)) {
                                String value = values[0];
                                StaticMultiLanguageBo languageContentVo = new StaticMultiLanguageBo(p, l, key, value);
                                data.add(languageContentVo);
                            }
                        }
                    }
                } catch (IOException e) {
                    throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_LOCAL_CACHE_READ_ERROR, file);
                }
            }
        }
        return getStringMapMap(data.stream().collect(Collectors.groupingBy(StaticMultiLanguageBo::getPanel,
                Collectors.groupingBy(StaticMultiLanguageBo::getLang))));
    }

    /**
     * 描述：将要导出的RN数据进行转换
     *
     * @param map RN数据
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.util.Map < java.lang.String, java.lang.String>>>
     * @date 2024/3/28 16:31
     **/
    private static Map<String, Map<String, Map<String, String>>> getStringMapMap(Map<String, Map<String, List<StaticMultiLanguageBo>>> map) {
        Map<String, Map<String, Map<String, String>>> outputMap = new HashMap<>(map.size());
        for (Map.Entry<String, Map<String, List<StaticMultiLanguageBo>>> entry : map.entrySet()) {
            String outerKey = entry.getKey();
            Map<String, Map<String, String>> innerMap = new HashMap<>(entry.getValue().size());
            for (Map.Entry<String, List<StaticMultiLanguageBo>> innerEntry : entry.getValue().entrySet()) {
                String innerKey = innerEntry.getKey();
                Map<String, String> finalMap = new HashMap<>(innerEntry.getValue().size());
                for (StaticMultiLanguageBo bo : innerEntry.getValue()) {
                    finalMap.put(bo.getCode(), bo.getContent());
                }
                innerMap.put(innerKey, finalMap);
            }
            outputMap.put(outerKey, innerMap);
        }
        return outputMap;
    }

    public void importMultiLanguage(String sysCode, String[] multiFileName, List<String> dataList, String lang) {
        String dataId = multiFileName[0] + StringPool.UNDERSCORE + lang + StringPool.DOT + multiFileName[1];
        try {
            NacosConfigUtils.updatePropertiesConfig(nacosConfigProperties.getServerAddr(),
                    nacosConfigProperties.getNamespace(), dataId, nacosConfigProperties.getGroup(), dataList);
        } catch (NacosException e) {
            throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_IMPORT_UPDATE_ERROR, sysCode, lang);
        }
    }

    public String replaceLineBreak(String str) {
        return str.replaceAll(LINE_BREAK, "");
    }

    /**
     * 描述：数据导入
     *
     * @param dataGroup 导入数据
     * @date 2024/3/28 16:29
     **/
    public void importData(Map<String, List<MultiLanguageRead>> dataGroup, String sysCode, String[] multiFileName) {
        List<MultiLanguageRead> languages = dataGroup.get(sysCode);
        List<String> englishUsList = languages.stream().filter(i -> !i.getEnglishUs().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getEnglishUs()))).collect(Collectors.toList());
        if (!englishUsList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, englishUsList, "en-US");
        }
        List<String> englishUkList = languages.stream().filter(i -> !i.getEnglishUk().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getEnglishUk()))).collect(Collectors.toList());
        if (!englishUkList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, englishUkList, "en-GB");
        }
        List<String> germanList = languages.stream().filter(i -> !i.getGerman().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getGerman()))).collect(Collectors.toList());
        if (!germanList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, germanList, "de");
        }
        List<String> spanishList = languages.stream().filter(i -> !i.getSpanish().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getSpanish()))).collect(Collectors.toList());
        if (!spanishList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, spanishList, "es");
        }
        List<String> frenchList = languages.stream().filter(i -> !i.getFrench().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getFrench()))).collect(Collectors.toList());
        if (!frenchList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, frenchList, "fr");
        }
        List<String> italianList = languages.stream().filter(i -> !i.getItalian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getItalian()))).collect(Collectors.toList());
        if (!italianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, italianList, "it");
        }
        List<String> dutchList = languages.stream().filter(i -> !i.getDutch().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getDutch()))).collect(Collectors.toList());
        if (!dutchList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, dutchList, "nl");
        }
        List<String> danishList = languages.stream().filter(i -> !i.getDanish().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getDanish()))).collect(Collectors.toList());
        if (!danishList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, danishList, "dk");
        }
        List<String> finnishList = languages.stream().filter(i -> !i.getFinnish().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getFinnish()))).collect(Collectors.toList());
        if (!finnishList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, finnishList, "fi");
        }
        List<String> swedishList = languages.stream().filter(i -> !i.getSwedish().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getSwedish()))).collect(Collectors.toList());
        if (!swedishList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, swedishList, "se");
        }
        List<String> norwegianList = languages.stream().filter(i -> !i.getNorwegian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getNorwegian()))).collect(Collectors.toList());
        if (!norwegianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, norwegianList, "no");
        }
        List<String> portugueseList = languages.stream().filter(i -> !i.getPortuguese().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getPortuguese()))).collect(Collectors.toList());
        if (!portugueseList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, portugueseList, "pt");
        }
        List<String> russianList = languages.stream().filter(i -> !i.getRussian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getRussian()))).collect(Collectors.toList());
        if (!russianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, russianList, "ru");
        }
        List<String> polishList = languages.stream().filter(i -> !i.getPolish().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getPolish()))).collect(Collectors.toList());
        if (!polishList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, polishList, "pl");
        }
        List<String> czechList = languages.stream().filter(i -> !i.getCzech().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getCzech()))).collect(Collectors.toList());
        if (!czechList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, czechList, "cz");
        }
        List<String> slovakList = languages.stream().filter(i -> !i.getSlovak().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getSlovak()))).collect(Collectors.toList());
        if (!slovakList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, slovakList, "sk");
        }
        List<String> hungarianList = languages.stream().filter(i -> !i.getHungarian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getHungarian()))).collect(Collectors.toList());
        if (!hungarianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, hungarianList, "hu");
        }
        List<String> romanianList = languages.stream().filter(i -> !i.getRomanian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getRomanian()))).collect(Collectors.toList());
        if (!romanianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, romanianList, "ro");
        }
        List<String> slovenianList = languages.stream().filter(i -> !i.getSlovenian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getSlovenian()))).collect(Collectors.toList());
        if (!slovenianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, slovenianList, "sl");
        }
        List<String> lithuanianList = languages.stream().filter(i -> !i.getLithuanian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getLithuanian()))).collect(Collectors.toList());
        if (!lithuanianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, lithuanianList, "lt");
        }
        List<String> latvianList = languages.stream().filter(i -> !i.getLatvian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getLatvian()))).collect(Collectors.toList());
        if (!latvianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, latvianList, "lv");
        }
        List<String> greekList = languages.stream().filter(i -> !i.getGreek().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getGreek()))).collect(Collectors.toList());
        if (!greekList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, greekList, "gr");
        }
        List<String> turkishList = languages.stream().filter(i -> !i.getTurkish().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getTurkish()))).collect(Collectors.toList());
        if (!turkishList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, turkishList, "tr");
        }
        List<String> estonianList = languages.stream().filter(i -> !i.getEstonian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getEstonian()))).collect(Collectors.toList());
        if (!estonianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, estonianList, "et");
        }
        List<String> ukrainianList = languages.stream().filter(i -> !i.getUkrainian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getUkrainian()))).collect(Collectors.toList());
        if (!ukrainianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, ukrainianList, "uk");
        }
        List<String> bulgarianList = languages.stream().filter(i -> !i.getBulgarian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getBulgarian()))).collect(Collectors.toList());
        if (!bulgarianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, bulgarianList, "bg");
        }
        List<String> croatianList = languages.stream().filter(i -> !i.getCroatian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getCroatian()))).collect(Collectors.toList());
        if (!croatianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, croatianList, "hr");
        }
        List<String> georgianList = languages.stream().filter(i -> !i.getGeorgian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getGeorgian()))).collect(Collectors.toList());
        if (!georgianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, georgianList, "ka");
        }
        List<String> serbianList = languages.stream().filter(i -> !i.getSerbian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getSerbian()))).collect(Collectors.toList());
        if (!serbianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, serbianList, "sr");
        }
        List<String> bosnianList = languages.stream().filter(i -> !i.getBosnian().isEmpty())
                .map(i -> i.getLangCode().concat(StringPool.EQUAL)
                        .concat(replaceLineBreak(i.getBosnian()))).collect(Collectors.toList());
        if (!bosnianList.isEmpty()) {
            importMultiLanguage(sysCode, multiFileName, bosnianList, "bs");
        }

    }

}

