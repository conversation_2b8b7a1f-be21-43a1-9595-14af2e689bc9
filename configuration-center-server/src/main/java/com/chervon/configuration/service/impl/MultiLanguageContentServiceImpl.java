package com.chervon.configuration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.common.web.controller.I18nController;
import com.chervon.configuration.config.PageLanguageContent;
import com.chervon.configuration.entity.MultiLanguage;
import com.chervon.configuration.entity.MultiLanguageContent;
import com.chervon.configuration.mapper.MultiLanguageContentMapper;
import com.chervon.configuration.service.MultiLanguageContentService;
import com.chervon.configuration.service.MultiLanguageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 多语言服务实现类
 * <AUTHOR>
 * @date 2022/7/27 17:06
 */
@Service
@Slf4j
public class MultiLanguageContentServiceImpl extends ServiceImpl<MultiLanguageContentMapper, MultiLanguageContent> implements MultiLanguageContentService {
    private static final List<String> listLang = Arrays.asList("zh","en");

    @Autowired
    @Lazy
    private MultiLanguageService multiLanguageService;

    @Autowired
    private MultiLanguageContentMapper multiLanguageContentMapper;

    /**
     * 将页面元素多语言写入缓存
     * @param sysCode 系统code
     */
    public void cachePageContent(String sysCode) {
        List<PageLanguageContent> pageLanguageContents = this.listPageContents(sysCode);
        if (!CollectionUtils.isEmpty(pageLanguageContents)) {
            // 页面元素，按语言存入redis
            Map<String, List<PageLanguageContent>> langMap = pageLanguageContents.stream()
                    .collect(Collectors.groupingBy(content -> content.getSysCode() + ":" + content.getLang()));
            langMap.keySet().forEach(
                    key -> { Map<String, String> dataMap = langMap.get(key).stream().collect(Collectors.toMap(PageLanguageContent::getLangCode, PageLanguageContent::getContent,(k1,k2)->k2));
                        key = "multiLanguage:page:" + key;
                        log.info("页面元素写入redis，key:{}", key);
                        RedisUtils.setCacheMap(key, dataMap);
                    }
            );
        }
    }

    private List<PageLanguageContent> listPageContents(String sysCode) {
        if(StringUtils.hasLength(sysCode)) {
            return multiLanguageContentMapper.listPageContentsBySysCode(sysCode);
        } else {
            return multiLanguageContentMapper.listPageContents();
        }
    }

    private List<MultiLanguageContent> listBySysCode(String sysCode) {
        return multiLanguageContentMapper.listBySysCode(sysCode);
    }

    /**
     * 设置的默认主语种，其他语言为小语种，按英文托底
     */
    private static final List<String> mainLangs = Arrays.asList("zh","en");
    @Override
    public String getByLangIdFromDb(Long langId) {
        final List<MultiLanguageContent> list = list(new LambdaQueryWrapper<MultiLanguageContent>()
                .eq(MultiLanguageContent::getLangId, langId)
                .select(MultiLanguageContent::getContent));
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0).getContent();
        }
        return null;
    }

    /**
     * 根据语言编码获取语言内容(缓存丢失情况下自动从数据库重新加载缓存，除中英文外的小语种按英文托底返回处理)
     * 缓存结构： RedisUtils.setCacheMapValue(e.getLangCode(), e.getLang(), e.getContent());
     * @param langCode 语言编码
     * @param lang 语言
     * @return
     */
    @Override
    public String getByLangCode(String langCode, String lang) {
        if(StringUtils.isEmpty(langCode) || StringUtils.isEmpty(lang)){
            return null;
        }
        String content = RedisUtils.getCacheMapValue(langCode, lang);
        if (!StringUtils.isEmpty(content)) {
            return content;
        }
        final List<MultiLanguageContent> list = list(new LambdaQueryWrapper<MultiLanguageContent>()
                .eq(MultiLanguageContent::getLangCode, langCode)
                .eq(MultiLanguageContent::getLang, lang)
                .select(MultiLanguageContent::getContent));
        if (!CollectionUtils.isEmpty(list)) {
            content = list.get(0).getContent();
            RedisUtils.setCacheMapValue(langCode, lang, content);
            return content;
        }
        boolean isDefaultLang = mainLangs.contains(lang.toLowerCase());
        if (!isDefaultLang) {
            return getByLangCode(langCode, I18nController.DEFAULT_LANGUAGE);
        }
        return null;
    }

    /**
     * 批量根据语言编码获取语言内容(缓存丢失情况下自动从数据库重新加载缓存，除中英文外的小语种按英文托底返回处理)
     * @param langCodes 语言编码列表
     * @param lang 语言
     * @return 多语言内容map
     */
    @Override
    public Map<String, String> batchGetByLangCode(Collection<String> langCodes, String lang) {
        if(CollectionUtils.isEmpty(langCodes) || StringUtils.isEmpty(lang)) {
            return new HashMap<>();
        }
        //读取缓存返回
        Map<String, String> map = new HashMap<>();
        List<String> notFoundByCache = new ArrayList<>();
        for (String langCode : langCodes) {
            if(StringUtils.isEmpty(langCode)){
                map.put(langCode, null);
                continue;
            }
            String content = RedisUtils.getCacheMapValue(langCode, lang);
            if (!StringUtils.isEmpty(content)) {
                map.put(langCode, content);
            }else{
                notFoundByCache.add(langCode);
            }
        }
        if(CollectionUtils.isEmpty(notFoundByCache)){
            return map;
        }
        //缓存未命中，查询数据库重新加载缓存
        final Map<String, String> langCodeMapFromDb = getLangCodeMapFromDb(lang, notFoundByCache);
        List<String> notFoundByLang = new ArrayList<>();
        boolean isDefaultLang = mainLangs.contains(lang.toLowerCase());
        for(String langCode:notFoundByCache){
            final String content = langCodeMapFromDb.get(langCode);
            if(!StringUtils.isEmpty(content)) {
                map.put(langCode, content);
                RedisUtils.setCacheMapValue(langCode, lang, content);
            }else{//指定语言没有对应的多语言内容，查询默认语言的内容
                if (!isDefaultLang) {
                    notFoundByLang.add(langCode);
                }
            }
        }
        //非小语种语言，直接返回，不做英文兜底
        if(isDefaultLang) {
            return map;
        }
        //小语种语言未配置词条，返回英文兜底
        if(!CollectionUtils.isEmpty(notFoundByLang)) {
            final Map<String, String> defaultLangMap = batchGetByLangCode(notFoundByLang, I18nController.DEFAULT_LANGUAGE);
            map.putAll(defaultLangMap);
        }
        return map;
    }

    /**
     * 从数据库加载多语言内容转map
     * @param lang 语言
     * @param langCodes 语言代码集合
     * @return 多语言内容map
     */
    private Map<String,String> getLangCodeMapFromDb(String lang, List<String> langCodes) {
        final List<MultiLanguageContent> languageContentsFromDb = getLanguageContentsFromDb(lang, langCodes);
        if(!CollectionUtils.isEmpty(languageContentsFromDb)) {
            return languageContentsFromDb.stream().collect(Collectors.toMap(a -> a.getLangCode(), MultiLanguageContent::getContent));
        }
        return new HashMap<>();
    }

    /**
     * 从数据库查询多语言内容
     * @param lang 语言
     * @param langCodes 语言代码集合
     * @return 多语言内容集合
     */
    private List<MultiLanguageContent> getLanguageContentsFromDb(String lang, List<String> langCodes) {
        final List<MultiLanguageContent> list = list(new LambdaQueryWrapper<MultiLanguageContent>()
                .in(MultiLanguageContent::getLangCode, langCodes)
                .eq(MultiLanguageContent::getLang, lang)
                .select(MultiLanguageContent::getLangCode,MultiLanguageContent::getContent));
        return list;
    }


}
