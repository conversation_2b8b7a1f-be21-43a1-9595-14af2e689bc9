package com.chervon.configuration.manage;

import cn.hutool.core.text.csv.CsvWriter;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.domain.SingleInfoResp;
import com.chervon.common.core.utils.CsvUtil;
import com.chervon.configuration.api.exception.ConfigurationException;
import com.chervon.configuration.config.ErrorCodeExcel;
import com.chervon.configuration.req.ErrorCodeCreateDto;
import com.chervon.configuration.req.ErrorCodePageDto;
import com.chervon.configuration.req.ErrorCodeUpdateDto;
import com.chervon.configuration.resp.ErrorCodePageVo;
import com.chervon.configuration.resp.ErrorCodeVo;
import com.chervon.configuration.service.ErrorCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/8 14:56
 */
@RestController
@RequestMapping("/m/errorCode")
@AllArgsConstructor
@Api(value = "错误码接口", tags = {"错误码接口"})
@Slf4j
public class ErrorCodeManage {

    private final ErrorCodeService errorCodeService;

    @ApiOperation(value = "创建错误码")
    @PostMapping("create")
    @Log(businessType = BusinessType.INSERT)
    public void createErrorCode(@RequestBody ErrorCodeCreateDto req) {
        errorCodeService.createErrorCode(req);
    }

    @ApiOperation(value = "修改错误码")
    @PostMapping("update")
    @Log(businessType = BusinessType.EDIT)
    public void updateErrorCode(@RequestBody ErrorCodeUpdateDto req) {
        errorCodeService.updateErrorCode(req);
    }

    @ApiOperation(value = "错误码分页查询")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public IPage<ErrorCodePageVo> page(@RequestBody ErrorCodePageDto req) {
        return errorCodeService.page(req);
    }

    @ApiOperation(value = "错误码详情")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public ErrorCodeVo detail(@RequestBody SingleInfoResp<Long> req) {
        return errorCodeService.detail(req.getInfo());
    }

    @ApiOperation(value = "导出")
    @PostMapping("export")
    @Log(businessType = BusinessType.EXPORT)
    public void export(@RequestBody ErrorCodePageDto req, HttpServletResponse response) throws IOException {
        try {
            List<ErrorCodeExcel> data = errorCodeService.listData(LocaleContextHolder.getLocale().getLanguage(), req);
            data.forEach(e -> {
                e.setCompleteErrorCode(CsvUtil.format(e.getCompleteErrorCode()));
                e.setSysName(CsvUtil.format(e.getSysName()));
                e.setModelName(CsvUtil.format(e.getModelName()));
                e.setTypeName(CsvUtil.format(e.getTypeName()));
                e.setErrorCode(CsvUtil.format(e.getErrorCode()));
                e.setOriginErrorMessage(CsvUtil.format(e.getOriginErrorMessage()));
                e.setLangId(CsvUtil.format(e.getLangId()));
                e.setErrorMessage(CsvUtil.format(e.getErrorMessage()));
                e.setCreateBy(CsvUtil.format(e.getCreateBy()));
                e.setCreateTime(CsvUtil.format(e.getCreateTime()));
                e.setUpdateBy(CsvUtil.format(e.getUpdateBy()));
                e.setUpdateTime(CsvUtil.format(e.getUpdateTime()));
                e.setRemark(CsvUtil.format(e.getRemark()));
            });
            if (CollectionUtils.isEmpty(data)) {
                data = new ArrayList<>();
                data.add(new ErrorCodeExcel());
            }
            response.setContentType("application/csv");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("ErrorCode-" + new SimpleDateFormat("yyyyMMdd").format(new Date()), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
            // 加上UTF-8文件的标识字符
            response.getWriter().write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
            CsvWriter csvWriter = cn.hutool.core.text.csv.CsvUtil.getWriter(response.getWriter());
            csvWriter.writeBeans(data);
            csvWriter.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载模板失败")));
        }
    }


    @ApiOperation(value = "删除错误码--传入错误码id  errorCodeId")
    @PostMapping("delete")
    @Log(businessType = BusinessType.DELETE)
    public void deleteErrorCode(@RequestBody SingleInfoReq<Long> req) {
        errorCodeService.deleteErrorCode(req.getReq());
    }
}
