package com.chervon.configuration.manage;

import cn.hutool.core.text.csv.CsvWriter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.domain.SingleInfoResp;
import com.chervon.common.core.utils.CsvUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.configuration.api.core.MultiLanguageCreateDto;
import com.chervon.configuration.api.core.MultiLanguageUpdateDto;
import com.chervon.configuration.req.MultiLanguagePageDto;
import com.chervon.configuration.resp.MultiLanguagePageVo;
import com.chervon.configuration.resp.MultiLanguageVo;
import com.chervon.configuration.service.MultiLanguageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.redisson.api.RMap;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/8 14:56
 */
@RestController
@RequestMapping("/m/language")
@AllArgsConstructor
@Api(value = "多语言接口", tags = {"多语言接口"})
@Slf4j
public class MultiLanguageManage {

    private final MultiLanguageService multiLanguageService;

    @ApiOperation(value = "创建多语言")
    @PostMapping("create")
    @Log(businessType = BusinessType.INSERT)
    public void createMultiLanguage(@RequestBody MultiLanguageCreateDto req) {
        multiLanguageService.createMultiLanguage(req);
    }

    @ApiOperation(value = "修改多语言")
    @PostMapping("update")
    @Log(businessType = BusinessType.EDIT)
    public void updateMultiLanguage(@RequestBody MultiLanguageUpdateDto req) {
        multiLanguageService.updateMultiLanguage(req);
    }

    @ApiOperation(value = "多语言分页查询")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public IPage<MultiLanguagePageVo> page(@RequestBody MultiLanguagePageDto req) {
        return multiLanguageService.page(req);
    }

    @ApiOperation(value = "多语言详情")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public MultiLanguageVo detail(@RequestBody SingleInfoResp<Long> req) {
        return multiLanguageService.detail(req.getInfo());
    }

    @ApiOperation(value = "导出")
    @PostMapping("export")
    @Log(businessType = BusinessType.EXPORT)
    public void export(@RequestBody MultiLanguagePageDto req, HttpServletResponse response) throws IOException {
        try {
            List<List<String>> data = multiLanguageService.listData(LocaleContextHolder.getLocale().getLanguage(), req);
            data.forEach(e -> e.replaceAll(CsvUtil::format));
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.toString());
            response.setCharacterEncoding("UTF-8");
            //进行下载
            String fileName = URLEncoder.encode("MultiLanguage-" + new SimpleDateFormat("yyyyMMdd").format(new Date()), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".csv");
            // 加上UTF-8文件的标识字符
            response.getWriter().write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
            CsvWriter csvWriter = cn.hutool.core.text.csv.CsvUtil.getWriter(response.getWriter());
            csvWriter.write(data);
            csvWriter.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }

    @ApiOperation(value = "模板下载")
    @PostMapping("template")
    @Log(businessType = BusinessType.DOWNLOAD)
    public void template(HttpServletResponse response) throws IOException {
        try {
            List<List<String>> data = multiLanguageService.template(LocaleContextHolder.getLocale().getLanguage());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("multiLanguage_template", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE).sheet("sheet1")
                    .doWrite(data);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载模板失败")));
        }
    }

    @ApiOperation(value = "导入多语言")
    @PostMapping("import")
    @Log(businessType = BusinessType.IMPORT)
    public List<String> importMultiLanguage(@RequestParam(value = "file") MultipartFile file) {
        return multiLanguageService.importMultiLanguage(file);
    }

    @ApiOperation(value = "根据sysCode查询所有多语言")
    @PostMapping("listLanguageBySysCode")
    @Log(businessType = BusinessType.VIEW)
    public Map<String, String> listLanguageBySysCode(@RequestBody SingleInfoReq<String> req) {
        if (req != null && StringUtils.isNotBlank(req.getReq())) {
            RMap<String, String> map = RedisUtils.getClient().getMap("multiLanguage:page:" + req.getReq() + ":" + LocaleContextHolder.getLocale().getLanguage());
            if (map != null) {
                return new HashMap<>(map);
            }
        }
        return new HashMap<>();
    }

}
