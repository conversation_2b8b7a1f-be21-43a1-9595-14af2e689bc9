package com.chervon.configuration.req;

import com.chervon.configuration.config.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/7/8 16:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "字典分页查询对象")
public class DictPageDto extends BasePage {

    @ApiModelProperty(value = "字典id")
    private String dictId;

    @ApiModelProperty(value = "字典名称")
    private String dictName;
}
