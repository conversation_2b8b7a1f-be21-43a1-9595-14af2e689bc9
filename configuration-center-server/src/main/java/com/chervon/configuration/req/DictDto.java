package com.chervon.configuration.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/7/8 15:49
 */
@Data
@ApiModel(description = "字典创建、修改对象")
public class DictDto {

    @ApiModelProperty(value = "字典id，创建时为空，修改时必填")
    private Long dictId;

    @ApiModelProperty(value = "字典名称")
    private String dictName;

    @ApiModelProperty(value = "字典描述")
    private String dictDescription;
}
