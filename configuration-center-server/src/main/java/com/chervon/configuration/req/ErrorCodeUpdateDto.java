package com.chervon.configuration.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/7/10 20:04
 */
@Data
@ApiModel(description = "错误码修改对象")
public class ErrorCodeUpdateDto {

    @ApiModelProperty(value = "错误码id")
    private Long errorCodeId;

    @ApiModelProperty(value = "错误码原始信息")
    private String originErrorMessage;

    @ApiModelProperty(value = "错误码默认信息")
    private String defaultErrorMessage;

    @ApiModelProperty(value = "错误码备注")
    private String remark;

    @ApiModelProperty(value = "多语言id")
    private Long langId;

    @ApiModelProperty(value = "系统code")
    private String sysCode;
}
