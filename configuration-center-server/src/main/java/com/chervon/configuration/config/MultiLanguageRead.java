package com.chervon.configuration.config;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/7/29 17:17
 */
@Data
public class MultiLanguageRead {

    @ExcelProperty("多语言ID")
    private String langId;

    @ExcelProperty("多语言code")
    private String langCode;

    @ExcelProperty("ZH")
    private String zh;

    @ExcelProperty("EN")
    private String en;

    @ExcelProperty("JP")
    private String jp;

    @ExcelProperty("FR")
    private String fr;

    @ExcelProperty("所属系统")
    private String sysCode;

    @ExcelProperty("所属页面")
    private String page;

    @ExcelProperty("功能名称")
    private String func;

    @ExcelProperty("元素类型")
    private String elementCode;

    @ExcelProperty("文本属性")
    private String textCode;

    @ExcelProperty("备注")
    private String remark;

}
