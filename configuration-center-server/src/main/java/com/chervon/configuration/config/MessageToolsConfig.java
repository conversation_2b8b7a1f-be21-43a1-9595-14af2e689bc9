package com.chervon.configuration.config;

import com.chervon.common.i18n.util.MessageTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/16 13:59
 * @desc 描述
 */
@Component
public class MessageToolsConfig {

    @Autowired
    MessageTools messageTools;

    @Bean(name = "messageTools")
    public MessageTools get() {
        return messageTools;
    }
}
