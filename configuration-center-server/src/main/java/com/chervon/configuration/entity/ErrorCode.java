package com.chervon.configuration.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/7/8 19:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("error_code")
public class ErrorCode extends BaseDo {

    private String completeErrorCode;

    private String sysCode;

    private String modelCode;

    private String typeCode;

    private String errorCode;

    private String originErrorMessage;

    private Long langId;

    private String remark;
}
