package com.chervon.configuration.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/11 9:54
 */
@Data
public class MultiLanguageRead {

    @ExcelProperty(value = "Lang_Code", index = 0)
    private String langCode;

    @ExcelProperty(value = "Sys_Code", index = 1)
    private String sysCode;

    @ExcelProperty(value = "ZH", index = 2)
    private String  chinese;

    @ExcelProperty(value = "en-US", index = 3)
    private String englishUs;

    @ExcelProperty(value = "en-GB", index = 4)
    private String englishUk;

    @ExcelProperty(value = "DE", index = 5)
    private String german;

    @ExcelProperty(value = "DK", index = 6)
    private String danish;

    @ExcelProperty(value = "ES", index = 7)
    private String spanish;

    @ExcelProperty(value = "FI", index = 8)
    private String finnish;

    @ExcelProperty(value = "FR", index = 9)
    private String french;

    @ExcelProperty(value = "IT", index = 10)
    private String italian;

    @ExcelProperty(value = "NL", index = 11)
    private String dutch;

    @ExcelProperty(value = "NO", index = 12)
    private String norwegian;

    @ExcelProperty(value = "SE", index = 13)
    private String swedish;

    @ExcelProperty(value = "PT", index = 14)
    private String portuguese;

    @ExcelProperty(value = "RU", index = 15)
    private String russian;

    @ExcelProperty(value = "PL", index = 16)
    private String polish;

    @ExcelProperty(value = "CZ", index = 17)
    private String czech;

    @ExcelProperty(value = "SK", index = 18)
    private String slovak;

    @ExcelProperty(value = "HU", index = 19)
    private String hungarian;

    @ExcelProperty(value = "RO", index = 20)
    private String romanian;

    @ExcelProperty(value = "SL", index = 21)
    private String slovenian;

    @ExcelProperty(value = "LT", index = 22)
    private String lithuanian;

    @ExcelProperty(value = "LV", index = 23)
    private String latvian;

    @ExcelProperty(value = "GR", index = 24)
    private String greek;

    @ExcelProperty(value = "TR", index = 25)
    private String turkish;

    @ExcelProperty(value = "ET", index = 26)
    private String estonian;

    @ExcelProperty(value = "UK", index = 27)
    private String ukrainian;

    @ExcelProperty(value = "BG", index = 28)
    private String bulgarian;

    @ExcelProperty(value = "HR", index = 29)
    private String croatian;

    @ExcelProperty(value = "KA", index = 30)
    private String georgian;

    @ExcelProperty(value = "SR", index = 31)
    private String serbian;

    @ExcelProperty(value = "BS", index = 32)
    private String bosnian;


    public String getLangCode() {
        return CsvUtil.unFormat(this.langCode);
    }

    public String getSysCode() {
        return CsvUtil.unFormat(this.sysCode);
    }

    public String getChinese() {
        return CsvUtil.unFormat(this.chinese);
    }

    public String getEnglishUs() {
        return CsvUtil.unFormat(this.englishUs);
    }

    public String getEnglishUk() {
        return CsvUtil.unFormat(this.englishUk);
    }

    public String getGerman() {
        return CsvUtil.unFormat(this.german);
    }

    public String getSpanish() {
        return CsvUtil.unFormat(this.spanish);
    }

    public String getFrench() {
        return CsvUtil.unFormat(this.french);
    }

    public String getItalian() {
        return CsvUtil.unFormat(this.italian);
    }

    public String getDutch() {
        return CsvUtil.unFormat(this.dutch);
    }

    public String getDanish() {
        return CsvUtil.unFormat(this.danish);
    }

    public String getFinnish() {
        return CsvUtil.unFormat(this.finnish);
    }

    public String getSwedish() {
        return CsvUtil.unFormat(this.swedish);
    }

    public String getNorwegian() {
        return CsvUtil.unFormat(this.norwegian);
    }

    public String getPortuguese() {
        return CsvUtil.unFormat(this.portuguese);
    }

    public String getRussian() {
        return CsvUtil.unFormat(this.russian);
    }

    public String getPolish() {
        return CsvUtil.unFormat(this.polish);
    }

    public String getCzech() {
        return CsvUtil.unFormat(this.czech);
    }

    public String getSlovak() {
        return CsvUtil.unFormat(this.slovak);
    }

    public String getHungarian() {
        return CsvUtil.unFormat(this.hungarian);
    }

    public String getRomanian() {
        return CsvUtil.unFormat(this.romanian);
    }

    public String getSlovenian() {
        return CsvUtil.unFormat(this.slovenian);
    }

    public String getLithuanian() {
        return CsvUtil.unFormat(this.lithuanian);
    }

    public String getLatvian() {
        return CsvUtil.unFormat(this.latvian);
    }

    public String getGreek() {
        return CsvUtil.unFormat(this.greek);
    }

    public String getTurkish() {
        return CsvUtil.unFormat(this.turkish);
    }

    public String getEstonian() {
        return CsvUtil.unFormat(this.estonian);
    }

    public String getUkrainian() {
        return CsvUtil.unFormat(this.ukrainian);
    }

    public String getBulgarian() {
        return CsvUtil.unFormat(this.bulgarian);
    }

    public String getCroatian() {
        return CsvUtil.unFormat(this.croatian);
    }

    public String getGeorgian() {
        return CsvUtil.unFormat(this.georgian);
    }

    public String getSerbian() {
        return CsvUtil.unFormat(this.serbian);
    }

    public String getBosnian() {
        return CsvUtil.unFormat(this.bosnian);
    }
}
