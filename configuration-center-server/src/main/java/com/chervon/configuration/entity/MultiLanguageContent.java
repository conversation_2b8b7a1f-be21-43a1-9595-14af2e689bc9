package com.chervon.configuration.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/7/27 16:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("multi_language_content")
public class MultiLanguageContent extends BaseDo {

    private Long langId;

    private String langCode;

    private String lang;

    private String content;

    private String shortContent;
}
