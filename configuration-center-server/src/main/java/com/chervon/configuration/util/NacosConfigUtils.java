package com.chervon.configuration.util;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.chervon.common.core.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/4
 * @desc Nacos配置文件更新工具类
 */
@Slf4j
public class NacosConfigUtils {

    public static void updatePropertiesConfig(String serverAddr, String namespace, String dataId, String group, List<String> dataList) throws NacosException {
        Properties properties = new Properties();
        properties.put("serverAddr", serverAddr);
        properties.put("namespace", namespace);
        ConfigService configService = NacosFactory.createConfigService(properties);
        StringBuilder newContent = new StringBuilder();
        // 读取配置文件并转换为Map
        String currentConfig = configService.getConfig(dataId, group, 5000);
        if (!Objects.isNull(currentConfig)) {
            try {
                Map<String, String> listMap = Arrays.stream(currentConfig.trim().split("\\n"))
                        .filter(l -> !l.isEmpty())
                        .peek(s -> {
                            String[] arr = s.split("=");
                            if (arr.length != CommonConstant.TWO) {
                                log.error("dataId: {}, lang_code: {} ", dataId, s);
                            }
                        })
                        .filter(s -> s.split("=").length == CommonConstant.TWO)
                        .map(s -> s.split("="))
                        .distinct()
                        .collect(Collectors.toMap(
                                arr -> arr[0],
                                arr -> arr[1]));

                // 新内容拼接
                for (String data : dataList) {
                    String key = data.split("=")[0];
                    String value = data.split("=")[1];
                    listMap.put(key, value + "|" + System.currentTimeMillis() / 1000);
                }
                newContent = new StringBuilder(listMap.entrySet().stream()
                        .map(entry -> entry.getKey() + "=" + entry.getValue())
                        .collect(Collectors.joining("\n")));
            } catch (Exception e) {
                log.error("nacos config: {} parse error.", dataId);
                log.info("dataId: {} currentConfig:{}", dataId, currentConfig);
            }
        } else {
            log.info("nacos config: {} not exists, create it.", dataId);
            for (String data : dataList) {
                newContent.append(data).append("|").append(System.currentTimeMillis() / 1000).append("\n");
            }
        }
        // 更新/新建 配置文件
        configService.publishConfig(dataId, group, newContent.toString());

    }
}
