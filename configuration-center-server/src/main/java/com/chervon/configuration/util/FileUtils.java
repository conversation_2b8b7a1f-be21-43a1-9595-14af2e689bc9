package com.chervon.configuration.util;

import cn.hutool.core.net.URLDecoder;
import com.chervon.configuration.config.Constant;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @date 2024/3/14 10:26
 * <AUTHOR>
 **/
@Slf4j
public class FileUtils {

    /**
     * 多文件压缩成 zip
     *
     * @param path    文件路径
     * @param files   文件名列表
     * @param zipFile 文件路径
     */
    public static boolean zipFiles(String path, List<String> files, File zipFile) throws IOException {
        // 判断压缩后的文件是否存在,如果存在则删除
        boolean zipExists = zipFile.exists();
        if (zipExists && !zipFile.delete()) {
            log.error("Failed to delete existing zip file: {}", zipFile.getAbsolutePath());
            return false;
        }

        // 创建新的压缩文件
        zipExists = zipFile.createNewFile();
        if (!zipExists) {
            log.error("Failed to create new zip file: {}", zipFile.getAbsolutePath());
            return false;
        }

        try (FileOutputStream fileOutputStream = new FileOutputStream(zipFile);
             ZipOutputStream zipOutputStream = new ZipOutputStream(fileOutputStream)) {

            // 遍历源文件列表
            for (String file : files) {
                String fileName = URLDecoder.decode(file, StandardCharsets.UTF_8);
                File f = new File(path + File.separator + fileName);
                if (f.exists()) {
                    addFileToZip(zipOutputStream, f, fileName);
                }
            }
        } catch (Exception e) {
            log.error("Failed to zip files", e);
            return false;
        }
        return true;
    }

    private static void addFileToZip(ZipOutputStream zipOutputStream, File file, String fileName) throws IOException {
        try (FileInputStream inputStream = new FileInputStream(file)) {
            // 创建 ZipEntry 对象
            ZipEntry zipEntry = new ZipEntry(fileName);
            zipOutputStream.putNextEntry(zipEntry);

            // 写入文件内容到 ZipOutputStream
            byte[] buffer = new byte[4096];
            int len;
            while ((len = inputStream.read(buffer)) > 0) {
                zipOutputStream.write(buffer, 0, len);
            }

            zipOutputStream.closeEntry();
        }
    }

    /**
     * 文件下载
     *
     * @param response HttpServletResponse
     * @param filePath 待下载文件（包括路径）
     */
    public static void fileDownload(HttpServletResponse response, File filePath, String type) throws IOException {
        if (filePath.exists()) {
            String file = filePath.getName();
            // 目前已知仅下载这两种文件类型
            if (Constant.ZIP.equalsIgnoreCase(type)) {
                response.setContentType("application/octet-stream");
            } else if (Constant.XLSX.equalsIgnoreCase(type)) {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            }
            response.setHeader("Content-disposition", "attachment; filename=" + new String(file.getBytes(StandardCharsets.UTF_8), "ISO8859-1"));
            byte[] buffer = new byte[4096];
            try (FileInputStream fis = new FileInputStream(filePath);
                 BufferedInputStream bis = new BufferedInputStream(fis);
                 OutputStream os = response.getOutputStream()) {
                int i;
                while ((i = bis.read(buffer)) != -1) {
                    os.write(buffer, 0, i);
                }
                // 导出之后，删除文件
                filePath.delete();
            }
        } else {
            log.warn("file not exists !");
        }
    }

}

