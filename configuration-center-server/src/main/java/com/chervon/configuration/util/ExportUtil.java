package com.chervon.configuration.util;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.chervon.configuration.resp.StaticMultiLanguageBo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.sun.xml.txw2.output.IndentingXMLStreamWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import javax.xml.stream.XMLOutputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamWriter;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/1/26
 * @desc 文件导出工具类
 */
@Slf4j
public class ExportUtil<T> {

    /**
     * 导出Xml文件
     *
     * @param path 文件路径
     * @param file 文件名
     * @param list 数据列表
     * @param k 需要导出的实体属性，xml节点的name
     * @param v 需要导出的实体属性，xml节点的value
     */
    public void exportToXml(String path, String file, List<T> list, String k, String v) {
        try {
            // 创建文件路径
            prepareFilePath(path, file);

            // 获取字段值列表
            List<Map<Object, Object>> mapList = getFieldValueList(list);

            // 使用 StAX XMLOutputFactory
            XMLOutputFactory outputFactory = XMLOutputFactory.newInstance();
            try (FileWriter fileWriter = new FileWriter(path + file)) {
                XMLStreamWriter baseWriter = outputFactory.createXMLStreamWriter(fileWriter);
                IndentingXMLStreamWriter writer = new IndentingXMLStreamWriter(baseWriter);
                writer.setIndentStep(StringUtils.repeat(" ", 2));

                // 写入内容
                writeXmlContent(writer, mapList, k, v);

                writer.close();
            }
            log.info("File written successfully at {}", path + file);
        } catch (Exception e) {
            log.error("Failed to write XML file at {}", path + file, e);
        }
    }

    private void prepareFilePath(String path, String file) throws IOException {
        Files.createDirectories(Paths.get(path));
        String fullPath = path + file;
        log.info("Prepared file path: {}", fullPath);
    }


    private void writeXmlContent(XMLStreamWriter writer, List<Map<Object, Object>> mapList, String k, String v) throws XMLStreamException {
        writer.writeStartDocument("UTF-8", "1.0");
        writer.writeStartElement("resources");
        writer.writeNamespace("tools", "http://schemas.android.com/tools");
        for (Map<Object, Object> map : mapList) {
            writeStringElement(writer, map, k, v);
        }
        writer.writeEndElement();
        writer.writeEndDocument();
    }

    private void writeStringElement(XMLStreamWriter writer, Map<Object, Object> map, String k, String v) throws XMLStreamException {
        String name = (String) map.get(k);
        String value = (String) map.get(v);
        if (name != null && value != null) {
            writer.writeStartElement("string");
            writer.writeAttribute("name", name);
            writer.writeCharacters(value);
            writer.writeEndElement();
        }
    }

    /**
     * 导出Json文件
     *
     * @param path 文件路径
     * @param file 文件名
     * @param objectList 数据列表
     * @param k 导出json文件中的key
     * @param v 导出json文件中的value
     */
    public void exportToJson(String path, String file, List<T> objectList, String k, String v) {
        try {
            File f = new File(path + file);
            boolean bol = f.exists();
            if(bol){
                bol = f.delete();
            }
            List<Map<Object, Object>> mapList = getFieldValueList(objectList);
            Map<String, String> dataMap = new HashMap<>(mapList.size());
            for (Map<Object, Object> map : mapList) {
                String key = (String) map.get(k);
                String value = (String) map.get(v);
                dataMap.put(key, value);
            }
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
            objectMapper.writeValue(new File(path + file), dataMap);
        } catch (IOException e) {
            log.error("local file {} write failed", path + file, e);
        }
    }

    /**
     * 导出Json文件
     *
     * @param path 文件路径
     * @param file 文件名
     * @param data 写入数据
     */
    public void exportToJson2(String path, String file, Map<String, Map<String, String>> data) {
        try {
            File f = new File(path + file);
            boolean bol = f.exists();
            if(bol){
                bol = f.delete();
            }
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
            objectMapper.writeValue(new File(path + file), data);
        } catch (IOException e) {
            log.error("local file {} write failed", path + file, e);
        }
    }


    /**
     * 导出excel文件
     *
     * @param path 文件路径
     * @param file 文件名
     * @param data 写入数据
     */
    public void exportToExcel(String path, String file, Map<String, List<Map<String, String>>> data) {
        try (Workbook workbook = new XSSFWorkbook()) {
            File f = new File(path + file);
            if (f.exists() && !f.delete()) {
                log.warn("Failed to delete existing file: {}", f.getAbsolutePath());
            }

            // 确保列顺序与 data.keySet() 保持一致
            List<String> columnOrder = new ArrayList<>(data.keySet());

            Sheet sheet = workbook.createSheet("Data");
            // 创建样式用于加粗并放大字体
            Font font = workbook.createFont();
            font.setBold(true);
            font.setFontHeightInPoints((short) 14);
            CellStyle headerCellStyle = workbook.createCellStyle();
            headerCellStyle.setFont(font);

            // 创建表头行
            Row headerRow = sheet.createRow(0);
            // 第一列是 code
            Cell cell = headerRow.createCell(0);
            cell.setCellValue("lang_code");
            cell.setCellStyle(headerCellStyle);
            // 按 columnOrder 的顺序生成列标题
            int colIndex = 1;
            for (String lang : columnOrder) {
                cell = headerRow.createCell(colIndex++);
                cell.setCellValue(lang);
                cell.setCellStyle(headerCellStyle);
            }
            int rowIndex = 1;
            // 提取所有唯一的 code
            SortedSet<String> uniqueCodes = new TreeSet<>();
            for (List<Map<String, String>> mapList : data.values()) {
                for (Map<String, String> map : mapList) {
                    uniqueCodes.addAll(map.keySet());
                }
            }
            // 遍历 code 并生成数据行
            for (String code : uniqueCodes) {
                Row row = sheet.createRow(rowIndex++);
                // 第一列是 code
                row.createCell(0).setCellValue(code);

                // 按 columnOrder 的顺序填充每列数据
                colIndex = 1;
                for (String lang : columnOrder) {
                    String value = getValueForCode(data.getOrDefault(lang, Collections.emptyList()), code);
                    row.createCell(colIndex++).setCellValue(value != null ? value : "");
                }
            }
            // 导出为 Excel 文件
            try (FileOutputStream fileOut = new FileOutputStream(f)) {
                workbook.write(fileOut);
            }
        } catch (IOException e) {
            log.error("local file {} write failed", path + file, e);
        }
    }


    @SuppressWarnings("unchecked")
    public List<Map<Object, Object>> getFieldValueList(List<T> list) {
        String str = JSONObject.toJSONString(list);
        return (List<Map<Object, Object>>) JSONArray.parse(str);
    }


    /**
     * 根据code从Map列表中获取对应的value
     */
    private static String getValueForCode(List<Map<String, String>> mapList, String code) {
        for (Map<String, String> map : mapList) {
            if (map.containsKey(code)) {
                return map.get(code);
            }
        }
        return null;
    }

}
