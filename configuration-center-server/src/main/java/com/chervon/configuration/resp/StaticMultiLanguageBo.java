package com.chervon.configuration.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/13 15:22
 * @desc 描述
 */
@Data
public class StaticMultiLanguageBo implements Serializable {

    private String panel;

    private String lang;

    private String code;

    private String content;

    public StaticMultiLanguageBo(String panel, String lang, String code, String content) {
        this.panel = panel;
        this.lang = lang;
        this.code = code;
        this.content = content;
    }

    public StaticMultiLanguageBo(String code, String content) {
        this.code = code;
        this.content = content;
    }
}
