package com.chervon.configuration.resp;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.configuration.api.core.MultiLanguageNode;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/10 20:47
 */
@Data
@ApiModel(description = "多语言分页查询出参对象")
public class MultiLanguagePageBo {

    @ApiModelProperty(value = "多语言id")
    private Long multiLanguageId;

    @ApiModelProperty(value = "多语言code")
    private String langCode;

    @ApiModelProperty(value = "多语言系统code")
    private String sysCode;

    @ApiModelProperty(value = "多语言页面名称")
    private String page;

    @ApiModelProperty(value = "多语言功能名称")
    private String func;

    @ApiModelProperty(value = "多语言元素类型code")
    private String elementCode;

    @ApiModelProperty(value = "多语言文本属性code")
    private String textCode;

    @ApiModelProperty(value = "多语言备注")
    private String remark;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    private String lang;

    private String content;
}
