package com.chervon.common.security.config;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.filter.SaServletFilter;
import cn.dev33.satoken.stp.StpUtil;
import com.chervon.common.core.domain.LoginSysUser;
import com.chervon.common.core.domain.LoginUserContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2023/2/20 22:40
 */
@Configuration
public class SaConfiguration implements WebMvcConfigurer {

    @Bean
    public CheckLoginProperties getCheckLoginProperties() {
        return new CheckLoginProperties();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        InterceptorRegistration interceptorRegistration = registry.addInterceptor(new MySaInterceptor(e -> {
            StpUtil.checkLogin();
            Object o = StpUtil.getSession().get(StpUtil.getLoginIdAsString());
            if (o != null) {
                LoginUserContext.setUser((LoginSysUser) o);
            } else {
                LoginUserContext.setUser(new LoginSysUser());
            }
        })).order(-1).addPathPatterns("/**");
        interceptorRegistration.excludePathPatterns("/sso/**");
        if (getCheckLoginProperties().getExcludeUrls() != null) {
            interceptorRegistration.excludePathPatterns(getCheckLoginProperties().getExcludeUrls());
        }
    }

    @Bean
    public SaServletFilter getSaServletFilter() {
        return new SaServletFilter()
                .setBeforeAuth(r -> SaHolder.getResponse()
                        // 服务器名称
                        .setServer("sa-server")
                        // 是否可以在iframe显示视图： DENY=不可以 | SAMEORIGIN=同域下可以 | ALLOW-FROM uri=指定域名下可以
                        .setHeader("X-Frame-Options", "SAMEORIGIN")
                        // 是否启用浏览器默认XSS防护： 0=禁用 | 1=启用 | 1; mode=block 启用, 并在检查到XSS攻击时，停止渲染页面
                        .setHeader("X-XSS-Protection", "1; mode=block")
                        // 禁用浏览器内容嗅探
                        .setHeader("X-Content-Type-Options", "nosniff"));
    }
}
