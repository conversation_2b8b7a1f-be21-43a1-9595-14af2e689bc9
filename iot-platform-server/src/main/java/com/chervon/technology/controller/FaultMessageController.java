package com.chervon.technology.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.iot.app.api.enums.BusinessTypeEnum;
import com.chervon.iot.middle.api.pojo.thingmodel.Event;
import com.chervon.message.api.RemoteMessageService;
import com.chervon.message.api.dto.DeviceMessageDto;
import com.chervon.message.api.dto.SearchMessageDto;
import com.chervon.message.api.vo.MessageSensitiveVo;
import com.chervon.message.api.vo.MessageVo;
import com.chervon.technology.api.toruleengine.FaultMessageAlarmLogAddDto;
import com.chervon.technology.domain.dataobject.FaultMessage;
import com.chervon.technology.domain.dto.MessageResultSearchDto;
import com.chervon.technology.domain.dto.fault.message.*;
import com.chervon.technology.domain.vo.fault.message.FaultMessageAlarmLogVo;
import com.chervon.technology.domain.vo.fault.message.FaultMessageDetailVo;
import com.chervon.technology.domain.vo.fault.message.FaultMessageVo;
import com.chervon.technology.service.FaultMessageAlarmLogService;
import com.chervon.technology.service.FaultMessageService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 故障消息相关接口
 *
 * <AUTHOR>
 * @since 2022-09-09 19:11
 **/
@Api(tags = "告警消息配置相关接口")
@RestController
@RequestMapping("/fault/message")
public class FaultMessageController {
    @Resource
    private FaultMessageService faultMessageService;
    @DubboReference
    private RemoteMessageService remoteMessageService;
    /**
     * 添加故障消息
     *
     * @param faultMessageAddDto 添加故障消息Dto
     * @return 添加结果
     */
    @ApiOperation("添加故障消息")
    @PostMapping("/add")
    @Log(businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody @Validated FaultMessageAddDto faultMessageAddDto) {
        faultMessageService.add(faultMessageAddDto);
        return R.ok();
    }

    /**
     * 编辑故障消息
     *
     * @param faultMessageEditDto 编辑故障消息
     */
    @ApiOperation(value = "编辑故障消息")
    @PostMapping("/edit")
    @Log(businessType = BusinessType.EDIT)
    public R<?> edit(@Validated @RequestBody FaultMessageEditDto faultMessageEditDto) {
        faultMessageService.edit(faultMessageEditDto);
        return R.ok();
    }

    /**
     * 根据消息故障id删除故障消息
     *
     * @param faultMessageDeleteDto 删除消息故障dto
     * @return 返回body
     */
    @ApiOperation("根据消息故障id删除故障消息")
    @PostMapping("/delete")
    @Log(businessType = BusinessType.DELETE)
    public R<?> deleteFaultMessage(@Validated @RequestBody FaultMessageDeleteDto faultMessageDeleteDto) {
        faultMessageService.delete(faultMessageDeleteDto);
        return R.ok();
    }

    /**
     * 编辑告警消息状态
     *
     * @param faultMessageStatusEditDto 编辑状态Dto
     * @return 编辑结果
     */
    @ApiOperation(value = "状态启用停用")
    @PostMapping("/edit/status")
    @Log(businessType = BusinessType.EDIT)
    public R<?> edit(@Validated @RequestBody FaultMessageStatusEditDto faultMessageStatusEditDto) {
        faultMessageService.editStatus(faultMessageStatusEditDto);
        return R.ok();
    }

    /**
     * 告警消息配置分页列表
     *
     * @param faultMessageSearchDto 分页参数
     * @return 返回body
     */
    @ApiOperation("告警消息配置分页列表")
    @PostMapping("/page")
    @Log(businessType = BusinessType.VIEW)
    public R<PageResult<FaultMessageVo>> page(@RequestBody @Validated FaultMessageSearchDto faultMessageSearchDto) {
        return R.ok(faultMessageService.list(faultMessageSearchDto));
    }

    /**
     * 告警记录分页消息
     *
     * @param faultMessageAlarmLogDto 查询条件
     * @return 分页结果
     */
    @ApiOperation("告警记录分页列表")
    @PostMapping("/alarm/log/page")
    @Log(businessType = BusinessType.VIEW)
    public R<PageResult<FaultMessageAlarmLogVo>> faultMessageAlarmLogList(@RequestBody @Validated FaultMessageAlarmLogDto faultMessageAlarmLogDto) {
        return R.ok(faultMessageService.faultMessageAlarmLogList(faultMessageAlarmLogDto));
    }

    /**
     * 告警消息配置详情
     *
     * @param faultMessageIdDto 详情查询参数
     * @return 返回body
     */
    @ApiOperation("告警消息配置详情")
    @PostMapping("/detail")
    @Log(businessType = BusinessType.VIEW)
    public R<FaultMessageDetailVo> detail(@RequestBody @Validated FaultMessageIdDto faultMessageIdDto) {
        return R.ok(faultMessageService.detail(faultMessageIdDto));
    }

    /**
     * 分页获取告警消息推送结果列表(messageType=2)
     *
     * @param dto 搜索条件
     * @return 分页结果
     */

    @ApiOperation("分页获取告警消息推送结果列表(messageType=2)")
    @PostMapping("/message/result/page")
    @Log(businessType = BusinessType.VIEW)
    public R<PageResult<MessageSensitiveVo>> pageMessageResult(@RequestBody @Validated MessageResultSearchDto dto) {
        SearchMessageDto searchMessageDto = ConvertUtil.convert(dto, SearchMessageDto.class);
        if (null != dto.getPushType()) {
            searchMessageDto.setPushType(dto.getPushType().getPushTypes());
        }
        if(null != dto.getBusinessType() && dto.getBusinessType() == BusinessTypeEnum.FLEET.getType()) {
            return R.ok(new PageResult<>(1, dto.getPageSize(), 0));
        }
        PageResult<MessageVo> voPageResult = remoteMessageService.getPushRecordPage(searchMessageDto);
        List<MessageVo> voList = voPageResult.getList();
        PageResult<MessageSensitiveVo> result = new PageResult<>(voPageResult.getPageNum(), voPageResult.getPageSize(), voPageResult.getTotal());
        List<MessageSensitiveVo> resultList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(voList)) {
            voList.forEach(v -> {
                MessageSensitiveVo convert = ConvertUtil.convert(v, MessageSensitiveVo.class);
                convert.setUserId(v.getUserId() != null ? v.getUserId().toString() : null);
                convert.setBusinessType(BusinessTypeEnum.EGO_CONNECT.getType());
                resultList.add(convert);
            });
            result.setList(resultList);
        }
        return R.ok(result);
    }
}
