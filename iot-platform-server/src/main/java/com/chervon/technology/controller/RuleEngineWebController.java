package com.chervon.technology.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.technology.domain.dto.rule.engine.RuleEngineAddDto;
import com.chervon.technology.domain.dto.rule.engine.RuleEngineEditDto;
import com.chervon.technology.domain.dto.rule.engine.RuleEngineIdDto;
import com.chervon.technology.domain.dto.rule.engine.RuleEnginePageDto;
import com.chervon.technology.domain.vo.rule.engine.RuleEngineDetailVo;
import com.chervon.technology.domain.vo.rule.engine.RuleEnginePageVo;
import com.chervon.technology.service.RuleEngineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 给web调用的规则引擎CRUD接口
 *
 * <AUTHOR>
 * @since 2022-09-08 11:48
 **/
@Api(tags = "规则引擎相关接口")
@RestController
@RequestMapping("/web/rule/engine")
public class RuleEngineWebController {
    @Resource
    private RuleEngineService ruleEngineService;

    /**
     * 添加规则引擎
     *
     * @param ruleEngineAddDto 添加规则引擎Dto
     * @return 添加结果
     */
    @ApiOperation("添加规则引擎")
    @PostMapping("/add")
    @Log(businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody @Validated RuleEngineAddDto ruleEngineAddDto) {
        ruleEngineService.add(ruleEngineAddDto);
        return R.ok();
    }

    /**
     * 编辑规则引擎
     *
     * @param ruleEngineEditDto 编辑规则引擎Dto
     * @return 编辑结果
     */
    @ApiOperation("编辑规则引擎")
    @PostMapping("/edit")
    @Log(businessType = BusinessType.EDIT)
    public R<?> edit(@RequestBody @Validated RuleEngineEditDto ruleEngineEditDto) {
        ruleEngineService.edit(ruleEngineEditDto);
        return R.ok();
    }

    /**
     * 删除规则引擎
     *
     * @param ruleEngineIdDto 规则引擎Id
     * @return 删除结果
     */
    @ApiOperation("删除规则引擎")
    @PostMapping("/delete")
    @Log(businessType = BusinessType.DELETE)
    public R<?> delete(@RequestBody @Validated RuleEngineIdDto ruleEngineIdDto) {
        ruleEngineService.delete(ruleEngineIdDto);
        return R.ok();
    }

    /**
     * 获取规则引擎详情
     *
     * @param ruleEngineIdDto 规则引擎Id
     * @return 规则引擎详情Vo
     */
    @ApiOperation("获取规则引擎详情")
    @PostMapping("/detail")
    @Log(businessType = BusinessType.VIEW)
    public R<RuleEngineDetailVo> detail(@RequestBody @Validated RuleEngineIdDto ruleEngineIdDto) {
        return R.ok(ruleEngineService.detail(ruleEngineIdDto));
    }

    /**
     * 分页获取规则引擎列表
     *
     * @param ruleEnginePageDto 查询条件
     * @return 规则引擎分页结果
     */
    @ApiOperation("分页获取规则引擎列表")
    @PostMapping("/page")
    @Log(businessType = BusinessType.VIEW)
    public R<PageResult<RuleEnginePageVo>> page(@RequestBody @Validated RuleEnginePageDto ruleEnginePageDto) {
        return R.ok(ruleEngineService.page(ruleEnginePageDto));
    }

}
