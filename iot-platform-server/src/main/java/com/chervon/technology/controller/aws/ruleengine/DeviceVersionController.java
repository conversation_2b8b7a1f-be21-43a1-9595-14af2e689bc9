package com.chervon.technology.controller.aws.ruleengine;

import com.chervon.common.core.utils.JsonUtils;
import com.chervon.technology.service.DeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Api(tags = "AWS规则引擎接收-设备版本上报")
@RestController
@RequestMapping("/rule")
@Slf4j
public class DeviceVersionController {
    @Autowired
    private DeviceService deviceService;

    /**
     * 上报更新总成版本号
     * 对应的topic：$aws/things/+/shadow/name/componentVersion/update/accepted
     * 规则引擎：*_update_component_version
     *
     * @param deviceId:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    @PostMapping("/job/package/version")
    @ApiOperation("上报更新总成版本号")
    public void updatePackageVersion(@RequestHeader String deviceId,
                                     @RequestBody Map<String, String> versionMap) {
        log.info("url:/job/package/version, deviceId:{}, versionMap:{}", deviceId, JsonUtils.toJsonString(versionMap));
        deviceService.updateFirmwareVersion(deviceId, versionMap);
    }
}
