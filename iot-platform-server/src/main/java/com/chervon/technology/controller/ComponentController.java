package com.chervon.technology.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.domain.dto.component.AddComponentDto;
import com.chervon.technology.domain.dto.component.CheckComponentDto;
import com.chervon.technology.domain.dto.component.EditComponentDto;
import com.chervon.technology.domain.dto.component.SearchComponentDto;
import com.chervon.technology.domain.vo.component.ComponentCheckVo;
import com.chervon.technology.domain.vo.component.ComponentVo;
import com.chervon.technology.service.ComponentService;
import com.chervon.technology.service.ProductComponentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-11
 */
@Api(tags = "总成零件相关接口")
@Slf4j
@RestController
@RequestMapping("/component")
public class ComponentController {

    @Autowired
    private ComponentService componentService;

    @Autowired
    private ProductComponentService productComponentService;

    /**
     * 创建总成零件
     *
     * @param
     */
    @ApiOperation("创建总成零件")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Log(businessType = BusinessType.INSERT)
    public R addComponent(@Validated @RequestBody AddComponentDto addComponent) {
        componentService.addComponent(addComponent);
        return R.ok();
    }

    /**
     * 更新总成零件
     *
     * @param editComponent 更新总成零件信息
     */
    @ApiOperation("编辑总成零件")
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @Log(businessType = BusinessType.EDIT)
    public R editComponent(@Validated @RequestBody EditComponentDto editComponent) {
        componentService.editComponent(editComponent);
        return R.ok();
    }

    /**
     * 根据id删除总成零件
     *
     * @param id 总成零件id
     */
    @ApiOperation("删除总成零件，参数为总成零件Id")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @Log(businessType = BusinessType.DELETE)
    public R deleteComponent(@Validated @RequestBody SingleInfoReq<Long> id) {
        componentService.deleteComponent(id.getReq());
        return R.ok();
    }

    /**
     * 获取总成零件详情
     *
     * @param id 总成零件id
     * @return 返回信息
     */
    @ApiOperation("获取总成零件详情，参数为总成零件Id")
    @RequestMapping(value = "/detail/get", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public R<ComponentVo> getDetail(@Validated @RequestBody SingleInfoReq<Long> id) {
        ComponentVo componentVo = componentService.getDetail(id.getReq());
        if (null == componentVo) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMPONENT_NOT_EXIST, id.getReq());
        }
        return R.ok(componentVo);
    }

    /**
     * 分页获取总成零件信息
     *
     * @param search 搜索总成零件信息
     * @return 结果
     */
    @ApiOperation("分页获取总成零件列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public R<PageResult<ComponentVo>> list(@Validated @RequestBody SearchComponentDto search) {
        return R.ok(componentService.list(search));
    }

    /**
     * 获取所有总成零件
     *
     * @return 结果
     */
    @ApiOperation("获取所有的总成零件")
    @RequestMapping(value = "/all", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public R<List<ComponentVo>> all() {
        return R.ok(componentService.all());
    }

    /**
     * 校验总成零件号，如存在返回总成零件详情，不存在返回null
     *
     * @param checkComponentDto
     * @return 返回信息
     */
    @ApiOperation("校验总成零件号，如存在返回总成零件详情，不存在返回null")
    @RequestMapping(value = "/check", method = RequestMethod.POST)
    @Log(businessType = BusinessType.OTHER)
    public R<ComponentCheckVo> check(@Validated @RequestBody CheckComponentDto checkComponentDto) {
        ComponentCheckVo componentVo = productComponentService.check(checkComponentDto);
        return R.ok(componentVo);
    }


}
