package com.chervon.technology.controller.aws.ruleengine;

import com.chervon.common.core.utils.JsonUtils;
import com.chervon.technology.api.toruleengine.FaultMessageAlarmLogAddDto;
import com.chervon.technology.service.FaultMessageAlarmLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "AWS规则引擎接收-管理平台创建规则接收")
@RestController
@RequestMapping("/rule")
@Slf4j
public class IoTWebDefRuleEngineController {
    @Autowired
    private FaultMessageAlarmLogService faultMessageAlarmLogService;

    /**
     * 规则引擎回调
     *
     * @param ruleId 故障消息Id
     * @param requestDto    添加故障消息告警记录Dto
     */
    @PostMapping("/engine/action")
    @ApiOperation("规则引擎回调")
    public void reportRuleEngineAction(@RequestHeader("ruleId") String ruleId,
                                       @RequestHeader(value = "groupId", required = false) String groupId,
                                       @RequestBody FaultMessageAlarmLogAddDto requestDto) {
        log.info("FaultMessageAlarm callback action: ruleId:{}, groupId:{}, requestDto: {}", ruleId, groupId, JsonUtils.toJsonString(requestDto));
        requestDto.setFaultMessageId(Long.valueOf(ruleId));
        requestDto.setGroupId(groupId);
        faultMessageAlarmLogService.add(requestDto);
    }

    /**
     * C充电桩充电提醒回调
     * 对应topic:$aws/things/+/shadow/update/accepted
     * 规则引擎: *_c_charging_completion_reminder
     *
     * @param dto 添加故障消息告警记录Dto
     */
    @PostMapping("/charger/reminder/action")
    @ApiOperation("C充电桩充电提醒回调")
    public void reportChargerReminderAction(@RequestBody FaultMessageAlarmLogAddDto dto) {
        log.info("reportChargerReminderAction -> 触发了C充电桩提醒消息回调:deviceId:{}, dto: {}", dto.getDeviceId(), JsonUtils.toJsonString(dto));
        faultMessageAlarmLogService.chargerReminder(dto);
    }

    /**
     * 1600w充满电池包数量达到指定的值提醒
     * 对应topic:$aws/things/+/shadow/update/accepted
     * 规则引擎: *_charger_1600_completion_reminder
     *
     * @param dto 添加故障消息告警记录Dto
     */
    @PostMapping("/charger/1600/reminder/action")
    @ApiOperation("1600W充电器充满电池包数量达到指定的值提醒")
    public void reportCharger1600ReminderAction(@RequestBody FaultMessageAlarmLogAddDto dto) {
        log.info("url:/charger/1600/reminder/action, deviceId:{}, dto: {}", dto.getDeviceId(), JsonUtils.toJsonString(dto));
        faultMessageAlarmLogService.charger1600Reminder(dto);
    }
}
