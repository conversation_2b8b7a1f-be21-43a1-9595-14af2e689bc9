package com.chervon.technology.controller;

import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.fleet.web.api.entity.dto.ProductFaultDto;
import com.chervon.technology.domain.dataobject.FaultDictionary;
import com.chervon.technology.service.XxlJobService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/1 14:07
 */
@RestController
@RequestMapping("/m/language")
@AllArgsConstructor
@Api(value = "多语言接口", tags = {"多语言接口"})
@Slf4j
public class MultiLanguageController {

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Autowired
    private XxlJobService xxlJobService;

    @ApiOperation(value = "根据sysCode查询所有多语言")
    @PostMapping("listLanguageBySysCode")
    public Map<String, String> listLanguageBySysCode(@RequestBody SingleInfoReq<String> req) {
        return remoteMultiLanguageService.listLanguageBySysCode(req.getReq());
    }

    @ApiOperation(value = "同步故障码列表信息到fleet测试接口")
    @GetMapping("syncProductFault")
    public List<ProductFaultDto> syncProductFault() {
        return xxlJobService.syncProductFault();
    }
    @ApiOperation(value = "同步故障码字典测试接口")
    @GetMapping("syncFaultDictionary")
    public List<FaultDictionary> syncFaultDictionary() {
        return xxlJobService.syncFaultDictionary();
    }

}
