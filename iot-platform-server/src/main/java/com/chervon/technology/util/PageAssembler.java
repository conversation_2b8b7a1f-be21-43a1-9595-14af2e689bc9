package com.chervon.technology.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.common.core.domain.PageResult;

/**
 * <AUTHOR>
 * @className PageAssembler
 * @description
 * @date 2022/6/17 11:06
 */
public class PageAssembler {
    public static PageResult assemble(IPage page) {
        PageResult pageResult = new PageResult();
        pageResult.setList(page.getRecords());
        pageResult.setPageNum(page.getCurrent());
        pageResult.setPages(page.getPages());
        pageResult.setTotal(page.getTotal());
        pageResult.setPageSize(page.getSize());
        return pageResult;
    }
}
