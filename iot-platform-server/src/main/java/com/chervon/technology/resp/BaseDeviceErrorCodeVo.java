package com.chervon.technology.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/13 11:04
 */
@Data
@ApiModel(description = "基础设备错误码详情")
public class BaseDeviceErrorCodeVo {

    @ApiModelProperty(value = "基础设备错误码id")
    private Long baseId;

    @ApiModelProperty(value = "品类id")
    private Long categoryId;

    @ApiModelProperty(value = "品类名称")
    private String categoryName;

    @ApiModelProperty(value = "品类编码")
    private String categoryCode;

    @ApiModelProperty(value = "模块信息集合")
    private List<ModelVo> models;

}
