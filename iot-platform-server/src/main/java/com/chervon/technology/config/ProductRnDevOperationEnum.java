package com.chervon.technology.config;

import lombok.Getter;

import static com.chervon.technology.api.exception.TechnologyErrorCode.TECHNOLOGY_RN_OPERATE_ILLEGAL;

/**
 * <AUTHOR>
 * @date 2022/7/23 16:37
 */
@Getter
public enum ProductRnDevOperationEnum {

    /**
     * 修改
     */
    UPDATE("update"),
    /**
     * 删除
     */
    DELETE("delete"),
    /**
     * 下载
     */
    DOWNLOAD("download"),
    /**
     * 申请封板
     */
    APPLY_CLOSE("apply_close"),
    /**
     * 确认封板
     */
    ENSURE_CLOSE("ensure_close"),
    /**
     * 封板驳回
     */
    REFUSE_CLOSE("refuse_close"),
    /**
     * 取消封板申请
     */
    CANCEL_APPLY_CLOSE("cancel_apply_close"),
    /**
     * 查看封板被驳回原因
     */
    VIEW_REFUSE_CLOSE_REASON("view_refuse_close_reason");

    private final String name;

    ProductRnDevOperationEnum(String name) {
        this.name = name;
    }

    public static ProductRnDevOperationEnum getFromName(String name) {
        for (ProductRnDevOperationEnum e : ProductRnDevOperationEnum.values()) {
            if (e.getName().equals(name)) {
                return e;
            }
        }
        throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_OPERATE_ILLEGAL);
    }

}
