package com.chervon.technology.config;

import com.chervon.common.core.constant.CacheConstants;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @since 2022-07-22 14:00
 **/
@Component
public class DeviceInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
                             @NotNull Object handler) throws Exception {
        String token = request.getHeader("token");
        String deviceId = RedisUtils.getCacheObject(CacheConstants.IOT_PLATFORM_DEVICE_VERIFY_TOKEN + token);
        if (StringUtils.isEmpty(deviceId)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_TOKEN_INVALID);
        }
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }
}
