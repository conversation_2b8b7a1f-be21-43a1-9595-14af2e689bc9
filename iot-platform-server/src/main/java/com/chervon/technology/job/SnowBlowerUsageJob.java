package com.chervon.technology.job;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/1 20:31
 */
@Component
@Slf4j
public class SnowBlowerUsageJob {


    /**
     * 处理十分钟后扫雪工作结束
     */
    @XxlJob("handleSnowBlowerUsage")
    public void handleSnowBlowerUsage() {
        log.info("XXL-JOB, start handle snow blower usage.");
        XxlJobHelper.log("XXL-JOB, start handle snow blower usage.");


        log.info("XXL-JOB, end handle snow blower usage.");
        XxlJobHelper.log("XXL-JOB, end handle snow blower usage.");
    }
}
