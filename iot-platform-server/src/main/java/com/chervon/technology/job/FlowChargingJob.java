package com.chervon.technology.job;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.technology.domain.dataobject.DeviceIccid;
import com.chervon.technology.domain.dataobject.FlowCharging;
import com.chervon.technology.service.DeviceIccidService;
import com.chervon.technology.service.FlowChargingService;
import com.chervon.technology.util.SignUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/25 20:08
 */
@Component
@Slf4j
public class FlowChargingJob {

    @Value("${quectel.international.url}")
    private String inUrl;

    @Value("${quectel.international.appKey}")
    private String inPrePayAppKey;

    @Value("${quectel.international.secret}")
    private String inPrePaySecret;

    @Value("${quectel.international.postPayAppKey}")
    private String inPostPayAppKey;

    @Value("${quectel.international.postPaySecret}")
    private String inPostPaySecret;

    @Autowired
    private DeviceIccidService deviceIccidService;

    @Autowired
    private FlowChargingService flowChargingService;

    @XxlJob("syncFlowChargingJob")
    public void syncFlowCharging() {
        List<DeviceIccid> deviceIccidList = deviceIccidService.list(new LambdaQueryWrapper<DeviceIccid>()
                .eq(DeviceIccid::getIsCurrent, 1).eq(DeviceIccid::getType, "in"));
        if (deviceIccidList.isEmpty()) {
            log.warn("no valid iccids, job stop");
            return;
        }
        deviceIccidList.removeIf(e -> StringUtils.isBlank(e.getIccid()));
        // 补偿操作，保证一个iccid某一时刻只在一个计费id下
        Map<String, List<DeviceIccid>> group = deviceIccidList.stream().collect(Collectors.groupingBy(DeviceIccid::getIccid));
        List<DeviceIccid> update = new ArrayList<>();
        List<Long> removeIds = new ArrayList<>();
        group.forEach((k, v) -> {
            if (v.size() > 1) {
                Long maxUpdateTimeId = v.stream().max(Comparator.comparing(BaseDo::getUpdateTime)).orElse(new DeviceIccid()).getId();
                update.addAll(v.stream().filter(e -> !e.getId().equals(maxUpdateTimeId)).map(e -> {
                    DeviceIccid u = new DeviceIccid();
                    u.setId(e.getId());
                    removeIds.add(e.getId());
                    u.setIsCurrent(0);
                    return u;
                }).collect(Collectors.toList()));
            }
        });
        if (!update.isEmpty()) {
            deviceIccidService.updateBatchById(update);
        }
        deviceIccidList.removeIf(e -> removeIds.contains(e.getId()));
        // 数据分类，后付、预付、未知
        List<DeviceIccid> post = deviceIccidList.stream().filter(e -> StringUtils.equals("post", e.getPayType())).collect(Collectors.toList());
        List<DeviceIccid> pre = deviceIccidList.stream().filter(e -> StringUtils.equals("pre", e.getPayType())).collect(Collectors.toList());
        List<DeviceIccid> unknown = deviceIccidList.stream().filter(e -> !StringUtils.equals("post", e.getPayType()) && !StringUtils.equals("pre", e.getPayType())).collect(Collectors.toList());
        Map<String, DeviceIccid> collect = deviceIccidList.stream().collect(Collectors.toMap(DeviceIccid::getIccid, Function.identity(), (e1, e2) -> e2));
        Map<String, JSONObject> data = new HashMap<>();
        // 处理后付
        handle(post, data, inPostPayAppKey, inPostPaySecret);
        // 处理预付
        handle(pre, data, inPrePayAppKey, inPrePaySecret);
        // 处理未知
        handleUnknown(unknown, data);
        if (data.isEmpty()) {
            log.warn("no update data");
            return;
        }
        // 对结果进行处理入库
        List<FlowCharging> flowChargings = new ArrayList<>();
        int year = LocalDate.now().getYear();
        int month = LocalDate.now().getMonthValue();
        String yyyyMM = year + "-" + (month < 10 ? "0" + month : month);
        data.forEach((k, v) -> {
            DeviceIccid deviceIccid = collect.get(k);
            if (deviceIccid != null) {
                for (int i = 1, j = LocalDate.now().getDayOfMonth(); i <= j; i++) {
                    BigDecimal flow = v.getBigDecimal("dayflow" + i);
                    if (flow != null && flow.compareTo(BigDecimal.ZERO) > 0) {
                        FlowCharging flowCharging = new FlowCharging();
                        flowCharging.setChargingId(deviceIccid.getChargingId());
                        flowCharging.setDeviceId(deviceIccid.getDeviceId());
                        flowCharging.setIccid(k);
                        flowCharging.setFlow(flow.toPlainString());
                        flowCharging.setHandleTime(yyyyMM + "-" + (i < 10 ? "0" + i : i));
                        flowChargings.add(flowCharging);
                    }
                }
            }
        });
        if (flowChargings.isEmpty()) {
            return;
        }
        // 查询这个月的历史数据，判断数据是否要修改
        List<FlowCharging> list = flowChargingService.list(new LambdaQueryWrapper<FlowCharging>()
                .in(FlowCharging::getIccid, flowChargings.stream().map(FlowCharging::getIccid).collect(Collectors.toList()))
                .likeRight(FlowCharging::getHandleTime, yyyyMM));
        Map<String, FlowCharging> listMap = list.stream().collect(Collectors.toMap(e -> e.getIccid() + "" + e.getHandleTime(), Function.identity(), (e1, e2) -> e2));
        List<FlowCharging> updateD = new ArrayList<>();
        List<FlowCharging> createD = new ArrayList<>();
        flowChargings.forEach(e -> {
            String key = e.getIccid() + "" + e.getHandleTime();
            if (listMap.containsKey(key)) {
                FlowCharging charging = listMap.get(key);
                if (new BigDecimal(charging.getFlow()).compareTo(new BigDecimal(e.getFlow())) != 0) {
                    FlowCharging flowCharging = new FlowCharging();
                    flowCharging.setId(charging.getId());
                    flowCharging.setFlow(e.getFlow());
                    updateD.add(flowCharging);
                }
            } else {
                createD.add(e);
            }
        });
        log.info("update {} records", updateD.size());
        if (!updateD.isEmpty()) {
            flowChargingService.updateBatchById(updateD);
        }
        log.info("create {} records", createD.size());
        if (!createD.isEmpty()) {
            flowChargingService.saveBatch(createD);
        }
        log.info("syncFlowChargingJob finish");
    }

    private void handleUnknown(List<DeviceIccid> unknown, Map<String, JSONObject> data) {
        List<DeviceIccid> other = handleWithResult(unknown, data, inPostPayAppKey, inPostPaySecret);
        List<String> collect = other.stream().map(DeviceIccid::getIccid).collect(Collectors.toList());
        List<DeviceIccid> post = unknown.stream().filter(e -> !collect.contains(e.getIccid())).map(e -> {
            DeviceIccid deviceIccid = new DeviceIccid();
            deviceIccid.setId(e.getId());
            deviceIccid.setPayType("post");
            return deviceIccid;
        }).collect(Collectors.toList());
        if (!post.isEmpty()) {
            deviceIccidService.updateBatchById(post);
        }
        List<DeviceIccid> rest = handleWithResult(other, data, inPrePayAppKey, inPrePaySecret);
        List<String> restIccids = rest.stream().map(DeviceIccid::getIccid).collect(Collectors.toList());
        List<DeviceIccid> pre = other.stream().filter(e -> !restIccids.contains(e.getIccid())).map(e -> {
            DeviceIccid deviceIccid = new DeviceIccid();
            deviceIccid.setId(e.getId());
            deviceIccid.setPayType("pre");
            return deviceIccid;
        }).collect(Collectors.toList());
        if (!pre.isEmpty()) {
            deviceIccidService.updateBatchById(post);
        }
    }

    private void handle(List<DeviceIccid> deviceIccidList, Map<String, JSONObject> dataMap, String appKey, String appSecret) {
        if (CollectionUtils.isEmpty(deviceIccidList)) {
            return;
        }
        int poolSize = 3;
        int pageNo = 0;
        List<DeviceIccid> page = ListUtil.page(pageNo, 100, deviceIccidList);
        while (poolSize > 0 && !page.isEmpty()) {
            RestTemplate restTemplate = new RestTemplate();
            // 请求头设置,x-www-form-urlencoded格式的数据
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            //提交参数设置
            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            map.add("appKey", appKey);
            map.add("method", "fc.function.queryFlowList");
            map.add("t", String.valueOf(new Date().getTime() / 1000));
            map.add("iccids", page.stream().map(DeviceIccid::getIccid).collect(Collectors.joining(",")));
            map.add("month", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM")));

            // 将参数加密签名
            String sourceData = SignUtils.makeSignData(map, appSecret);
            String signData = SignUtils.shaEncode(sourceData);
            map.add("sign", signData);
            // 组装请求体
            HttpEntity<?> request = new HttpEntity<>(map, headers);
            JSONObject res;
            try {
                res = restTemplate.postForObject(inUrl, request, JSONObject.class);
            } catch (Exception e) {
                log.error("syncFlowChargingJob handlePost http error:{}", e.getMessage(), e);
                poolSize--;
                continue;
            }
            if (res == null) {
                log.error("syncFlowChargingJob handlePost no response");
                poolSize--;
            } else if (!Objects.equals(res.getInteger("code"), 200)) {
                log.error("syncFlowChargingJob handlePost request error:{}", res.getString("msg"));
                poolSize--;
            } else {
                poolSize = 3;
                JSONArray data = res.getJSONArray("data");
                if (data == null) {
                    log.warn("syncFlowChargingJob handlePost data is null");
                } else {
                    for (int i = 0, j = data.size(); i < j; i++) {
                        JSONObject jsonObject = data.getJSONObject(i);
                        String iccid = jsonObject.getString("iccid");
                        dataMap.put(iccid, jsonObject);
                    }
                }
                pageNo++;
                page = ListUtil.page(pageNo, 100, deviceIccidList);
            }
        }
    }

    private List<DeviceIccid> handleWithResult(List<DeviceIccid> deviceIccidList, Map<String, JSONObject> dataMap, String appKey, String appSecret) {
        List<DeviceIccid> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(deviceIccidList)) {
            return result;
        }
        int poolSize = 3;
        int pageNo = 0;
        List<DeviceIccid> page = ListUtil.page(pageNo, 100, deviceIccidList);
        while (poolSize > 0 && !page.isEmpty()) {
            Map<String, DeviceIccid> collect = page.stream().collect(Collectors.toMap(DeviceIccid::getIccid, Function.identity(), (e1, e2) -> e2));
            RestTemplate restTemplate = new RestTemplate();
            // 请求头设置,x-www-form-urlencoded格式的数据
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            //提交参数设置
            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            map.add("appKey", appKey);
            map.add("method", "fc.function.queryFlowList");
            map.add("t", String.valueOf(new Date().getTime() / 1000));
            map.add("iccids", page.stream().map(DeviceIccid::getIccid).collect(Collectors.joining(",")));
            map.add("month", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM")));

            // 将参数加密签名
            String sourceData = SignUtils.makeSignData(map, appSecret);
            String signData = SignUtils.shaEncode(sourceData);
            map.add("sign", signData);
            // 组装请求体
            HttpEntity<?> request = new HttpEntity<>(map, headers);
            JSONObject res;
            try {
                res = restTemplate.postForObject(inUrl, request, JSONObject.class);
            } catch (Exception e) {
                log.error("syncFlowChargingJob handlePost http error:{}", e.getMessage(), e);
                poolSize--;
                continue;
            }
            if (res == null) {
                log.error("syncFlowChargingJob handlePost no response");
                poolSize--;
            } else if (!Objects.equals(res.getInteger("code"), 200)) {
                log.error("syncFlowChargingJob handlePost request error:{}", res.getString("msg"));
                poolSize--;
            } else {
                poolSize = 3;
                JSONArray data = res.getJSONArray("data");
                if (data == null) {
                    log.warn("syncFlowChargingJob handlePost data is null");
                } else {
                    for (int i = 0, j = data.size(); i < j; i++) {
                        JSONObject jsonObject = data.getJSONObject(i);
                        String iccid = jsonObject.getString("iccid");
                        dataMap.put(iccid, jsonObject);
                        collect.remove(iccid);
                    }
                }
                pageNo++;
                page = ListUtil.page(pageNo, 100, deviceIccidList);
                result.addAll(collect.values());
            }
        }
        return result;
    }

    public static void main(String[] args) {
        List<String> months = Arrays.asList("202306", "202307");
        List<String> iccids = Arrays.asList("898604270922C0406119", "898604B0102140420557"

        );
        for (String month : months) {
            for (String iccid : iccids) {
                System.out.println("==============================");
                System.out.println("iccids==" + iccid + ",month==" + month);
                handleCn(iccid, month);
                System.out.println("==============================");
            }
        }
    }

    private static void handle(String iccid, String month) {
//        appKey: Q0095Dj1PCw1f9e5
//        secret: Y973car4

        RestTemplate restTemplate = new RestTemplate();
        // 请求头设置,x-www-form-urlencoded格式的数据
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        //提交参数设置
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("appKey", "T2r51Cw394680W81");
//        map.add("appKey", "Q0095Dj1PCw1f9e5");
        map.add("method", "fc.function.queryFlowList");
        map.add("t", String.valueOf(new Date().getTime() / 1000));
        map.add("iccids", iccid);
        map.add("month", month);

        // 将参数加密签名
        String sourceData = SignUtils.makeSignData(map, "G2H704f0");
//        String sourceData = SignUtils.makeSignData(map, "Y973car4");
        String signData = SignUtils.shaEncode(sourceData);
        map.add("sign", signData);
        // 组装请求体
        HttpEntity<?> request = new HttpEntity<>(map, headers);
        JSONObject res;
        try {
            res = restTemplate.postForObject("https://connectivity.quectel.com/openapi/router", request, JSONObject.class);
            System.out.println(res);
        } catch (Exception e) {
            log.error("syncFlowChargingJob handleExternal http error:{}", e.getMessage(), e);
        }
    }

    private static void handleCn(String iccid, String month) {
//        appKey: Q0095Dj1PCw1f9e5
//        secret: Y973car4

        RestTemplate restTemplate = new RestTemplate();
        // 请求头设置,x-www-form-urlencoded格式的数据
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        //提交参数设置
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();

        map.add("appKey", "Mabz8A257155xfJ8");
        map.add("method", "fc.function.dayflow.list");
        map.add("t", String.valueOf(new Date().getTime() / 1000));
        map.add("iccid", iccid);
        map.add("month", month);

        // 将参数加密签名
        String sourceData = SignUtils.makeSignData(map, "8KcaK273");
        String signData = SignUtils.shaEncode(sourceData);
        map.add("sign", signData);
        // 组装请求体
        HttpEntity<?> request = new HttpEntity<>(map, headers);
        JSONObject res;
        try {
            res = restTemplate.postForObject("http://api.quectel.com/openapi/router", request, JSONObject.class);
            System.out.println(res);
        } catch (Exception e) {
            log.error("syncFlowChargingJob handleExternal http error:{}", e.getMessage(), e);
        }
    }

}
