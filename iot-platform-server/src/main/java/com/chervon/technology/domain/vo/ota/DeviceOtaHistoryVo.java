package com.chervon.technology.domain.vo.ota;

import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig.LocalDateTimeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @className DeviceOtaHistoryVo
 * @description
 * @date 2022/7/13 18:17
 */
@Data
@ApiModel("升级历史vo")
public class DeviceOtaHistoryVo {
    /**
     * jobId
     **/
    @ApiModelProperty("jobId")
    private Long jobId;

    /**
     * 原始固件版本
     **/
    @ApiModelProperty("原始固件版本")
    private String oldVersion;

    /**
     * 目标固件版本
     **/
    @ApiModelProperty("目标固件版本")
    private String newVersion;

    /**
     * 升级状态
     **/
    @ApiModelProperty("升级任务状态\n"
        + "  QUEUED：初始状态，排队中\n"
        + "  IN_PROGRESS：进行中\n"
        + "  SUCCEED：成功\n"
        + "  FAILED: 失败\n"
        + "  REJECTED: 拒绝\n"
        + "  CANCELED: 取消 被其他终端终止\n"
        + "  REMOVED：移除，设备被从任务分组中移除\n"
        + "  TIMED_OUT: 超时")
    private String status;

    /**
     * 失败原因
     **/
    @ApiModelProperty("失败原因")
    private String detail;

    /**
     * 操作用户id
     **/
    @ApiModelProperty("操作用户id")
    private String userId;

    /**
     * 操作的用户平台
     **/
    @ApiModelProperty("操作的用户平台")
    private Integer businessType;

    /**
     * 升级时间
     **/
    @ApiModelProperty("升级时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime upgradeTime;
}
