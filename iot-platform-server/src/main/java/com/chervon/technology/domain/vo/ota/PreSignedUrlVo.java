package com.chervon.technology.domain.vo.ota;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 预签名url vo
 * <AUTHOR>
 * @date 18:36 2022/7/29
 **/
@Data
public class PreSignedUrlVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件存在s3的key
     **/
    @ApiModelProperty("文件存在s3的key")
    private String key;

    /**
     * 预签名url
     **/
    @ApiModelProperty("预签名url")
    private String preSignedUrl;


}
