//package com.chervon.technology.domain.enums;
//
///**
// * 日志通信类型
// * <AUTHOR>
// * @date 16:38 2022/7/29
// **/
//public enum LogTransferType {
//    UP(0, "上行"),
//    DOWN(1, "下行"),
//    UNKNOWN(2, "未知");
//
//    private int value;
//    private String typeStr;
//
//    LogTransferType(int value, String typeStr) {
//        this.value = value;
//        this.typeStr = typeStr;
//    }
//
//    public int getValue() {
//        return value;
//    }
//
//    public String getTypeStr() {
//        return typeStr;
//    }
//
//
//
//    public static LogTransferType getType(int value) {
//        switch (value) {
//            case 0:
//                return LogTransferType.UP;
//            case 1:
//                return LogTransferType.DOWN;
//            case 2:
//            default:
//            return LogTransferType.UNKNOWN;
//        }
//    }
//}
