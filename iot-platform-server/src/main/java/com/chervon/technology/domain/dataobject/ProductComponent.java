package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 产品和总成的关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("product_component")
@ApiModel(value = "productComponent", description = "产品和总成的关联表")
@NoArgsConstructor
public class ProductComponent extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品Id，PID")
    private Long productId;

    @ApiModelProperty("总成零件号")
    private String componentNo;

    @ApiModelProperty("软件编码")
    private String softwareCoding;

    public ProductComponent(Long productId, String componentNo) {
        this.productId = productId;
        this.componentNo = componentNo;
    }

    public ProductComponent(Long productId, String componentNo,String softwareCoding) {
        this.productId = productId;
        this.componentNo = componentNo;
        this.softwareCoding = softwareCoding;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        ProductComponent that = (ProductComponent) o;
        if (that.getProductId().equals(this.getProductId()) && that.getComponentNo().equals(this.getComponentNo())) {
            return true;
        }
        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), productId, componentNo);
    }
}
