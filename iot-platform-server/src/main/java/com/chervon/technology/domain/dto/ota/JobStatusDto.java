package com.chervon.technology.domain.dto.ota;

import com.chervon.technology.api.enums.OtaJobDevelopStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @className JobAddDto
 * @description 新增任务DTO
 * @date 2022/7/29 11:06
 */
@ApiModel("新增升级任务DTO")
@Data
public class JobStatusDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * jobId
     **/
    @ApiModelProperty("jobId")
    private Long jobId;

    /**
     * 升级任务发布状态
     **/
    @ApiModelProperty("升级任务发布状态")
    private OtaJobDevelopStatus status;

    /**
     * 审批意见
     **/
    @ApiModelProperty("审批意见")
    private String description;
}
