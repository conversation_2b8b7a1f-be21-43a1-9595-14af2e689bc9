package com.chervon.technology.domain.dto.product.model;

import com.chervon.technology.domain.dto.product.ParamDataDto;
import com.chervon.common.core.domain.MultiLanguageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-12
 */
@Data
public class EventAddDto implements Serializable {

    /**
     * 所属产品id
     */
    @ApiModelProperty("物模型所属产品id")
    @NotNull
    private Long productId;

    /**
     * 物模型项目唯一标识符
     **/
    @ApiModelProperty("物模型唯一标识符")
    @NotEmpty
    private String identifier;

    /**
     * 物模型项目名称
     **/
    @NotEmpty
    @ApiModelProperty("物模型名称")
    private String name;

    /**
     * 事件类型(info, warn， error)
     */
    @NotEmpty
    @ApiModelProperty("事件类型：info, warn， error")
    private String type;

    /**
     * 事件描述
     **/
    @ApiModelProperty("事件描述")
    private String desc;

    /**
     * 参数列表
     */
    @ApiModelProperty("参数列表")
    private List<ParamDataDto> outputData;
}
