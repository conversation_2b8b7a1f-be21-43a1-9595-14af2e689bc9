package com.chervon.technology.domain.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 产品排序
 *
 * <AUTHOR>
 * @date 2022年11月10日
 */
@Data
public class ProductOrderDto {

	@ApiModelProperty("品类排序")
	private Map<Long, Integer> categoryOrdersMap;

	@ApiModelProperty("商品型号排序")
	private Map<String, Integer> commodityModelsMap;

	@ApiModelProperty("品类Id")
	private List<Long> categoryIds;

	@ApiModelProperty("商品型号")
	private List<String> commodityModels;
}
