package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 审批记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("approval_record")
@ApiModel(value = "ApprovalRecord对象", description = "审批记录表")
@NoArgsConstructor
public class ApprovalRecord extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("审批类型：0产品审批，1固件审批，2RN包审批")
    private Integer type;

    @ApiModelProperty("审批的记录Id，如产品Id，固件Id，RN包管理ID")
    private Long approvalId;

    @ApiModelProperty("审批的用户Id")
    private Long approvalUserId;

    @ApiModelProperty("操作：0申请封板，1确认封板，2拒绝封板，3取消封板，4申请解版，5确认解版，6拒绝解版，7取消解版")
    private Integer operationType;

    @ApiModelProperty("审批意见")
    private String description;

    @ApiModelProperty("起始状态")
    private String sourceStatus;

    @ApiModelProperty("目标状态")
    private String targetStatus;

    public ApprovalRecord(Integer type, Long approvalId, Long approvalUserId, String description,
                          Integer operationType, String sourceStatus, String targetStatus) {
        this.type = type;
        this.approvalId = approvalId;
        this.approvalUserId = approvalUserId;
        this.description = description;
        this.operationType = operationType;
        this.sourceStatus = sourceStatus;
        this.targetStatus = targetStatus;
    }

}
