package com.chervon.technology.domain.vo.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-09-31
 */
@Data
public class ProductBaseInfoVo implements Serializable {

    @ApiModelProperty("产品Pid")
    private Long pId;

    /**
     * 品类Id
     */
    @ApiModelProperty("品类Id")
    private Long categoryId;

    @ApiModelProperty("品类名称")
    private String categoryName;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String model;

    @ApiModelProperty("商品型号Model#")
    private String commodityModel;
}
