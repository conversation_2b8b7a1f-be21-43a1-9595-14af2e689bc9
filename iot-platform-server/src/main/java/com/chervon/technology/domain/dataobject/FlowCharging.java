package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/2/25 15:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("charging_flow")
public class FlowCharging extends BaseDo implements Serializable {

    /**
     * 计费id
     */
    private Long chargingId;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * iccid
     */
    private String iccid;

    /**
     * 发生时间
     */
    private String handleTime;

    /**
     * 发生流量，单位MB
     */
    private String flow;

}
