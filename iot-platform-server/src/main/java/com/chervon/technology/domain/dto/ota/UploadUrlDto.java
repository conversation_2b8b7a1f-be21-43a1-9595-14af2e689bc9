package com.chervon.technology.domain.dto.ota;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 预签名url vo
 * <AUTHOR>
 * @date 18:36 2022/7/29
 **/
@Data
public class UploadUrlDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件名
     **/
    @ApiModelProperty("文件名")
    private String fileName;

    /**
     * 产品型号
     **/
    @ApiModelProperty("产品型号")
    private String productModel;

    /**
     * 总成零件号
     **/
    @ApiModelProperty("总成零件号")
    private String componentNo;


}
