package com.chervon.technology.domain.vo.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 产品被驳回的vo
 * <AUTHOR>
 * @date 2022-07-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RefuseApprovalRecordVo implements Serializable {

    @ApiModelProperty("产品Pid")
    private Long pid;

    @ApiModelProperty("审批者")
    private String approvalUserName;

    @ApiModelProperty("审批内容")
    private String content;

    @ApiModelProperty("产品状态")
    private String targetStatus;
}
