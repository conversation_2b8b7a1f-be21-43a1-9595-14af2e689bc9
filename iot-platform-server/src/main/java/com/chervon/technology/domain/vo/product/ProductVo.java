package com.chervon.technology.domain.vo.product;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-06
 */
@Data
public class ProductVo implements Serializable {

    private static final long serialVersionUID = -6881092095586010157L;
    @ApiModelProperty("产品Pid")
    private Long id;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;
    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String model;

    /**
     * 商品型号Model#
     */
    @ApiModelProperty("商品型号Model#")
    private String commodityModel;
    /**
     * 产品图标
     */
    @ApiModelProperty("产品图标")
    private String productIcon;
    /**
     * 品类Id
     */
    @ApiModelProperty("品类Id")
    private Long categoryId;

    @ApiModelProperty("品类名称")
    private String categoryName;

    @ApiModelProperty("产品SnCode")
    private String productSnCode;

    /**
     * 设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，
     * notIotDevice：非Iot设备，oldIotDevice：老iot设备
     */
    @ApiModelProperty("设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，" +
            " notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    private String productType;

    @ApiModelProperty("设备类型，wifiAndBle：Wifi+BLE，4GAndBle：4G+BLE，ble：BLE，DOrT：D/T，" +
            "wifi：WIFI，4G：4G，lan：LAN，notNetworked：不联网")
    private List<String> networkModes;

    /**
     * 产品描述
     */
    @ApiModelProperty("产品描述")
    private String description;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    @ApiModelProperty("问卷模板：commonTemplate， extendedWarrantyTemplate，用逗号间隔")
    private String questionTemplate;

    @ApiModelProperty("图片类型：0图片上传，1图片链接")
    private Integer iconType;

    @ApiModelProperty("适用app类型 1 ego 2 fleet")
    private List<Integer> businessType;

    @ApiModelProperty("是否支持分享, false:不支持, true:支持")
    private Boolean isSharingSupported;

    @ApiModelProperty("分享权限, 1:账户权限最高，2：设备权限最高")
    private Integer shareAuthorityType;
}
