package com.chervon.technology.domain.vo.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 产品的简约信息
 * <AUTHOR>
 */
@Data
public class ProductInfoVo implements Serializable {
    @ApiModelProperty("产品Pid")
    private Long id;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;
    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String model;

    /**
     * 商品型号Model#
     */
    @ApiModelProperty("商品型号Model#")
    private String commodityModel;
    /**
     * 产品图标
     */
    @ApiModelProperty("产品图标")
    private String productIcon;
    /**
     * 品类Id
     */
    @ApiModelProperty("品类Id")
    private Long categoryId;

    /**
     * 设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，
     * oldIotDevice：老iot设备
     */

    @ApiModelProperty("设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，" +
            "notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    private String productType;

    /**
     * 产品描述
     */
    @ApiModelProperty("产品描述")
    private String description;
    /**
     * 产品状态 开发中: develop_ing, 1封板审核中: approve_ing, 已封板:approved  解版审核中:reopen_approve_ing  4已解版:reopen_success  拒绝封板：refuse_approve  拒绝解版：refuse_open_approve
     */
    @ApiModelProperty("产品状态 开发中: develop_ing, 1封板审核中: approve_ing, 已封板:approved  解版审核中:reopen_approve_ing  4已解版:reopen_success  拒绝封板：refuse_approve  拒绝解版：refuse_open_approve")
    private String status;

    @ApiModelProperty("图片类型：0图片上传，1图片链接")
    private Integer iconType;

    @ApiModelProperty("适用app类型 1 ego 2 fleet")
    private List<Integer> businessType;

}
