package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 故障消息告警记录
 *
 * <AUTHOR>
 * @since 2022-09-14 19:22
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fault_message_alarm_log")
public class FaultMessageAlarmLog extends BaseDo {
    /**
     * 故障消息Id(规则请求头中的ruleId)
     */
    @ApiModelProperty("故障消息Id(规则请求头中的ruleId)")
    private Long faultMessageId;
    /**
     * 设备Id
     */
    @ApiModelProperty("设备Id")
    private String deviceId;
    /**
     * 物模型功能id
     */
    @ApiModelProperty("物模型功能id")
    private String propertyId;
    /**
     * 功能名称多语言id
     */
    @ApiModelProperty("功能名称多语言id")
    private String multiLanguageId;
    /**
     * 触发告警物模型的值
     */
    @ApiModelProperty("触发告警物模型的值")
    private String value;
    /**
     * 是否调用消息中心推送方法: 0否 1是
     */
    @ApiModelProperty("是否调用消息中心推送方法: 0否 1是")
    private Integer isPushed;
    /**
     * 没有调用消息中心推送方法的原因
     */
    @ApiModelProperty("没有调用消息中心推送方法的原因")
    private String reason;

    /**
     * 推送方式集合，逗号分隔
     */
    @ApiModelProperty("推送方式集合，逗号分隔")
    private String pushTypes;

    /**
     * 推送用户列表，逗号分隔
     */
    @ApiModelProperty("推送用户列表，逗号分隔")
    private String pushUsers;
}
