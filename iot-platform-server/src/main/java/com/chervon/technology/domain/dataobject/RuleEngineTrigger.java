package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则引擎 1-N 触发器关联表
 * {@link RuleEngine 规则引擎表}
 * {@link RuleTrigger 触发器表}
 *
 * <AUTHOR>
 * @since 2022-09-07 15:10
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("rule_engine_trigger")
public class RuleEngineTrigger extends BaseDo {
    /**
     * 规则引擎Id
     */
    private Long ruleEngineId;
    /**
     * 触发器Id
     */
    private Long triggerId;
}
