package com.chervon.technology.domain.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/17 11:37
 * @desc 描述
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictBo {

    @ApiModelProperty(value = "字典名称")
    private String dictName;

    @ApiModelProperty(value = "字典项集合")
    private List<DictNodeBo> nodes = new ArrayList<>();
}

