package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/8 11:15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("device_path")
public class DevicePath extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String deviceId;

    private String h;

    private String v;

    private Long snowId;

}
