package com.chervon.technology.domain.dto.ota;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 获取设备升级记录列表
 * <AUTHOR>
 * @since 2022-07-29
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "获取设备升级记录列表", description = "获取设备升级记录列表")
public class DeviceOtaListDto extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

 /**
     * 设备id
     **/
    @NotNull
    @ApiModelProperty("设备id")
    private String deviceId;

}
