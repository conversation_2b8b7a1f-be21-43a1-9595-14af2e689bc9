package com.chervon.technology.domain;

import java.text.MessageFormat;

/**
 * Robot项目用到的常量
 *
 * <AUTHOR>
 */
public class RobotConstant {

    /**
     * 设备下的pathId集合key，{0}表示设备id
     */
    private static final String DEVICE_PATH_IDS_KEY = "DEVICE_PATH_IDS_{0}";

    /**
     * 设备下的pathId集合key，数据类型Set: DEVICE_PATH_IDS_{deviceId}
     *
     * @param deviceId 设备id
     * @return key值
     */
    public static String getDevicePathIdsKey(String deviceId) {
        return MessageFormat.format(DEVICE_PATH_IDS_KEY, deviceId);
    }

    /**
     * 设备轨迹集合key，{0}表示设备id，{1}表示轨迹id
     */
    private static final String DEVICE_PATH_ITEMS_KEY = "DEVICE_PATH_ITEMS_{0}_{1}";

    /**
     * 设备轨迹集合key，数据类型Set: DEVICE_PATH_ITEMS_{deviceId}_{pathId}
     *
     * @param deviceId 设备id
     * @param pathId   轨迹id
     * @return key值
     */
    public static String getDevicePathItemsKey(String deviceId, String pathId) {
        return MessageFormat.format(DEVICE_PATH_ITEMS_KEY, deviceId, pathId);
    }

}
