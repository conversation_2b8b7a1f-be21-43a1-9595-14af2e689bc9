package com.chervon.technology.domain.vo.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 运营平台获取产品发布列表时的数据合并
 * <AUTHOR>
 * @date 2022-08-04
 */
@Data
public class ProductReleaseMergeVo implements Serializable {
    @ApiModelProperty("产品Id")
    private Long id;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("产品图标")
    private String productIcon;

    @ApiModelProperty("产品snCode")
    private String productSnCode;

    @ApiModelProperty("产品model")
    private String model;

    @ApiModelProperty("商品型号Model#")
    private String commodityModel;

    @ApiModelProperty("品牌id")
    private Long brandId;

    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("品类Id")
    private Long categoryId;

    @ApiModelProperty("品类名称")
    private String categoryName;

    @ApiModelProperty("设备类型：设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，" +
            "gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    private String productType;

    @ApiModelProperty("产品发布状态:\n" +
            " 产品没有封板 -\n" +
            " PToBeRelease	待发布\n" +
            " PReleaseTest 发布测试验证中\n" +
            " PReleaseTestRefuse 发布测试驳回\n" +
            " PReleaseApprove	发布审核\n" +
            " PReleased	已发布\n" +
            " PReleaseRefuse	发布被驳回\n" +
            " PToBeUpdateRelease 待更新发布\n" +
            " PUpdateReleaseTest 更新测试验证中\n" +
            " PUpdateReleaseTestRefuse 更新测试驳回中\n" +
            " PUpdateReleaseApprove	更新审核中\n" +
            " PUpdateReleaseRefuse	更新被驳回\n" +
            " POffReleasedApprove	下架审核中\n" +
            " POffReleasedRefuse	下架被驳回\n" +
            " POffReleased	已下架")
    private String releaseStatus;

    /**
     * 运营平台的备注
     */
    @ApiModelProperty("运营平台备注")
    private String operationRemark;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("草稿Id")
    private Long draftId;

    private Integer draftDeleted;

    /**
     * 产品名称
     */
    @ApiModelProperty("草稿产品名称")
    private String draftProductName;

    /**
     * 商品型号Model#
     */
    @ApiModelProperty("草稿商品型号Model#")
    private String draftCommodityModel;

    @ApiModelProperty("草稿产品SNCode")
    private String draftProductSnCode;
    /**
     * 产品图标
     */
    @ApiModelProperty("草稿产品图标")
    private String draftProductIcon;

    /**
     * 品牌Id
     */
    @ApiModelProperty("品牌Id")
    private Long draftBrandId;

    /**
     * 产品发布状态
     * 无状态 -
     * 待发布 to_be_release
     * 发布审核 release_approve
     * 已发布 released
     * 发布被驳回 release_refuse
     * 更新审核中 update_release_approve
     * 更新被驳回 update_release_refuse
     * 下架审核中 off_released_approve
     * 下架被驳回 off_released_refuse
     * 已下架 off_released
     */
    @ApiModelProperty("发布状态")
    private String draftReleaseStatus;

    /**
     * 运营平台的备注
     */
    @ApiModelProperty("运营平台的备注")
    private String draftOperationRemark;

    @ApiModelProperty("申请时间")
    private Date applyTime;

    @ApiModelProperty("申请人")
    private String applyBy;

    @ApiModelProperty("审批人")
    private String approveBy;

    @ApiModelProperty("审批时间")
    private Date approveTime;
}
