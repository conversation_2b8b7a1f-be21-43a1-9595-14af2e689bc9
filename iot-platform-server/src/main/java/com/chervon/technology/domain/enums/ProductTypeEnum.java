package com.chervon.technology.domain.enums;

import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.config.ExceptionMessageUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * 产品类型
 *
 * <AUTHOR>
 * @date 2022-07-22
 */
public enum ProductTypeEnum {
    /**
     * deviceType	设备类型	 (字典code)
     * directConnectedDevice	直连设备
     * gatewayDevice	网关设备
     * gatewaySubDevice	网关子设备
     * notIotDevice	非Iot设备
     * oldIotDevice	老iot设备
     */

    DIRECT_CONNECTED_DEVICE("directConnectedDevice", "直连设备"),
    GATEWAY_DEVICE("gatewayDevice", "网关设备"),
    GATEWAY_SUB_DEVICE("gatewaySubDevice", "网关子设备"),
    NOT_IOT_DEVICE("notIotDevice", "非Iot设备"),
    NET_IOT_DEVICE("oldIotDevice", "老iot设备");

    private String value;

    private String label;

    ProductTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据匹配value的值获取Label
     *
     * @param value 枚举类型
     * @return 审批状态
     */
    public static String getLabelByValue(String value) {
        if (StringUtils.isBlank(value)) {
            return "";
        }
        for (ProductTypeEnum s : ProductTypeEnum.values()) {
            if (value.equals(s.getValue())) {
                return s.getLabel();
            }
        }
        return "";
    }

    public static String getValueByLabel(String label) {
        if (StringUtils.isBlank(label)) {
            return "";
        }
        for (ProductTypeEnum s : ProductTypeEnum.values()) {
            if (label.equals(s.getLabel())) {
                return s.getValue();
            }
        }
        return "";
    }

    /**
     * 获取StatusEnum
     *
     * @param value 枚举类型
     * @return 审批状态
     */
    public static ProductTypeEnum getStatusEnum(String value) {
        for (ProductTypeEnum s : ProductTypeEnum.values()) {
            if (value.equals(s.getValue())) {
                return s;
            }
        }
        throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_DEVICE_TYPE_NOT_EXIST);
    }
}
