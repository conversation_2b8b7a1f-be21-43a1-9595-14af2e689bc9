package com.chervon.technology.domain.vo.device;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import com.chervon.iot.middle.api.enums.ThingLogStatus;
import com.chervon.iot.middle.api.enums.ThingLogTransferType;
import com.sargeraswang.util.ExcelUtil.ExcelCell;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备日志导出使用的vo
 *
 * <AUTHOR>
 * @date 2022年10月17日
 */
@Data
public class LogListExportVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 日志类型
	 **/
	@Alias("日志类型")
	private String type;

	public String getType(){
		return CsvUtil.format(this.type);
	}

	/**
	 * 日志时间
	 **/
	@Alias("日志时间")
	private String logTime;

	public String getLogTime(){
		return CsvUtil.format(this.logTime);
	}

	/**
	 * 日志结果
	 **/
	@Alias("结果")
	private String status;

	public String getStatus(){
		return CsvUtil.format(this.status);
	}

	/**
	 * 日志内容
	 **/
	@Alias("日志内容")
	private String log;

	public String getLog(){
		return CsvUtil.format(this.log);
	}



}
