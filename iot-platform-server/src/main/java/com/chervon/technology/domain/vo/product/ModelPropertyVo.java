package com.chervon.technology.domain.vo.product;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.iot.middle.api.pojo.thingmodel.StructDto;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-07-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModelPropertyVo implements Serializable {

    /**
     * 物模型项目唯一标识符
     */
    @ApiModelProperty("物模型项目唯一标识符")
    private String identifier;
    /**
     * 物模型项目名称
     **/
    @ApiModelProperty("物模型项目名称")
    private String name;//多语言name转string
    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String desc;
    /**
     * r上报/w下发/rw上报&下发
     */
    @ApiModelProperty("r上报/w下发/rw上报&下发")
    private String accessMode;

    /**
     * 数据类型: int(原生)，float(原生)，bool(0或1的int类型)，enum(int类型)，String
     */
    @ApiModelProperty("数据类型: int(原生)，float(原生)，bool(0或1的int类型)，enum(int类型)，String")
    private String dataType;
    /**
     * 结构体列表
     **/
    @ApiModelProperty("结构体列表")
    private List<StructDto> struct;
    /**
     * 属性规范（简单类型）
     **/
    @ApiModelProperty("属性规范")
    private Map specs;


    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("更新者")
    private String updateBy;
}
