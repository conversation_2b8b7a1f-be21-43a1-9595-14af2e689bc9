package com.chervon.technology.domain.dto.component;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022-08-03
 */
@Data
public class CheckComponentDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("总成零件号")
    @NotEmpty
    private String componentNo;

    @ApiModelProperty("产品PID")
    @NotNull
    private Long productId;
}
