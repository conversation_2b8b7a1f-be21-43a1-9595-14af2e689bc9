package com.chervon.technology.domain.dto.product.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 添加物模型
 *
 * <AUTHOR>
 */
@Data
public class ModelDeleteDto {

    /**
     * 所属产品id
     */
    @ApiModelProperty("物模型所属产品id")
    @NotNull
    private Long productId;

    /**
     * 物模型项目唯一标识符
     **/
    @ApiModelProperty("物模型唯一标识符")
    @NotEmpty
    private String identifier;
}
