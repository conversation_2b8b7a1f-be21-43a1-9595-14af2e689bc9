package com.chervon.technology.domain.vo.device;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023年1月13日
 */
@Data
public class DeviceDebugLogSearchVo extends PageRequest implements Serializable {

	@ApiModelProperty("设备Id")
	@NotEmpty
	private String deviceId;

	@ApiModelProperty("文件名称")
	private String fileName;

}
