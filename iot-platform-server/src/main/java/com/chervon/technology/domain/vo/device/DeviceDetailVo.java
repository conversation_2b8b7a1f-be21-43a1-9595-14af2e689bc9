package com.chervon.technology.domain.vo.device;

import com.chervon.common.core.annotation.Sensitive;
import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.common.core.enums.SensitiveStrategy;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 20220505
 * 设备详情Vo
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceDetailVo extends DeviceListVo implements Serializable {
    /**
     * 设备昵称
     */
    @ApiModelProperty("设备昵称")
    private String nickName;
    /**
     * 所属产品SnCode
     */
    @ApiModelProperty("所属产品SnCode")
    private String productSnCode;
    /**
     * 通讯方式，wifiAndBle：Wifi+BLE，4GAndBle：4G+BLE，ble：BLE，DOrT：D/T，wifi：WIFI，4G：4G，lan：LAN，notNetworked：不联网
     */
    @ApiModelProperty("通讯方式，wifiAndBle：Wifi+BLE，4GAndBle：4G+BLE，ble：BLE，DOrT：D/T，wifi：WIFI，4G：4G，lan：LAN，notNetworked：不联网")
    private List<String> communicateMode;

    /**
     * 设备配置表
     */
    @ApiModelProperty("设备配置表")
    private String configurationTable;
    /**
     * 注册用户ID
     */
    @ApiModelProperty("注册用户ID")
    @Sensitive(strategy = SensitiveStrategy.USER_ID)
    private String infoRegisteredBy;
    /**
     * 注册时间
     */
    @ApiModelProperty("注册时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime registerTime;
    /**
     * 设备固件技术版本
     */
    @ApiModelProperty("设备固件技术版本")
    private String technologyVersion;
    /**
     * 设备固件客户版本
     */
    @ApiModelProperty("设备固件客户版本")
    private String customVersion;

    /**
     * 已绑定用户ID
     */
    @ApiModelProperty("已绑定用户ID")
    private List<String> sensitiveBoundUserIds;

}
