package com.chervon.technology.domain.vo.productdebug;

import com.chervon.technology.domain.vo.product.ModelEventVo;
import com.chervon.technology.domain.vo.product.ModelPropertyVo;
import com.chervon.technology.domain.vo.product.ModelServiceVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 物模型
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ModelVo implements Serializable {
    @ApiModelProperty("属性功能集合")
    private List<ModelPropertyVo> property;
    @ApiModelProperty("服务功能集合")
    private List<ModelServiceVo> service;
    @ApiModelProperty("事件功能集合")
    private List<ModelEventVo> event;
}
