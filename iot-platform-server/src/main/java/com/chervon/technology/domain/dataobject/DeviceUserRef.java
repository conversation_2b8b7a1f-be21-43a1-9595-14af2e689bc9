package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 设备表用户关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("device_user_ref")
public class DeviceUserRef extends BaseDo {

    /**
     * 设备id
     */
    private String deviceId;
    /**
     * ego第一次绑定的用户id
     */
    private Long egoFirstBindUserId;
    /**
     * ego第一次绑定的时间
     */
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime egoFirstBindTime;
    /**
     * fleet第一次绑定的租户id
     */
    private Long fleetFirstBindCompanyId;
    /**
     * fleet第一次绑定的用户id
     */
    private Long fleetFirstBindUserId;
    /**
     * fleet第一次绑定的时间
     */
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime fleetFirstBindTime;
    /**
     * 当前绑定的app类型 0 未绑定 1 ego 2 fleet
     */
    private Integer currentBindBusinessType;
    /**
     * 第一次绑定的app类型 1 ego 2 fleet
     */
    private Integer firstBindBusinessType;
    /**
     * 第一次绑定的时间
     */
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime firstBindTime;
    /**
     * 第一次绑定ego的用户id
     */
    private Long firstBindEgoUserId;
    /**
     * 第一次绑定fleet的租户id
     */
    private Long firstBindFleetCompanyId;
    /**
     * 第一次绑定fleet的用户id
     */
    private Long firstBindFleetUserId;

}
