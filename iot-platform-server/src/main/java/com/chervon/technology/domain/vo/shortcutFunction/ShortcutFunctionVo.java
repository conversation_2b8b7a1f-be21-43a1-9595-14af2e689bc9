package com.chervon.technology.domain.vo.shortcutFunction;

import com.chervon.common.core.domain.MultiLanguageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2022/11/10 14:37
 */
@Data
public class ShortcutFunctionVo implements Serializable {
    @ApiModelProperty("主键id")
    private Long id;
    @ApiModelProperty("功能id")
    private String propertyId;
    @ApiModelProperty("功能名称")
    private MultiLanguageVo functionName;
    @ApiModelProperty("功能类型")
    private String functionType;
    @ApiModelProperty("类型")
    private String type;
}
