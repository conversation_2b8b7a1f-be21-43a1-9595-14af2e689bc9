package com.chervon.technology.domain.dto.component;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-07-11
 */
@Data
public class AddComponentDto implements Serializable {

    @ApiModelProperty("总成零件号")
    @NotEmpty
    private String componentNo;

    @ApiModelProperty("总成零件名称")
    @NotEmpty
    private String componentName;

    @ApiModelProperty("总成零件类型，mcu：MCU，subDevice：子设备，bleModule：蓝牙模组，4gModule：4G")
    @NotNull
    private String componentType;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("软件编码--这里作废了")
    @Deprecated
    private String softwareCoding;
}
