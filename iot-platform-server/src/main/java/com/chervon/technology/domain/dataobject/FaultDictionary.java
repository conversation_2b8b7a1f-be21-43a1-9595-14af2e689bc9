package com.chervon.technology.domain.dataobject;

import java.io.Serializable;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 故障告警消息信息表（从iot平台同步过来给大数据看板查询使用）(t_dic_fault_code)实体类
 *
 * <AUTHOR>
 * @since 2024-03-13 13:56:34
 * @description 设备证书关系
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_fault_dictionary")
public class FaultDictionary extends Model<FaultDictionary> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId
	private Long id;
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 产品sn编码
     */
    private String productSnCode;
    /**
     * 故障编号
     */
    private String faultCode;
    /**
     * 故障标题
     */
    private String faultTitle;
    /**
     * 故障内容
     */
    private String faultDesc;
    /**
     * 语言编码
     */
    private String language;
    /**
     * 删除状态 0：未删除 1：已删除
     */
    private Integer isDeleted;
    /**
     * 告警消息id
     */
    private Long faultMessageId;
    /**
     * 消息模板id
     */
    private Long messageTemplateId;
    /**
     * 处理建议id
     */
    private Long suggestionId;
    /**
     * 修改时间
     */
    private Long modifyTime;

    public void setId(){
        this.id= (long) Objects.hash(this.getFaultMessageId(), this.getFaultCode(), this.getLanguage());
    }

}