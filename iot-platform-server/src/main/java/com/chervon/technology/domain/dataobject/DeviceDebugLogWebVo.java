package com.chervon.technology.domain.dataobject;

import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 *
 * <AUTHOR>
 * @since 2022年12月30日
 */
@Data
public class DeviceDebugLogWebVo extends BaseDo implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("设备id")
	private String deviceId;

	@ApiModelProperty("文件名称")
	private String fileName;

	@ApiModelProperty("s3的key")
	private String fileKey;

	@ApiModelProperty("文件大小")
	private Integer fileSize;

	@ApiModelProperty("上传进度")
	private String progress;

	@ApiModelProperty("分段上传任务标识id")
	private String uploadId;

	@ApiModelProperty("分段总数")
	private Integer TotalPartNum;

	@ApiModelProperty("成功数量")
	private Integer successPartNum;

}
