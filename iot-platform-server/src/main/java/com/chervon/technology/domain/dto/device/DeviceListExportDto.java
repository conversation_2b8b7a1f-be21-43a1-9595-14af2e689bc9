package com.chervon.technology.domain.dto.device;

import cn.hutool.core.annotation.Alias;
import com.chervon.technology.api.enums.DeviceOnlineStatusEnum;
import com.chervon.technology.api.enums.DeviceStatusEnum;
import com.chervon.technology.api.enums.DeviceUsageStatusEnum;
import com.chervon.technology.domain.enums.ProductTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sargeraswang.util.ExcelUtil.ExcelCell;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 设备导出Dto
 *
 * <AUTHOR>
 * @date 2022年10月28日
 */
@Data
public class DeviceListExportDto implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 设备ID
	 */
	@ApiModelProperty("设备ID")
	@Alias("设备ID")
	@ExcelCell(index = 0)
	private String deviceId;
	/**
	 * SN
	 */
	@ApiModelProperty("SN")
	@Alias("SN")
	@ExcelCell(index = 1)
	private String sn;
	/**
	 * PID
	 */
	@ApiModelProperty("PID")
	@Alias("PID")
	@ExcelCell(index = 2)
	private Long pid;
	/**
	 * 品类名称
	 */
	@ApiModelProperty("品类名称")
	@Alias("品类名称")
	@ExcelCell(index = 3)
	private String categoryName;
	/**
	 * 品牌名称
	 */
	@ApiModelProperty("品牌名称")
	@Alias("品牌名称")
	@ExcelCell(index = 4)
	private String brandName;
	/**
	 * 产品型号
	 */
	@ApiModelProperty("产品型号")
	@Alias("产品型号")
	@ExcelCell(index = 5)
	private String productModel;
	/**
	 * 商品型号/Model #
	 */
	@ApiModelProperty("商品型号/Model #")
	@Alias("商品型号/Model #")
	@ExcelCell(index = 6)
	private String commodityModel;
	/**
	 * 设备类型
	 *
	 * @link com.chervon.technology.domain.enums.ProductTypeEnum
	 */
	@ApiModelProperty("设备类型")
	@Alias("设备类型")
	@ExcelCell(index = 7)
	private String productType;
	/**
	 * 注册手机号
	 */
	@ApiModelProperty("注册手机号")
	@Alias("注册手机号")
	@ExcelCell(index = 8)
	private String devicePhoneNum;
	/**
	 * 在线状态
	 */
	@ApiModelProperty("在线状态")
	@Alias("在线状态")
	@ExcelCell(index = 9)
	private DeviceOnlineStatusEnum isOnline;
	/**
	 * 设备状态：DISABLE 停用 NORMAL 正常
	 */
	@ApiModelProperty("设备状态：DISABLE 停用 NORMAL 正常")
	@Alias("设备状态")
	@ExcelCell(index = 10)
	private DeviceStatusEnum status;
	/**
	 * 已绑定用户ID
	 */
	@ApiModelProperty("已绑定用户ID")
	@Alias("已绑定用户ID")
	@ExcelCell(index = 11)
	@JsonSerialize
	private List<Long> boundUserIds;
	/**
	 * 使用状态
	 */
	@ApiModelProperty("使用状态")
	@Alias("使用状态")
	@ExcelCell(index = 12)
	private DeviceUsageStatusEnum usageStatus;
	/**
	 * 激活用户ID
	 */
	@ApiModelProperty("激活用户ID")
	@Alias("激活用户ID")
	@ExcelCell(index = 13)
	private Long activationUserId;
	/**
	 * 设备激活时间，设备注册的时候设置设备为激活状态
	 */
	@ApiModelProperty("设备激活时间，设备注册的时候设置设备为激活状态")
	@Alias("激活时间")
	@ExcelCell(index = 14)
	private String activationTimeStr;
	/**
	 * 设备最后一次上线时间
	 */
	@ApiModelProperty("设备最后一次上线时间")
	@Alias("最后上线时间")
	@ExcelCell(index = 15)
	private String lastLoginTimeStr;


	public String getIsOnline() {
		return isOnline == null ? null : isOnline.getZh();
	}

	public String getStatus() {
		return status == null ? null : status.getZh();
	}

	public String getUsageStatus() {
		return usageStatus == null ? null : usageStatus.getZh();
	}

	public String getProductType() {
		return ProductTypeEnum.getLabelByValue(productType);
	}

}