package com.chervon.technology.domain.dto.rule.engine;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2022/9/16 17:39
 */
@Data
public class RuleEngineFaultMessageDto implements Serializable {
    /**
     * 规则引擎Id
     */
    @ApiModelProperty("规则引擎Id")
    private Long ruleEngineId;
    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    private List<Long> productIds;
    /**
     * 不包含的产品id
     */
    @ApiModelProperty("不包含的产品id")
    private List<Long> notProductIds;
}
