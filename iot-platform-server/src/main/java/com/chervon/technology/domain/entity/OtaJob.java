package com.chervon.technology.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.technology.api.enums.OtaJobDevelopStatus;
import com.chervon.technology.api.enums.OtaJobReleaseStatus;
import com.chervon.technology.domain.enums.UpgradeMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 升级任务
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("ota_job")
@ApiModel(value = "OtaJob对象", description = "升级任务")
public class OtaJob extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品PID
     **/
    @ApiModelProperty(value = "产品PID", required = true)
    @NotNull
    private Long productId;

    @ApiModelProperty("技术版本号")
    private String technologyVersion;

    @ApiModelProperty("自定义版本号，运营版本号")
    private String customVersion;

    @ApiModelProperty("固件数量")
    private Integer packageCount;

    @ApiModelProperty("升级内容，用户侧不展示")
    private String upgradeContent;

    @ApiModelProperty("发布升级说明，用户侧展示")
    private String releaseContent;

    @ApiModelProperty("升级方式 NOT_FORCE非强制升级 FORCE强制升级 SILENCE静默升级")
    private UpgradeMode upgradeMode;

    @ApiModelProperty("升级任务的开发状态：DEVELOPING开发中 CLOSING封板审核中 CLOSE_REFUSED封板被驳回 CLOSED已封板")
    private OtaJobDevelopStatus developStatus;

    @ApiModelProperty("升级任务的发布状态：RELEASE_WAITING待发布 TESTING测试验证中 RELEASING发布审核中 "
        + "RELEASE_TEST_REFUSED 测试验证未通过 RELEASE_REFUSED发布被驳回 "
        + "RELEASE_READY发布就绪，发布时间未到，RELEASED已发布 "
        + "OVER已结束  R撤回审核中  REVOKED已撤回 REVOKE_REFUSED撤回被驳回")
    private OtaJobReleaseStatus releaseStatus;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("任务开始时间的时区")
    private String startZone;

    @ApiModelProperty("任务开始时间")
    private Date startTime;

    @ApiModelProperty("任务结束时间的时区")
    private String endZone;

    @ApiModelProperty("任务结束时间")
    private Date endTime;

    /**
     * 开始时间类型  1 立即  2 定时
     */
    @ApiModelProperty("开始时间类型  1 立即  2 定时")
    private Integer startType;

    /**
     * 结束时间类型  1 永不过期  2 定时
     */
    @ApiModelProperty("结束时间类型  1 永不过期  2 定时")
    private Integer endType;
    /**
     * 最近申请人
     */
    @ApiModelProperty("最近申请人")
    private String applyBy;
    /**
     * 最近申请时间
     */
    @ApiModelProperty("最近申请时间")
    private LocalDateTime applyTime;
    /**
     * 最近审批人
     */
    @ApiModelProperty("最近审批人")
    private String ensuredBy;
    /**
     * 最近审批时间
     */
    @ApiModelProperty("最近审批时间")
    private LocalDateTime ensuredTime;


    public static final String ID = "id";

    public static final String PRODUCT_ID = "product_id";

    public static final String TECHNOLOGY_VERSION = "technology_version";

    public static final String CUSTOM_VERSION = "custom_version";

    public static final String PACKAGE_COUNT = "package_count";

    public static final String UPGRADE_CONTENT = "upgrade_content";

    public static final String RELEASE_CONTENT = "release_content";

    public static final String UPGRADE_MODE = "upgrade_mode";

    public static final String DEVELOP_STATUS = "develop_status";

    public static final String RELEASE_STATUS = "release_status";

    public static final String DESCRIPTION = "description";

    public static final String START_TIME = "start_time";

    public static final String END_TIME = "end_time";

    public static final String CREATE_BY = "create_by";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_BY = "update_by";

    public static final String UPDATE_TIME = "update_time";

    public static final String IS_DELETED = "is_deleted";

}
