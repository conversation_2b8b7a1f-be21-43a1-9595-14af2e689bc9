package com.chervon.technology.domain.dto.ota;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @className PublishOtaDto
 * @description 固件包
 * @date 2022/7/13 10:43
 */
@ApiModel("固件包")
@Data
public class PackageDto {

    /**
     * 固件ID
     **/
    @ApiModelProperty("固件ID")
    private Long packageId;

    /**
     * 固件名称
     **/
    @ApiModelProperty("固件名称")
    private String packageName;

    /**
     * 固件版本号
     **/
    @ApiModelProperty("固件版本号")
    private String packageVersion;

    /**
     * 总成零件号
     **/
    @ApiModelProperty("总成零件号")
    private String componentNo;

    /**
     * 总成零件名称
     **/
    @ApiModelProperty("总成零件名称")
    private String componentName;

    /**
     * 最低兼容版本号
     **/
    @ApiModelProperty("最低兼容版本号")
    private String minimumVersion;

    /**
     * 升级方式： 0：非强制升级 1：强制升级 2：静默升级
     **/
    @ApiModelProperty("升级方式： 0：非强制升级 1：强制升级 2：静默升级")
    private Integer upgradeMode;

    /**
     * 排序编号
     **/
    @ApiModelProperty("排序编号")
    private Integer orderNum;



}
