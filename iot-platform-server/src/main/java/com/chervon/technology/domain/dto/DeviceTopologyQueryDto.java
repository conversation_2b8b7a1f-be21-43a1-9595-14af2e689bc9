package com.chervon.technology.domain.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-07-26 14:05
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceTopologyQueryDto extends PageRequest implements Serializable {
    /**
     * 设备ID
     */
    @ApiModelProperty(value = "设备ID", required = true)
    @NotEmpty
    String deviceId;

    /**
     * 拓扑设备ID-上行设备/下行设备
     */
    @ApiModelProperty(value = "拓扑设备ID")
    String topologyDeviceId;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", required = false)
    Long startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", required = false)
    Long endTime;

    /**
     * 数据上下行类型 0上行 1下行
     */
    @ApiModelProperty(value = "数据上下行类型 0上行 1下行", required = true)
    @NotNull
    Integer type;
}
