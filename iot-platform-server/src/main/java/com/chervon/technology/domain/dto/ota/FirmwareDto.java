package com.chervon.technology.domain.dto.ota;

import com.chervon.technology.api.enums.PackageTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 固件管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "Firmware对象", description = "固件管理")
public class FirmwareDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总成零件号
     **/
    @NotNull
    @ApiModelProperty("总成零件号")
    private String componentNo;

    /**
     * 固件名称
     **/
    @ApiModelProperty("固件名称")
    private String packageName;

    /**
     * 固件版本号
     **/
    @NotNull
    @ApiModelProperty("固件版本号")
    private String packageVersion;

    /**
     * 显示版本号，生成用户版本号使用
     **/
    @ApiModelProperty("显示版本号，生成用户版本号使用")
    private String displayVersion;

    /**
     * 文件大小 单位字节
     **/
    @ApiModelProperty("文件大小 单位字节")
    private Long size;

    /**
     * 最低兼容的版本
     **/
    @ApiModelProperty("最低兼容的版本")
    private String minimumVersion;

    /**
     * 固件类型, FULL_PACKAGE:全包, DELTA_PACKAGE:差分包
     **/
    @ApiModelProperty("固件类型, FULL_PACKAGE:全包, DELTA_PACKAGE:差分包")
    private PackageTypeEnum packageType;

    /**
     * 固件包存放在s3中的key
     **/
    @ApiModelProperty("固件包存放在s3中的key")
    private String packageKey;

    /**
     * 升级包的hash值, sha1算法
     **/
    @ApiModelProperty("升级包的hash值, sha1算法")
    private String hash;

    /**
     * 排序编号
     **/
    @ApiModelProperty("排序编号")
    private Integer orderNum;

}
