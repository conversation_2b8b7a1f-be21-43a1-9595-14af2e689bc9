package com.chervon.technology.domain.dto.firmware;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @date 2022-08-03
 * <AUTHOR>
 */
@Data
public class ListVersionDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品PID")
    @NotNull
    private Long productId;

    @ApiModelProperty("总成零件号")
    @NotNull
    private String componentNo;
}
