package com.chervon.technology.rpc;

import com.chervon.technology.api.RemoteComponentService;
import com.chervon.technology.service.ProductComponentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-11-07 11:23
 **/
@DubboService
@Service
@Slf4j
public class RemoteComponentServiceImpl implements RemoteComponentService {
    @Resource
    private ProductComponentService productComponentService;

    @Override
    public List<String> listAssemblySnListByProductId(Long productId) {
        return productComponentService.listComponentNosByPid(productId);
    }
}
