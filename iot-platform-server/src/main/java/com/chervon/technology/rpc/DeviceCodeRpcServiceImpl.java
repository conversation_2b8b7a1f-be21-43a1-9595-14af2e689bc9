package com.chervon.technology.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.technology.api.RemoteDeviceCodeService;
import com.chervon.technology.api.vo.DeviceCodeRpcVo;
import com.chervon.technology.domain.dataobject.DeviceCode;
import com.chervon.technology.mapper.DeviceCodeMapper;
import com.chervon.technology.service.DeviceCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-16 18:15
 **/
@DubboService
@Slf4j
public class DeviceCodeRpcServiceImpl implements RemoteDeviceCodeService {
    @Resource
    private DeviceCodeService deviceCodeService;

    @Override
    public DeviceCodeRpcVo deviceCodeDetail(String deviceId) {
        LambdaQueryWrapper<DeviceCode> queryWrapper = new LambdaQueryWrapper<DeviceCode>()
            .eq(DeviceCode::getDeviceId, deviceId)
            .select(DeviceCode::getDeviceId, DeviceCode::getSn, DeviceCode::getProductSnCode, DeviceCode::getStatus);
        DeviceCode deviceCode = deviceCodeService.getOne(queryWrapper);
        if (null != deviceCode) {
            return ConvertUtil.convert(deviceCode, DeviceCodeRpcVo.class);
        }
        return null;
    }

    @Override
    public DeviceCodeRpcVo deviceCodeDetailBySn(String sn) {
        LambdaQueryWrapper<DeviceCode> queryWrapper = new LambdaQueryWrapper<DeviceCode>()
            .eq(DeviceCode::getSn, sn)
            .select(DeviceCode::getDeviceId, DeviceCode::getSn, DeviceCode::getProductSnCode, DeviceCode::getStatus);
        DeviceCode deviceCode = deviceCodeService.getOne(queryWrapper);
        if (null != deviceCode) {
            return ConvertUtil.convert(deviceCode, DeviceCodeRpcVo.class);
        }
        return null;
    }

    @Override
    public List<DeviceCodeRpcVo> listByDeviceId(List<String> deviceIdList) {
        LambdaQueryWrapper<DeviceCode> queryWrapper = new LambdaQueryWrapper<DeviceCode>()
            .in(DeviceCode::getDeviceId, deviceIdList)
            .select(DeviceCode::getStatus, DeviceCode::getDeviceId);
        List<DeviceCode> deviceCodes = deviceCodeService.list(queryWrapper);
        if (null != deviceCodes) {
            return ConvertUtil.convertList(deviceCodes, DeviceCodeRpcVo.class);
        }
        return null;
    }

    @Override
    public String createDeviceCodeBySn(String sn, String snCode) {
        return deviceCodeService.createDeviceCodeBySn(sn, snCode);
    }
}
