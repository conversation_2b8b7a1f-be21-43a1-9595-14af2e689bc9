package com.chervon.technology.rpc;

import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.RemoteDeviceCodeManageService;
import com.chervon.technology.api.dto.DeviceCodeAddDto;
import com.chervon.technology.api.dto.EditDeviceCodeStatusDto;
import com.chervon.technology.api.dto.SearchDeviceCodeDto;
import com.chervon.technology.api.vo.DeviceCodeExcel;
import com.chervon.technology.api.vo.DeviceCodeVo;
import com.chervon.technology.service.DeviceCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2023/2/1 17:01
 */
@DubboService
@Slf4j
public class RemoteDeviceCodeManageServiceImpl implements RemoteDeviceCodeManageService {

    @Autowired
    private DeviceCodeService deviceCodeService;


    @Override
    public void addDeviceCode(String lang, DeviceCodeAddDto deviceAdd) {
        LocaleContextHolder.setLocale(new Locale(lang));
        deviceCodeService.addDeviceCode(deviceAdd);
    }

    @Override
    public void editDeviceCodeStatus(String lang, EditDeviceCodeStatusDto editDeviceCodeStatusDto) {
        LocaleContextHolder.setLocale(new Locale(lang));
        deviceCodeService.editDeviceCodeStatus(editDeviceCodeStatusDto);
    }

    @Override
    public List<String> uploadDeviceCodeFile(String lang, byte[] deviceCodeFileArr, Long productId) {
        LocaleContextHolder.setLocale(new Locale(lang));
        return deviceCodeService.uploadDeviceCodeFile(deviceCodeFileArr, productId);
    }

    @Override
    public PageResult<DeviceCodeVo> getListDevice(String lang, SearchDeviceCodeDto searchDeviceCodeDto) {
        LocaleContextHolder.setLocale(new Locale(lang));
        return deviceCodeService.getListDevice(searchDeviceCodeDto);
    }

    @Override
    public List<DeviceCodeExcel> export(String lang, SearchDeviceCodeDto searchDeviceCode) {
        LocaleContextHolder.setLocale(new Locale(lang));
        return deviceCodeService.export(searchDeviceCode);
    }
}
