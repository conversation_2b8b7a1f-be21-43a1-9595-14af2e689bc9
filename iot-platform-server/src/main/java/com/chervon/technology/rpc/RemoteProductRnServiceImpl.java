package com.chervon.technology.rpc;

import com.chervon.technology.api.RemoteProductRnService;
import com.chervon.technology.api.dto.LastRnPackageDto;
import com.chervon.technology.api.vo.LastRnPackageInfoVo;
import com.chervon.technology.entity.ProductRn;
import com.chervon.technology.service.ProductRnService;
import com.chervon.technology.util.VersionCompareUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/9/16 16:10
 */
@Service
@Slf4j
@DubboService
public class RemoteProductRnServiceImpl implements RemoteProductRnService {

    @Autowired
    private ProductRnService productRnService;

    @Override
    public LastRnPackageInfoVo lastInfo(LastRnPackageDto req, Long userId) {
        LastRnPackageInfoVo res = new LastRnPackageInfoVo();
        ProductRn max = productRnService.getMaxRn(req);
        if (max == null) {
            res.setHasLast(false);
            productRnService.rnNameToRedis(null, userId, req.getProductId(), req.getAppType(), req.getAppVersion());
            return res;
        }
        if (StringUtils.isBlank(req.getRnVersion())) {
            res.setHasLast(true);
            res.setLastRnUrl(max.getResourceUrl());
            res.setLastRnVersion(max.getRnVersion());
            res.setRnBundleName(max.getRnName());
            productRnService.rnNameToRedis(max.getRnName(), userId, req.getProductId(), req.getAppType(), req.getAppVersion());
            return res;
        }
        if (VersionCompareUtils.compareVersion(max.getRnVersion(), req.getRnVersion()) <= 0) {
            res.setHasLast(false);
        } else {
            res.setHasLast(true);
            res.setLastRnUrl(max.getResourceUrl());
            res.setLastRnVersion(max.getRnVersion());
            res.setRnBundleName(max.getRnName());
        }
        productRnService.rnNameToRedis(max.getRnName(), userId, req.getProductId(), req.getAppType(), req.getAppVersion());
        return res;
    }
}
