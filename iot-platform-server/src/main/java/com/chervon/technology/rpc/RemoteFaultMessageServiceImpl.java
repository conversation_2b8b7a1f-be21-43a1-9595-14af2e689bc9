package com.chervon.technology.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.RemoteFaultMessageService;
import com.chervon.technology.api.dto.FaultMessageRecordSearchDto;
import com.chervon.technology.api.dto.FaultMessageResultCountDto;
import com.chervon.technology.api.vo.FaultMessageRecordVo;
import com.chervon.technology.domain.dataobject.FaultMessage;
import com.chervon.technology.service.FaultMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.i18n.LocaleContextHolder;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-08-16 18:15
 **/
@DubboService
@Slf4j
public class RemoteFaultMessageServiceImpl implements RemoteFaultMessageService {
    @Resource
    private FaultMessageService faultMessageService;

    @Override
    public Long getMessageTemplateUsedTimes(Long messageTemplateId) {
        return faultMessageService.count(
                new LambdaQueryWrapper<FaultMessage>().eq(FaultMessage::getMessageTemplateId, messageTemplateId));
    }

    @Override
    public Map<Long, Long> selectMessageTemplateCount(List<Long> listTemplateId) {
        return faultMessageService.selectMessageTemplateCount(listTemplateId);
    }


    @Override
    public PageResult<FaultMessageRecordVo> pageRecord(BaseRemoteReqDto<FaultMessageRecordSearchDto> req) {
        LocaleContextHolder.setLocale(new Locale(req.getLanguage()));
        PageResult<FaultMessageRecordVo> faultMessageRecordVo = faultMessageService.pageRecord(req.getReq());
        return faultMessageRecordVo;
    }

    @Override
    public List<FaultMessageRecordVo> listRecord(BaseRemoteReqDto<FaultMessageRecordSearchDto> req) {
        LocaleContextHolder.setLocale(new Locale(req.getLanguage()));
        List<FaultMessageRecordVo> faultMessageRecordVo = faultMessageService.listRecord(req.getReq());
        return faultMessageRecordVo;
    }

    @Override
    public void updateFaultMsgCount(List<FaultMessageResultCountDto> countDtoList) {
        faultMessageService.updateMsgCount(countDtoList);
    }
}
