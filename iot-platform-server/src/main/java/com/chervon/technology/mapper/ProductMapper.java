package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.technology.api.dto.ProductDto;
import com.chervon.technology.api.dto.ProductOperationSearchDto;
import com.chervon.technology.api.vo.ProductReleaseVo;
import com.chervon.technology.domain.dataobject.Product;
import com.chervon.technology.domain.dto.product.SearchProductDto;
import com.chervon.technology.domain.vo.product.OperationProductMergeBaseVo;
import com.chervon.technology.domain.vo.product.OperationProductMergeVo;
import com.chervon.technology.domain.vo.product.ProductReleaseMergeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品信息表
 *
 * <AUTHOR>
 * @date 2022-04-25 19:00:12
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 分页查询产品
     *
     * @param page          分页信息
     * @param search        搜索信息
     * @param status        封板状态
     * @param releaseStatus 发布状态
     * @return 产品分页信息
     */
    Page<Product> selectByPage(@Param("page") Page page, @Param("search") ProductDto search,
                               @Param("status") List<String> status,
                               @Param("releaseStatus") List<String> releaseStatus);

    /**
     * 技术平台搜索产品信息，包含草稿和原始数据
     *
     * @param page   分页信息
     * @param search 搜索信息
     * @return 产品分页信息
     */
    Page<Product> getProductSource(@Param("page") Page page,
                                   @Param("search") SearchProductDto search);



    /**
     * get product List
     *
     * @param page               page
     * @param search             search
     * @param productNameLangIds 多语言Ids
     * @return product list
     */
    Page<Product> getOperationProductList(@Param("page") Page page, @Param("search") ProductOperationSearchDto search,
                                          @Param("productNameLangIds") List<Long> productNameLangIds);

    /**
     * 运营平台获取产品发布列表
     *
     * @param page               分页信息
     * @param search             搜索信息
     * @param pIds               拥有草稿权限的产品，null时所有产品都有权限
     * @param productNameLangIds 产品名称多语言Id
     * @return 产品发布分页信息
     */
    Page<ProductReleaseMergeVo> getOperationReleaseList(@Param("page") Page page,
                                                        @Param("search") ProductOperationSearchDto search,
                                                        @Param("pIds") List<Long> pIds,
                                                        @Param("productNameLangIds") List<Long> productNameLangIds);


    /**
     * 运营平台获取产品发布列表，所有原始产品
     *
     * @param page               分页信息
     * @param search             搜索信息
     * @param productNameLangIds 产品多语言Ids
     * @return 产品发布分页信息
     */
    Page<ProductReleaseVo> getSourceReleaseList(@Param("page") Page page,
                                                @Param("search") ProductOperationSearchDto search,
                                                @Param("productNameLangIds") List<Long> productNameLangIds);

    /**
     * 获取一个list的 pIds的产品基本信息
     *
     * @param pIds 产品所在PIds
     * @return 产品信息
     */
    List<OperationProductMergeBaseVo> getOperationProductBase(@Param("pIds") List<Long> pIds);

    /**
     * 获取产品类型列表，按照给定列表顺序排序
     *
     * @param deviceIds APP用户绑定设备ID列表，按照sort值排序
     * @return 产品列表
     */
    List<Product> getProductTypeListByDeviceIds(@Param("deviceIds") List<String> deviceIds);

    /**
     * 获取已发布产品最大appshoworder
     * @return
     */
    Integer getReleaseProductAppShowMaxOrder();
}
