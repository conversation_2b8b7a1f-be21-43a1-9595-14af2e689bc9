package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.technology.domain.dataobject.DeviceCode;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 设备表
 *
 * <AUTHOR>
 * @date 2022-04-25 19:00:12
 */
@Mapper
public interface DeviceCodeMapper extends BaseMapper<DeviceCode> {

    /**
     * 获取多码表中的设备Id列表
     *
     * @return 设备Id列表
     */
    List<String> getDeviceIds();

    /**
     * 获取多码表中的SN列表
     *
     * @return Sn列表
     */
    List<String> getSns();
}
