package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.technology.domain.dataobject.DevicePath;
import com.chervon.technology.domain.dataobject.DevicePathInstance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2022/8/11 17:26
 */
@Mapper
public interface DevicePathMapper extends BaseMapper<DevicePath> {

    /**
     * 判断表是否存在
     *
     * @param deviceId 设备id
     * @return 大于0则表存在
     */
    Integer isExitsTable(@Param("deviceId") String deviceId);

    /**
     * 创建表
     *
     * @param deviceId 设备id
     */
    void createTable(@Param("deviceId") String deviceId);

    void insertData(@Param("devicePath") DevicePathInstance devicePath);

}
