package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.technology.api.dto.charging.SmsChargingDetailPageDto;
import com.chervon.technology.api.vo.charging.SmsChargingDetailVo;
import com.chervon.technology.domain.dataobject.Charging;
import com.chervon.technology.domain.dataobject.SmsCharging;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SmsChargingMapper extends BaseMapper<SmsCharging> {

    /**
     * 按条件分页查询计费明细情况
     *
     * @param page   分页
     * @param search 条件
     * @return 分页数据
     */
    IPage<SmsChargingDetailVo> smsChargingDetailPage(IPage<SmsChargingDetailVo> page,
                                                     @Param("search") SmsChargingDetailPageDto search,
                                                     @Param("msgTitleLangIds") List<Long> msgTitleLangIds);

    /**
     * 按条件列表查询计费明细情况
     *
     * @param search 条件
     * @return 列表数据
     */
    List<SmsChargingDetailVo> smsChargingDetailList(@Param("search") SmsChargingDetailPageDto search,
                                                    @Param("msgTitleLangIds") List<Long> msgTitleLangIds);
}
