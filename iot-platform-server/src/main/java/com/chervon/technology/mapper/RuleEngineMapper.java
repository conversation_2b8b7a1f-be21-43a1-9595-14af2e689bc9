package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.technology.domain.dataobject.RuleEngine;
import com.chervon.technology.domain.dto.rule.engine.RuleEngineFaultMessageDto;
import com.chervon.technology.domain.vo.rule.engine.RuleEngineFaultMessageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-09-07 20:17
 **/
@Mapper
public interface RuleEngineMapper extends BaseMapper<RuleEngine> {
    List<RuleEngineFaultMessageVo> ruleEngineFaultMessageList(RuleEngineFaultMessageDto ruleEngineFaultMessageDto);
}
