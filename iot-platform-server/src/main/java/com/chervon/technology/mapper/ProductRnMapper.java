package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.technology.api.dto.rn.OpReManageRnPageDto;
import com.chervon.technology.api.vo.rn.OpReManageRnPageVo;
import com.chervon.technology.entity.ProductRn;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 产品和RN管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-07
 */
@Mapper
public interface ProductRnMapper extends BaseMapper<ProductRn> {


}
