package com.chervon.technology.req;

import com.chervon.technology.api.vo.rn.RnItemVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022-07-11
 */
@Data
@ApiModel(description = "产品下添加或者修改rn数据")
public class ProductRnAddOrUpdateDto {

    @ApiModelProperty("产品RN Id，新增时为null，编辑时必填")
    private Long productRnId;

    @ApiModelProperty("产品Id")
    @NotNull
    private Long productId;

    @ApiModelProperty("产品下选择的rn信息")
    private RnItemVo rn;

    @ApiModelProperty("更新内容")
    @NotBlank
    private String updateContent;

    @ApiModelProperty("备注")
    private String remark;
}
