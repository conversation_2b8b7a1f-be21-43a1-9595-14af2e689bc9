package com.chervon.technology.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/9/13 11:01
 */
@Data
@ApiModel(description = "添加、编辑基础设备错误码对象")
public class CategoryDto {

    @ApiModelProperty(value = "品类id")
    @NotNull
    private Long categoryId;

    @ApiModelProperty(value = "品类编码")
    @NotBlank
    private String categoryCode;

    @ApiModelProperty(value = "基础设备错误码id，新增不传，编辑要传")
    private Long baseId;

}
