package com.chervon.technology.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/7/19 16:22
 */
@Data
@ApiModel(description = "产品下的RN的操作中的查看驳回入参")
public class ProductRnViewRefuseDto {

    @ApiModelProperty(value = "产品下RN id")
    private Long productRnId;

    @ApiModelProperty(value = "操作类型" +
            "ViewRefuseCloseReason：查看封板被驳回原因\n" +
            "ViewRefuseReleaseReason：查看发布被驳回原因")
    private String operation;
}
