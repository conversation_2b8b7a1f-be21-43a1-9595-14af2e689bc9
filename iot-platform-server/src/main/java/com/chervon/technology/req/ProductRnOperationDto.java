package com.chervon.technology.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/7/19 14:59
 */
@Data
@ApiModel(description = "产品下RN操作对象")
public class ProductRnOperationDto {

    @ApiModelProperty(value = "产品下RN id")
    private Long productRnId;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "操作类型" +
            "apply_close：申请封板\n" +
            "ensure_close：确认封板\n" +
            "refuse_close：封板驳回\n" +
            "cancel_apply_close：取消封板申请\n" +
            "view_refuse_close_reason：查看封板被驳回原因")
    private String operation;

    public ProductRnOperationDto(Long productRnId, String operation) {
        this.productRnId = productRnId;
        this.operation = operation;
    }

    public ProductRnOperationDto(Long productRnId, String reason, String operation) {
        this.productRnId = productRnId;
        this.reason = reason;
        this.operation = operation;
    }
}
