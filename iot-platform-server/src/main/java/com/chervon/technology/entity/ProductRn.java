package com.chervon.technology.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 产品和RN管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_rn")
@ApiModel(value = "ProductRn对象", description = "产品和RN管理表")
public class ProductRn extends BaseDo {

    @ApiModelProperty("产品Id")
    private Long productId;

    @ApiModelProperty(value = "rnId")
    private Integer rnId;
    @ApiModelProperty(value = "应用id")
    private String appId;
    @ApiModelProperty(value = "应用名称，1 ego 2 fleet")
    private String appName;
    @ApiModelProperty(value = "rn名称")
    private String rnName;
    @ApiModelProperty(value = "rn nickname")
    private String rnNickName;
    @ApiModelProperty(value = "rn适配原生类型 android  ios")
    private String rnType;
    @ApiModelProperty(value = "rn包url")
    private String resourceUrl;
    @ApiModelProperty(value = "rn包大小")
    private Long rnSize;
    @ApiModelProperty(value = "rn包版本")
    private String rnVersion;
    @ApiModelProperty(value = "rn包更新日志")
    private String rnUpdateLog;
    @ApiModelProperty(value = "适配原生最小版本")
    private String versionMin;
    @ApiModelProperty(value = "适配原生最大版本")
    private String versionMax;

    @ApiModelProperty("更新内容")
    private String updateContent;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("RN包发布状态")
    private String releaseStatus;







}
