package com.chervon.technology.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.technology.domain.dto.ota.DeviceOtaListDto;
import com.chervon.technology.domain.entity.OtaDeviceResult;
import com.chervon.technology.domain.vo.ota.DeviceOtaHistoryVo;
import com.chervon.technology.mapper.OtaDeviceResultMapper;
import com.chervon.technology.service.OtaDeviceResultService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Service
public class OtaDeviceResultServiceImpl extends ServiceImpl<OtaDeviceResultMapper, OtaDeviceResult> implements OtaDeviceResultService {

    @Override
    public Long countSucceed(String jobId) {
        LambdaUpdateWrapper<OtaDeviceResult> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OtaDeviceResult::getJobId, jobId).
            eq(OtaDeviceResult::getStatus, "SUCCEEDED");
        return this.count(wrapper);
    }

    @Override
    public Long countFailed(String jobId) {
        LambdaUpdateWrapper<OtaDeviceResult> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OtaDeviceResult::getJobId, jobId).
            eq(OtaDeviceResult::getStatus, "FAILED");
        return this.count(wrapper);
    }

    @Override
    public PageResult<DeviceOtaHistoryVo> getOtaRecord(DeviceOtaListDto deviceOtaListDto) {
        LambdaQueryWrapper<OtaDeviceResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OtaDeviceResult::getDeviceId, deviceOtaListDto.getDeviceId())
                .orderByDesc(OtaDeviceResult::getUpdateTime);
        Page page = new Page(deviceOtaListDto.getPageNum(), deviceOtaListDto.getPageSize());
        Page<OtaDeviceResult> pageResult = this.page(page, wrapper);
        List<DeviceOtaHistoryVo> otaHistoryVos = new ArrayList<>();
        pageResult.getRecords().stream().forEach(otaResult -> {
            DeviceOtaHistoryVo vo = ConvertUtil.convert(otaResult, DeviceOtaHistoryVo.class);
            vo.setUpgradeTime(otaResult.getUpdateTime());
            vo.setUserId(otaResult.getAppUserId() == null ? null : DesensitizedUtil.idCardNum(otaResult.getAppUserId()+"", 7, 8));
            otaHistoryVos.add(vo);
        });
        PageResult result = new PageResult(deviceOtaListDto.getPageNum(),
                deviceOtaListDto.getPageSize());
        result.setPages(pageResult.getPages());
        result.setTotal(pageResult.getTotal());
        result.setList(otaHistoryVos);
        return result;
    }

    @Override
    public OtaDeviceResult getLatestDeviceOtaRecord(String deviceId) {
        LambdaQueryWrapper<OtaDeviceResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OtaDeviceResult::getDeviceId, deviceId)
                .eq(OtaDeviceResult::getStatus, "SUCCEEDED")
                .eq(OtaDeviceResult::getIsDeleted, 0)
                .orderByDesc(OtaDeviceResult::getCreateTime)
                .last("limit 1");
        return this.getOne(wrapper);
    }

    @Override
    public OtaDeviceResult getOtaDeviceResultByDeviceIdWithJobId(String deviceId,Long jobId) {
        return  getOne(Wrappers.<OtaDeviceResult>lambdaQuery()
                .eq(OtaDeviceResult::getDeviceId,deviceId)
                .eq(OtaDeviceResult::getJobId,jobId)
        );
    }
}
