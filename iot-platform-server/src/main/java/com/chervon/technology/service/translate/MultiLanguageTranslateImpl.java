package com.chervon.technology.service.translate;

import cn.hutool.core.collection.CollectionUtil;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.chervon.common.web.core.TranslateService;
import com.chervon.common.web.entity.MetaContext;
import com.chervon.common.web.util.UserContext;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.vo.CategoryVo;
import com.chervon.technology.config.MultiLanguageUtil;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通过注解统一获取国际化后内容*
 * <AUTHOR> 2022/12/6
 */
@Component
public class MultiLanguageTranslateImpl implements TranslateService<CategoryVo,MultiLanguageVo,String> {
    @DubboReference
    private RemoteMultiLanguageService languageService;

    public String getInstanceCode(){
        return "multiLanguage";
    }

    @Override
    public MultiLanguageVo getSourceValue(Field field, CategoryVo dataEntity) throws IllegalAccessException {
        String code = null;
        Object objValue = field.get(dataEntity);
        if (objValue != null) {
            code = objValue.toString();
        } else {
            return null;
        }
        // 获取多语言code
        MultiLanguageBo bo = languageService.getById(code);
        MultiLanguageVo languageVo = new MultiLanguageVo(bo.getLangId(),
                MultiLanguageUtil.getByLangCode(bo.getLangCode(), UserContext.getClientInfo().getLanguage()));
        return languageVo;
    }

    @Override
    public void setTargetValue(CategoryVo dataDto, MultiLanguageVo result, List<String> targetField) throws IllegalAccessException {
        Class<?> aClass = dataDto.getClass();
        List<Field> fields = TranslateUtils.getBindFields(aClass,targetField);
        for (Field field : fields) {
            field.setAccessible(true);
            if ("categoryName".equals(field.getName())) {
                field.set(dataDto, result);
            }
        }
    }

    /**
     * * 批量获取原字段对应的值列表
     * @param context
     * @return
     * @throws IllegalAccessException
     */
    @Override
    public void batchGetSourceValue(MetaContext<String,MultiLanguageVo> context){
        final Set<String> listSourceValue = context.getListSourceValue();
        if(CollectionUtil.isEmpty(listSourceValue)){
            return;
        }
        //批量读取国际化原值
        List<String> sourceName=new ArrayList<>(listSourceValue);
        final List<MultiLanguageBo> multiLanguageBos = languageService.listByIds(sourceName);
        final List<String> collect = multiLanguageBos.stream().map(a -> a.getLangCode()).collect(Collectors.toList());

        Map<String, String> codeMap = MultiLanguageUtil.getByLangCodes(collect);
        final List<MultiLanguageVo> languageVos = multiLanguageBos.stream().map(e -> {
            MultiLanguageVo vo = new MultiLanguageVo();
            vo.setLangId(e.getLangId());
            vo.setMessage(codeMap.get(e.getLangCode()));
            return vo;
        }).collect(Collectors.toList());
        //设置目标值
        context.setListTargetValue(languageVos);
    }

    /**
     * * 批量根据原值结果列表赋值目标结果对象集合
     * @param dataEntity 数据实体列表
     * @throws IllegalAccessException
     */
    @Override
    public void batchSetTargetValue(List<CategoryVo> dataEntity, MetaContext<String,MultiLanguageVo> context) throws IllegalAccessException {
        Field sourceField=context.getSourceField();
        sourceField.setAccessible(true);
        List<Field> targetField=context.getTargetField();
        final Map<Long, MultiLanguageVo> multiLanguageVoMap = context.getListTargetValue().stream().collect(Collectors.toMap(a -> a.getLangId(), Function.identity()));
        for(CategoryVo dataDto:dataEntity){
            final Long sourceValue = (Long) sourceField.get(dataDto);
            final MultiLanguageVo result = multiLanguageVoMap.get(sourceValue);
            if(Objects.isNull(result)){
                continue;
            }
            for (Field field : targetField) {
                field.setAccessible(true);
                if ("categoryName".equals(field.getName())) {
                    field.set(dataDto, result.getMessage());
                }
            }
        }
    }
}