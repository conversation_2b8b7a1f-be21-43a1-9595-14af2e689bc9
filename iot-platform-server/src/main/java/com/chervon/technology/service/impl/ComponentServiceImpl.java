package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.domain.dataobject.Component;
import com.chervon.technology.domain.dto.component.AddComponentDto;
import com.chervon.technology.domain.dto.component.EditComponentDto;
import com.chervon.technology.domain.dto.component.SearchComponentDto;
import com.chervon.technology.domain.vo.component.ComponentVo;
import com.chervon.technology.mapper.ComponentMapper;
import com.chervon.technology.service.ComponentService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-14
 */
@Service
@Slf4j
@AllArgsConstructor
public class ComponentServiceImpl extends ServiceImpl<ComponentMapper, Component> implements ComponentService {

    private final ComponentMapper componentMapper;


    @Override
    public Component getByNo(String componentNo) {
        LambdaQueryWrapper<Component> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Component::getComponentNo, componentNo);
        return this.getOne(wrapper);
    }

    @Override
    public List<Component> getByNos(List<String> componentNos) {
        LambdaQueryWrapper<Component> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Component::getComponentNo, componentNos);
        return this.list(wrapper);
    }

    @Override
    public void addComponent(AddComponentDto addComponent) {
        // 检查总成零件类型是否存在
        Component component = this.getByNo(addComponent.getComponentNo());
        if (null != component) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMPONENT_ALREADY_EXIST, addComponent.getComponentNo());
        }
        component = ConvertUtil.convert(addComponent, Component.class);
        this.save(component);
    }

    @Override
    public void editComponent(EditComponentDto editComponent) {
        Component component = this.getById(editComponent.getId());
        if (null == component) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMPONENT_NOT_EXIST, editComponent.getId());
        }
        if (!editComponent.getComponentNo().equals(component.getComponentNo())) {
            Component com = this.getByNo(editComponent.getComponentNo());
            if (null != com) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMPONENT_ALREADY_EXIST, editComponent.getComponentNo());
            }
        }

        boolean canEdit = true;
        // 判断是否修改了总成零件号
        if (StringUtils.isNotEmpty(editComponent.getComponentNo()) &&
                !editComponent.getComponentNo().equals(component.getComponentNo())) {
            // 判断总成零号是否已经使用
            canEdit = checkIfEditAndDelete(component.getComponentNo());
        }
        if (canEdit) {
            component = ConvertUtil.convert(editComponent, Component.class);
            this.updateById(component);
        } else {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMPONENT_NO_ALREADY_USED_CANNOT_EDIT, editComponent.getComponentNo());
        }
    }

    @Override
    public void deleteComponent(Long id) {
        Component component = getById(id);
        if (null == component) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMPONENT_NOT_EXIST, id);
        }
        // 判断总成零件是否已经使用
        if (checkIfEditAndDelete(component.getComponentNo())) {
            this.removeById(id);
        } else {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMPONENT_ALREADY_USED_CANNOT_DELETE, id);
        }

    }

    private Boolean checkIfEditAndDelete(String componentNo) {
        List<Long> list = componentMapper.getListByComponentNo(componentNo);
        if (!CollectionUtils.isEmpty(list)) {
            return false;
        }
        return true;
    }

    @Override
    public ComponentVo getDetail(Long id) {
        Component component = this.getById(id);
        return ConvertUtil.convert(component, ComponentVo.class);
    }

    @Override
    public List<ComponentVo> all() {
        LambdaQueryWrapper<Component> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(Component::getCreateTime);
        List<Component> list = this.list(wrapper);
        List<ComponentVo> voList = new ArrayList<>();
        if (!list.isEmpty()) {
            for (Component component : list) {
                ComponentVo componentVo = ConvertUtil.convert(component, ComponentVo.class);
                voList.add(componentVo);
            }
        }
        return voList;
    }

    @Override
    public PageResult<ComponentVo> list(SearchComponentDto search) {
        LambdaQueryWrapper<Component> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(search.getId())) {
            wrapper.like(Component::getId, search.getId());
        }
        if (StringUtils.isNotEmpty(search.getComponentNo())) {
            wrapper.like(Component::getComponentNo, search.getComponentNo());
        }
        if (StringUtils.isNotEmpty(search.getComponentName())) {
            wrapper.like(Component::getComponentName, search.getComponentName());
        }
        if (null != search.getComponentType()) {
            wrapper.eq(Component::getComponentType, search.getComponentType());
        }
        if (null != search.getUpdateStartTime()) {
            wrapper.ge(Component::getUpdateTime, search.getUpdateStartTime());
        }
        if (null != search.getUpdateEndTime()) {
            wrapper.le(Component::getUpdateTime, search.getUpdateEndTime());
        }
        wrapper.orderByDesc(Component::getCreateTime);
        Page<Component> page = componentMapper.selectPage(new Page<>(search.getPageNum(),
                search.getPageSize()), wrapper);
        PageResult<ComponentVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());

        if (res.getTotal() > CommonConstant.ZERO) {
            List<ComponentVo> list = new ArrayList<>(page.getRecords().size());
            page.getRecords().forEach(component -> {
                ComponentVo componentVo = ConvertUtil.convert(component, ComponentVo.class);
                list.add(componentVo);
            });
            res.setList(list);
        }
        return res;
    }
}
