package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.domain.dto.ota.DeviceOtaListDto;
import com.chervon.technology.domain.entity.OtaDeviceResult;
import com.chervon.technology.domain.vo.ota.DeviceOtaHistoryVo;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
public interface OtaDeviceResultService extends IService<OtaDeviceResult> {

    /**
     * 获取升级成功设备数
     * <AUTHOR>
     * @date 16:37 2022/8/17
     * @param jobId:
     * @return java.lang.Long
     **/
    Long countSucceed(String jobId);

    /**
     * 获取升级失败设备数
     * <AUTHOR>
     * @date 16:37 2022/8/17
     * @param jobId:
     * @return java.lang.Long
     **/
    Long countFailed(String jobId);

    /**
     * 分页获取设备升级记录
     *
     * @param deviceOtaListDto 获取固件升级记录Dto
     * @return 设备升级记录Vo列表
     */
    PageResult<DeviceOtaHistoryVo> getOtaRecord(DeviceOtaListDto deviceOtaListDto);

    /**
     * 获取最新设备升级记录
     * @return 设备升级记录Vo列表
     */
    OtaDeviceResult getLatestDeviceOtaRecord(String deviceId);

    /**
     * 根据任务id和设备id获取设备升级记录
     * @param jobId
     * @param deviceId
     * @return
     */
    OtaDeviceResult getOtaDeviceResultByDeviceIdWithJobId(String deviceId, Long jobId);
}
