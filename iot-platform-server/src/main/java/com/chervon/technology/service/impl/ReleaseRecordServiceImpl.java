package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.technology.api.enums.OtaJobReleaseOperation;
import com.chervon.technology.api.enums.OtaJobReleaseStatus;
import org.springframework.stereotype.Service;
import com.chervon.technology.service.ReleaseRecordService;
import com.chervon.technology.mapper.ReleaseRecordMapper;
import com.chervon.technology.domain.dataobject.ReleaseRecord;

/**
 * <AUTHOR>
 * @date 20220425
 */
@Service
public class ReleaseRecordServiceImpl extends ServiceImpl<ReleaseRecordMapper, ReleaseRecord>
        implements ReleaseRecordService {

    @Override
    public ReleaseRecord getLatestRecord(Long id, Integer releaseType, String operationType) {
        LambdaQueryWrapper<ReleaseRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReleaseRecord::getReleaseId, id).eq(ReleaseRecord::getType, releaseType).
            eq(ReleaseRecord::getReleaseOperationType, operationType).
            orderByDesc(ReleaseRecord::getCreateTime).last("limit 1");
        return this.getOne(wrapper);
    }

    @Override
    public String getRefuseReason(Long jobId, OtaJobReleaseOperation status) {
        LambdaQueryWrapper<ReleaseRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReleaseRecord::getReleaseId, jobId).
            eq(ReleaseRecord::getType, CommonConstant.ONE).
            eq(ReleaseRecord::getReleaseOperationType, status.getValue()).
            select(ReleaseRecord::getDescription).
            orderByDesc(ReleaseRecord::getCreateTime).last("limit 1");
        ReleaseRecord record = this.getOne(wrapper);
        if (record == null) {
            return StringUtils.EMPTY;
        }
        String description = record.getDescription();
        return description;
    }
}
