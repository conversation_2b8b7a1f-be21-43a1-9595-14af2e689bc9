package com.chervon.technology.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.core.utils.SpringUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisRepository;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.app.api.RemoteAppDeviceService;
import com.chervon.iot.app.api.RemoteUserSettingService;
import com.chervon.iot.app.api.vo.TargetUserVo;
import com.chervon.iot.app.api.vo.UserIdDeviceIdVo;
import com.chervon.iot.app.api.dto.AppUserDeviceDTO;
import com.chervon.iot.app.api.vo.UserSettingBo;
import com.chervon.iot.middle.api.pojo.thingmodel.Event;
import com.chervon.iot.middle.api.service.RemoteDeviceShadowService;
import com.chervon.iot.middle.api.service.RemoteThingModelService;
import com.chervon.message.api.RemoteMessageService;
import com.chervon.message.api.dto.DeviceMessageDto;
import com.chervon.message.api.enums.OsType;
import com.chervon.operation.api.RemoteMessageTemplateService;
import com.chervon.operation.api.RemoteSuggestionService;
import com.chervon.operation.api.vo.MessageTemplateBo;
import com.chervon.operation.api.vo.RemoteSuggestionVo;
import com.chervon.technology.api.enums.PushMethodEnum;
import com.chervon.technology.api.enums.PushUserTypeEnum;
import com.chervon.technology.api.enums.SwitchFlagEnum;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.toruleengine.FaultMessageAlarmLogAddDto;
import com.chervon.technology.config.Charger1600ReminderConfig;
import com.chervon.technology.config.ChargerReminderConfig;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.domain.dataobject.*;
import com.chervon.technology.domain.enums.NetworkModesEnum;
import com.chervon.technology.mapper.FaultMessageAlarmLogMapper;
import com.chervon.technology.service.*;
import com.chervon.technology.service.fault_message.MessageTemplateHandler;
import com.chervon.technology.service.fault_message.entity.FaultContextAttributes;
import com.chervon.technology.service.message_analysis.IMsgHandlerManager;
import com.chervon.technology.service.message_analysis.entity.ContextAttributes;
import com.chervon.usercenter.api.service.RemoteAppUserService;
import com.chervon.usercenter.api.vo.AppUserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @since 2022-09-14 19:57
 **/
@Service
@Slf4j
public class FaultMessageAlarmLogServiceImpl extends ServiceImpl<FaultMessageAlarmLogMapper, FaultMessageAlarmLog>
        implements FaultMessageAlarmLogService {
    @Autowired
    private ChargerReminderConfig chargerReminderConfig;
    @Autowired
    private Charger1600ReminderConfig charger1600ReminderConfig;
    @Resource
    private FaultMessageService faultMessageService;
    @Resource
    private FaultMessagePushMethodService faultMessagePushMethodService;
    @Resource
    private DeviceService deviceService;
    @Resource
    @Lazy
    private ProductService productService;
    @Resource
    private RedisRepository redisRepository;

    @DubboReference
    private RemoteMessageService remoteMessageService;
    @DubboReference
    private RemoteMessageTemplateService remoteMessageTemplateService;
    @DubboReference
    private RemoteAppDeviceService remoteAppDeviceService;
    @DubboReference
    private RemoteAppUserService remoteAppUserService;
    @DubboReference
    private RemoteUserSettingService remoteUserSettingService;
    @DubboReference
    private RemoteSuggestionService remoteSuggestionService;

    @DubboReference
    private RemoteThingModelService remoteThingModelService;

    @Resource
    private ProductNetworkModeService productNetworkModeService;

    @Resource
    private TriggerService triggerService;

    @DubboReference(timeout = 120000, retries = 0)
    private RemoteDeviceShadowService deviceShadowService;
    @Autowired
    private MessageTemplateHandler messageTemplateHandler;

    private final String DEFAULT_GROUP = "1";
    @Override
    public void add(FaultMessageAlarmLogAddDto requestDto) {
        //验证物模型id是否匹配推送
        Boolean needPush = pushCheckTriggerProperty(requestDto);
        if (!needPush) {
            log.info("FaultMessageAlarm pushCheck intercepted: faultMessageId:{} groupId:{}",requestDto.getFaultMessageId(),requestDto.getGroupId());
            return;
        }
        //获取告警消息对象
        FaultMessage faultMessage = faultMessageService.getById(requestDto.getFaultMessageId());
        if (Objects.isNull(faultMessage)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_FAULT_MESSAGE_NOT_EXIST, requestDto.getFaultMessageId());
        }
        if (!Objects.equals(faultMessage.getStatus(), SwitchFlagEnum.OPEN.getType())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_FAULT_MESSAGE_NOT_EDITABLE, faultMessage.getId());
        }
        HashMap<String,Object> faultMessageContext=new HashMap<>();
        faultMessageContext.put(FaultContextAttributes.FaultMessageAlarmLogAddDto.getCode(),requestDto);
        faultMessageContext.put(FaultContextAttributes.FaultMessage.getCode(),faultMessage);
        //获取设备基础信息
        Device device = getDeviceInfo(requestDto.getDeviceId());
        if (Objects.isNull(device)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_NOT_EXIST, device.getDeviceId());
        }
        faultMessageContext.put(FaultContextAttributes.DeviceInfo.getCode(),device);
        // 判断上报告警的设备的产品ID是否与当前告警消息的产品ID一致
        if (!Objects.equals(device.getProductId(), faultMessage.getProductId())) {
            log.warn("FaultMessageAlarm invalid -> device productId not same,device productId:{}:{}", device.getProductId(), faultMessage.getProductId());
            return;
        }
        // 发送消息时作为依据的设备ID,如果是DT联网方式的子设备(eg.电池包),则推送告警消息是推送给父设备的绑定用户
        String faultMessageDeviceId = requestDto.getDeviceId();
        // 获取设备的联网方式（即产品的联网方式）
        List<String> productNetMode = productNetworkModeService.getNetworkCodeByPid(device.getProductId());
        if (null != requestDto.getMqttBody().get("route") && productNetMode.contains(NetworkModesEnum.D_OR_T.getValue())) {
            log.debug("FaultMessageAlarm route info: {}", requestDto.getMqttBody().get("route"));
            String[] routes = requestDto.getMqttBody().get("route").toString().split("[/.]");
            // 顺序遍历route字段,找到第一个有效的deviceId时设置为messageSendingDeviceId
            for (String s : routes) {
                Device parentDevice = getDeviceInfo(s);
                if (parentDevice != null) {
                    log.info("FaultMessageAlarm route find parent device, parentDeviceId: {}", parentDevice.getDeviceId());
                    faultMessageDeviceId = parentDevice.getDeviceId();
                    faultMessage.setProductId(parentDevice.getProductId());
                    break;
                }
            }
        }
        //获取事件类型
        Event event = remoteThingModelService.getSimpleEvent(String.valueOf(device.getProductId()), requestDto.getPropertyId());
        faultMessageContext.put(FaultContextAttributes.Event.getCode(),event);
        FaultMessageAlarmLog faultAlarmLog = ConvertUtil.convert(requestDto, FaultMessageAlarmLog.class);
        // 这里默认先设置不成功,如果执行到推送,在修改为成功
        faultAlarmLog.setIsPushed(CommonConstant.ZERO);
        if (faultMessage.getPushType().equals(CommonConstant.ZERO)) {
            // 推送类型0:告警后推送
            pushDeviceMessage(faultAlarmLog, faultMessageDeviceId, faultMessageContext);
        } else if (faultMessage.getPushType().equals(CommonConstant.ONE)) {
            // 推送类型1:多少秒内触发多少次就推送第一次/最后一次消息
            String canPushMessageKey = RedisConstant.FAULT_MESSAGE_LIMIT + faultMessage.getId() + RedisConstant.SPLIT + requestDto.getDeviceId();
            if (pushLimiterAllow(canPushMessageKey, faultMessage.getCountedTriggerTime(), faultMessage.getCountedTriggerTimes())) {
                pushDeviceMessage(faultAlarmLog, faultMessageDeviceId, faultMessageContext);
            } else {
                // 不推送
                String notPushReason = MessageFormat.format("告警消息{0}触发,但还不满足{1}秒内{2}次告警的推送条件",faultMessage.getId(),faultMessage.getCountedTriggerTime(),faultMessage.getCountedTriggerTimes());
                log.info("FaultMessageAlarm -> " + notPushReason);
                faultAlarmLog.setReason(notPushReason);
            }
        }
        this.save(faultAlarmLog);
    }

    private boolean pushLimiterAllow(String key,Integer timespan,Integer timesNumber){
        MessagePushAllow pushCache = RedisUtils.getCacheObject(key);
        if(Objects.isNull(pushCache)){
            pushCache=new MessagePushAllow(System.currentTimeMillis(),1);
            RedisUtils.setWithExpire(key,pushCache,timespan);
            return true;
        }
        Integer count=pushCache.getNum()+1;
        Long diffSecond = (System.currentTimeMillis()-pushCache.getTs())/1000;
        if(count<=timesNumber && diffSecond < timespan){
            pushCache.setNum(count);
            RedisUtils.setWithExpire(key,pushCache,timespan-diffSecond);
            return true;
        }
        if(count>timesNumber && diffSecond >= timespan){
            RedisUtils.deleteObject(key);
        }else{
            pushCache.setNum(count);
            RedisUtils.setWithExpire(key,pushCache,timespan-diffSecond);
        }
        return false;
    }

    /**
     * 检查是否需要推送
     *
     * @param requestDto
     * @return
     */
    private Boolean pushCheckTriggerProperty(FaultMessageAlarmLogAddDto requestDto) {
        if(Objects.isNull(requestDto.getGroupId()) || requestDto.getGroupId().equals("1")){
            return true;
        }
        String groupId=requestDto.getGroupId();
        Map<String, Object> mqttBody=requestDto.getMqttBody();
        String propertyId=requestDto.getPropertyId();
        String parameter=requestDto.getParameter();
        JSONObject body = new JSONObject(mqttBody);
        if (StringUtils.isEmpty(parameter)) {
                return true;
        }
        JSONObject propBody = body.getJSONObject(requestDto.getPropertyId());
        final List<String> listPropertyIndex = propBody.keySet().stream().sorted().collect(Collectors.toList());
        if(!listPropertyIndex.get(0).equals(requestDto.getParameter())){
            return false;
        }
        //根据分组和propertyId查询规则列表
        List<RuleTrigger> triggers = triggerService.listByGroupId(groupId,propertyId,"","");
        if(CollectionUtils.isEmpty(triggers)){
            return false;
        }
        if(triggers.size()==1 || triggers.size()>10){
            return true;
        }
        for (RuleTrigger trigger : triggers) {
            JSONObject propertyBody = body.getJSONObject(trigger.getPropertyId());
            if(Objects.isNull(propertyBody)){
                return false;
            }
            Object parameterObject = propertyBody.get(trigger.getParameter());
            if (parameterObject != null && !triggered(trigger, parameterObject)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 从上报的mqtt payload中获取属性或者参数的值
     * @param mqttBody
     * @param propertyId
     * @param parameter
     * @return
     */
    private Object getValueFromMqttBody (Map<String, Object> mqttBody, String propertyId, String parameter) {
        JSONObject mqttJson = new JSONObject(mqttBody);
        Object propertyObject = mqttJson.get(propertyId);
        if (StringUtils.isEmpty(parameter)) {
            return propertyObject;
        } else {
            JSONObject propertyJson = new JSONObject(propertyObject);
            return propertyJson.get(parameter);
        }
    }

    /**
     * 判断属性或参数值是否符合触发器条件
     * @param trigger
     * @param propertyObject
     * @return
     */
    private Boolean triggered (RuleTrigger trigger, Object propertyObject){
        switch (trigger.getRuleCondition()) {
            case EQUALS: {
                if (propertyObject.toString().equals(trigger.getValue())) {
                    return true;
                }
                break;
            }
            case INCLUDE: {
                if (trigger.getValue().contains(propertyObject.toString())) {
                    return true;
                }
                break;
            }
            case GRATER_THAN: {
                try {
                    if (Long.valueOf(propertyObject.toString()) > Long.valueOf(trigger.getValue())) {
                        return true;
                    }
                } catch (Exception exception) {
                    log.error("数据转换错误，无法进行比较，propertyObject：{}，exception：{}", propertyObject, exception);
                }
                break;
            }
            case LESS_THAN: {
                try {
                    if (Long.valueOf(propertyObject.toString()) < Long.valueOf(trigger.getValue())) {
                        return true;
                    }
                } catch (Exception exception) {
                    log.error("数据转换错误，无法进行比较，propertyObject：{}，exception：{}", propertyObject, exception);
                }
                break;
            }
            default:
                break;
        }
        return false;
    }

    /**
     * 根据DeviceId获取并检测productId
     * @param deviceId 设备ID
     * @return device
     */
    private Device getDeviceInfo(String deviceId) {
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<Device>()
                .eq(Device::getDeviceId, deviceId)
                .select(Device::getProductId, Device::getDeviceId, Device::getNickName, Device::getDeviceName)
                .last("limit 1");
        return deviceService.getOne(queryWrapper);
    }

    /**
     * 告警消息确定要推送消息,检查
     *
     * @param alarmLog   告警消息记录
     * @param alarmDeviceId 推送消息依据的设备ID(如果涉及父子设备,这里是父设备ID)
     * @param context
     */
    private void pushDeviceMessage(FaultMessageAlarmLog alarmLog, String alarmDeviceId, HashMap<String,Object> context) {
        FaultMessage faultMessage=(FaultMessage)context.get(FaultContextAttributes.FaultMessage.getCode());
        // 获取绑定的所有用户Id
        Set<Long> userIds = new HashSet<>();
        Map<Long,AppUserDeviceDTO> appUserDeviceDTOMap=new HashMap<>();
        if (faultMessage.getTargetUser().contains(PushUserTypeEnum.EGO_LOGIN_USER.getStrType())) {
            // 推送目标用户类型：1 登录用户
            //获取用户和设备绑定关系数据
            List<AppUserDeviceDTO>  appUserDeviceDTOList = remoteAppDeviceService.getAppUserDeviceInfoByDeviceId(alarmDeviceId,null);
            //用户-用户和设备绑定关系数据
            appUserDeviceDTOMap=appUserDeviceDTOList.stream().collect(Collectors.toMap(AppUserDeviceDTO::getUserId,AppUserDeviceDTO->AppUserDeviceDTO,(k1,k2)->k1));
            userIds.addAll(appUserDeviceDTOList.stream().map(x->x.getUserId()).collect(Collectors.toList()));
        }
        if (faultMessage.getTargetUser().contains(PushUserTypeEnum.APP_CUSTOM_USER.getStrType())) {
            List<TargetUserVo> deviceAlarmUsers = remoteAppDeviceService.getDeviceAlarmUsers(alarmDeviceId);
            if (!CollectionUtils.isEmpty(deviceAlarmUsers)) {
                userIds.addAll(deviceAlarmUsers.stream().map(x -> x.getUserId()).collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isEmpty(userIds)){
            return;
        }
        FaultMessageAlarmLogAddDto requestDto=(FaultMessageAlarmLogAddDto)context.get(FaultContextAttributes.FaultMessageAlarmLogAddDto.getCode());
//     //判断同一个groupId下有无其他触发器 fault_message_trigger->rule_trigger
//        userIds = filterUserIds(userIds, requestDto);
//        if (CollectionUtils.isEmpty(userIds)) {
//            // 没有要推送的用户，直接返回
//            return;
//        }
        List<Long> listUserId = new ArrayList<>(userIds);
        Map<String, AppUserVo> appUserVoMap = remoteAppUserService.listAppUserMap(listUserId);
        if(CollectionUtils.isEmpty(appUserVoMap)){
            return;
        }
        context.put(FaultContextAttributes.AppBindUser.getCode(), appUserVoMap);
        // 获取推送消息方式
        List<Integer> pushTypes = getPushTypes(faultMessage);
        //构建app自定义消息列表
        List<DeviceMessageDto> messageList = buildAppCustomeMessageList(faultMessage, alarmDeviceId, context, pushTypes);

        Map<Long, UserSettingBo> userSettingBoMap = remoteUserSettingService.listUserSettingBoMap(listUserId);
        // 用字符串info记录告警失败原因
        StringBuilder failureReason = new StringBuilder();
        Event event=(Event)context.get(FaultContextAttributes.Event.getCode());
        for (Long userId : userIds) {
            DeviceMessageDto deviceMessageDto = new DeviceMessageDto();
            deviceMessageDto.setUserId(userId.toString());
            deviceMessageDto.setSystemMessageId(faultMessage.getId().toString());
            deviceMessageDto.setDeviceId(alarmDeviceId);
            deviceMessageDto.setDeviceNickName(Optional.ofNullable(appUserDeviceDTOMap.get(userId)).orElse(AppUserDeviceDTO.builder().build()).getDeviceNickName());
            deviceMessageDto.setProductId(faultMessage.getProductId().toString());
            Map<String, String> payLoadHashMap = new HashMap<>(CommonConstant.THREE);
            payLoadHashMap.put("data",JsonUtils.toJson(requestDto.getMqttBody()));
            if (StringUtils.isNotBlank(requestDto.getValue())) {
                payLoadHashMap.put("value", requestDto.getValue());
            }
            if (Objects.nonNull(event)) {
                payLoadHashMap.put("eventType", event.getType());
            }
            deviceMessageDto.setPayloadData(payLoadHashMap);
            deviceMessageDto.setPushTypes(pushTypes);
            try {
                // 推送消息设备类型(user-center user表中获取)
                AppUserVo appUserVo = appUserVoMap.get(userId.toString());
                if (null == appUserVo) {
                    failureReason.append("appUserVo(").append(userId).append(")不存在,自动跳过;");
                    log.warn("FaultMessageAlarm -> faultMessageId({}),appUserVo({})不存在,自动跳过", faultMessage.getId(), userId);
                    continue;
                }
                deviceMessageDto.setDeviceType(OsType.valueOf(appUserVo.getAppTypeCode().toUpperCase()));
                deviceMessageDto.setEmail(appUserVo.getEmail());
                if (pushTypes.contains(PushMethodEnum.MESSAGE_REMOVE.getPushTypes())) {
                    // 如果为消息清除，则需要填充content
                    deviceMessageDto.setTitle(PushMethodEnum.MESSAGE_REMOVE.getValue());
                    deviceMessageDto.setContent(PushMethodEnum.MESSAGE_REMOVE.getValue()+":"+requestDto.getPropertyId());
                    deviceMessageDto.setPushTypes(pushTypes);
                    messageList.add(deviceMessageDto);
                    continue;
                }
                // 推送消息用户Token(iot-app user_setting表中的push_token字段)
                UserSettingBo userSettingBo = userSettingBoMap.get(userId);
                if (userSettingBo == null) {
                    failureReason.append("userSetting(").append(userId).append(")为空,自动跳过;");
                    log.warn("FaultMessageAlarm -> faultMessageId({}),userSetting为空({}),自动跳过", faultMessage.getId(), userId);
                    continue;
                } else if (pushTypes.contains(CommonConstant.ZERO) && StringUtils.isEmpty(userSettingBo.getPushToken())) {
                    log.warn("FaultMessageAlarm -> faultMessageId({}),推送方式包含墓碑但userSetting({})的push_token为空", faultMessage.getId(), userId);
                    failureReason.append("userSetting(").append(userId).append(")pushToken字段为空,自动跳过墓碑;");
                    List<Integer> newPushTypes =  new ArrayList<>(pushTypes);
                    newPushTypes.remove(CommonConstant.ZERO);
                    deviceMessageDto.setPushTypes(newPushTypes);
                    // pushToken为空，不应该直接跳过整条消息的推送，只跳过墓碑即可
                    // continue;
                } else {
                    deviceMessageDto.setToken(userSettingBo.getPushToken());
                }
                // 推送消息开关(iot-app user_setting表中的device_message_switch字段)
                deviceMessageDto.setPushSwitch(userSettingBo.getDeviceMessageSwitch());
                // 推送消息标题、内容(多语言语种从user_setting表中language字段获取)
                MessageTemplateBo messageTemplateBo = messageTemplateHandler.getPushMessageTemplate(context,userSettingBo.getLanguage(), faultMessage.getMessageTemplateId(),userId);
//                MessageTemplateBo messageTemplateBo = remoteMessageTemplateService.get(userSettingBo.getLanguage(), faultMessage.getMessageTemplateId());
                if (Objects.isNull(messageTemplateBo) || StringUtils.isEmpty(messageTemplateBo.getTitle().getMessage())) {
                    failureReason.append("userSetting(").append(userId).append(",").append(userSettingBo.getLanguage()).append(")告警标题为空,自动跳过.");
                    log.warn("FaultMessageAlarm -> faultMessageId({}),userSetting({},{})告警标题为空,自动跳过", faultMessage.getId(), userId, userSettingBo.getLanguage());
                    continue;
                } else {
                    deviceMessageDto.setTitle(messageTemplateBo.getTitle().getMessage());
                }
                if (StringUtils.isEmpty(messageTemplateBo.getContent().getMessage())) {
                    failureReason.append("userSetting(").append(userId).append(",").append(userSettingBo.getLanguage()).append(")告警内容为空,自动跳过.");
                    log.warn("FaultMessageAlarm -> faultMessageId({}),userSetting({},{})告警内容为空,自动跳过", faultMessage.getId(), userId, userSettingBo.getLanguage());
                    continue;
                } else {
                    deviceMessageDto.setContent(messageTemplateBo.getContent().getMessage());
                }
                // 添加处理建议到消息中
                if (null != faultMessage.getSuggestionId()) {
                    RemoteSuggestionVo suggestionVo = remoteSuggestionService.detail(faultMessage.getSuggestionId(),
                            userSettingBo.getLanguage());
                    payLoadHashMap.put("suggestionContent", suggestionVo.getContent());
                    payLoadHashMap.put("suggestionExtra", suggestionVo.getExtra());
                    deviceMessageDto.setPayloadData(payLoadHashMap);
                }
                log.info("appUserVo: {}", appUserVo);
                messageList.add(deviceMessageDto);
            } catch (Exception e) {
                failureReason.append("user(").append(userId).append(")发生异常;");
                log.error(String.format(TechnologyErrorCode.TECHNOLOGY_COMMON_PARAM_ERROR2.getErrorMessage(), userId, e.getMessage()), e);
            }
        }
        //设备短信推送：判断是否推送device表中电话号码的短信推送
        buildDeviceSmsMessage(faultMessage, alarmDeviceId, context, pushTypes, messageList);
        // 判断deviceMessageDtoList是否为空,决定是否调用消息中心推送设备消息
        if (CollectionUtils.isEmpty(messageList)) {
            String noPush = "没有符合条件的推送:";
            log.info("FaultMessageAlarm -> faultMessageId({})没有符合条件的推送", faultMessage.getId());
            alarmLog.setReason(noPush + failureReason);
        } else {
            alarmLog.setPushTypes(pushTypes.stream().map(a->a.toString()).collect(Collectors.joining(",")));
            alarmLog.setPushUsers(userIds.stream().map(a->a.toString()).collect(Collectors.joining(",")));
            if (StringUtils.isNotEmpty(failureReason)) {
                alarmLog.setReason(failureReason.toString());
            }
            alarmLog.setIsPushed(CommonConstant.ONE);
            log.info("FaultMessageAlarm -> 调用消息中心推送设备消息deviceMessageDtoList.size: {}", messageList.size());
            remoteMessageService.pushDeviceMessageAsync(messageList);
            //消息推送的业务处理
            processMessageBusiness(requestDto,faultMessage,event,messageList);
        }
    }

    private void buildDeviceSmsMessage(FaultMessage faultMessage, String alarmDeviceId,HashMap<String,Object> context, List<Integer> pushTypes, List<DeviceMessageDto> messageList) {
        // 如果推送方式包含短信则仅推送一次给设备选定手机号
        if (pushTypes.contains(PushMethodEnum.MESSAGE.getPushTypes()) && faultMessage.getTargetUser().contains(PushUserTypeEnum.EGO_LOGIN_USER.getStrType())) {
            // 如果推送方式包括手机
            Device device = deviceService.getOne(new LambdaQueryWrapper<Device>()
                    .eq(Device::getDeviceId, alarmDeviceId).select(Device::getPhoneNum));
            if (null != device && StringUtils.isNotBlank(device.getPhoneNum())) {
                TargetUserVo targetUserVo=new TargetUserVo();
                targetUserVo.setTelNumber(device.getPhoneNum());
                targetUserVo.setUserId(0L);
                targetUserVo.setLanguage("en");
                final DeviceMessageDto deviceMessageDto = buildDeviceAlarmMessage(faultMessage, alarmDeviceId, pushTypes, targetUserVo, context);
                if(!Objects.isNull(deviceMessageDto)){
                    messageList.add(deviceMessageDto);
                }
            }
        }
    }

    @NotNull
    private List<Integer> getPushTypes(FaultMessage faultMessage) {
        LambdaQueryWrapper<FaultMessagePushMethod> pushMethodWrapper = new LambdaQueryWrapper<FaultMessagePushMethod>()
                .eq(FaultMessagePushMethod::getFaultMessageId, faultMessage.getId());
        Set<PushMethodEnum> pushMethodEnumList = faultMessagePushMethodService.list(pushMethodWrapper)
                .stream().map(FaultMessagePushMethod::getPushMethod).collect(Collectors.toSet());
        List<Integer> pushTypes = pushMethodEnumList.stream().map(PushMethodEnum::getPushTypes).collect(Collectors.toList());
        if (pushTypes.contains(PushMethodEnum.MESSAGE_REMOVE.getPushTypes())) {
            // 如果为消息清除，则忽略其他推送方式
            pushTypes = Arrays.asList(PushMethodEnum.MESSAGE_REMOVE.getPushTypes());
        }
        return pushTypes;
    }

    private List<DeviceMessageDto> buildAppCustomeMessageList(FaultMessage faultMessage, String alarmDeviceId, HashMap<String,Object> context, List<Integer> pushTypes) {
        List<DeviceMessageDto> messageList = new ArrayList<>();
        if (!faultMessage.getTargetUser().contains(PushUserTypeEnum.APP_CUSTOM_USER.getStrType())) {
            return messageList;
        }
        FaultMessageAlarmLogAddDto requestDto=(FaultMessageAlarmLogAddDto)context.get(FaultContextAttributes.FaultMessageAlarmLogAddDto.getCode());
        // 推送目标用户类型：2 app自定义用户(设备报警用户)
        List<TargetUserVo> deviceAlarmUsers = remoteAppDeviceService.getDeviceAlarmUsers(requestDto.getDeviceId());
        if (CollectionUtils.isEmpty(deviceAlarmUsers)) {
            return messageList;
        }
        Map<String, AppUserVo> appUserVoMap = (Map<String, AppUserVo>)context.get(FaultContextAttributes.AppBindUser.getCode());
        for (TargetUserVo deviceAlarmUserVo : deviceAlarmUsers) {
            if(!appUserVoMap.containsKey(deviceAlarmUserVo.getUserId().toString())){
                continue;
            }
            if (pushTypes.contains(PushMethodEnum.PHONE_VOICE.getPushTypes()) && deviceAlarmUserVo.getNotificationByCall().equals(SwitchFlagEnum.OPEN.getType())) {
                DeviceMessageDto voiceMessage = buildDeviceAlarmMessage(faultMessage, alarmDeviceId, pushTypes, deviceAlarmUserVo, context);
                if(Objects.isNull(voiceMessage)){
                    continue;
                }
                voiceMessage.setPushTypes(Arrays.asList(PushMethodEnum.PHONE_VOICE.getPushTypes()));
                voiceMessage.setTargetUserPhone(deviceAlarmUserVo.getTelNumber());
                voiceMessage.setPhoneSwitch(SwitchFlagEnum.OPEN.getType());
                messageList.add(voiceMessage);
            }
            if (pushTypes.contains(PushMethodEnum.MESSAGE.getPushTypes()) && deviceAlarmUserVo.getNotificationByMsg().equals(SwitchFlagEnum.OPEN.getType())) {
                DeviceMessageDto snsMessage = buildDeviceAlarmMessage(faultMessage, alarmDeviceId, pushTypes, deviceAlarmUserVo, context);
                if(Objects.isNull(snsMessage)){
                    continue;
                }
                snsMessage.setPushTypes(Arrays.asList(PushMethodEnum.MESSAGE.getPushTypes()));
                snsMessage.setTargetUserPhone(deviceAlarmUserVo.getTelNumber());
                snsMessage.setSnsSwitch(SwitchFlagEnum.OPEN.getType());
                messageList.add(snsMessage);
            }
        }
        return messageList;
    }

    private DeviceMessageDto buildDeviceAlarmMessage(FaultMessage faultMessage,
                                                     String alarmDeviceId,
                                                     List<Integer> pushTypes,TargetUserVo targetUserVo,
                                                     HashMap<String,Object> context) {
        DeviceMessageDto deviceMessageDto = new DeviceMessageDto();
        deviceMessageDto.setUserId(targetUserVo.getUserId().toString());
        deviceMessageDto.setSystemMessageId(faultMessage.getId().toString());
        deviceMessageDto.setDeviceId(alarmDeviceId);
        deviceMessageDto.setProductId(faultMessage.getProductId().toString());
        deviceMessageDto.setPushTypes(pushTypes);
        deviceMessageDto.setDeviceType(OsType.ALL);
        Map<String, String> payLoadHashMap = new HashMap<>(CommonConstant.THREE);
        FaultMessageAlarmLogAddDto requestDto=(FaultMessageAlarmLogAddDto)context.get(FaultContextAttributes.FaultMessageAlarmLogAddDto.getCode());
        payLoadHashMap.put("data",JsonUtils.toJson(requestDto.getMqttBody()));
        if (StringUtils.isNotBlank(requestDto.getValue())) {
            payLoadHashMap.put("value", requestDto.getValue());
        }
        payLoadHashMap.put("language",targetUserVo.getLanguage());
        deviceMessageDto.setPayloadData(payLoadHashMap);
        MessageTemplateBo messageTemplateBo = messageTemplateHandler.getPushMessageTemplate(context,"en", faultMessage.getMessageTemplateId(),targetUserVo.getUserId());
        deviceMessageDto.setContent(messageTemplateBo.getContent().getMessage());
        if (StringUtils.isNotBlank(messageTemplateBo.getContent().getMessage())) {
            deviceMessageDto.setTitle(messageTemplateBo.getTitle().getMessage());
            deviceMessageDto.setPhone(targetUserVo.getTelNumber());
            return deviceMessageDto;
        }
        return null;
    }

//    /**
//     * 二次过滤需要推送的用户id
//     *
//     * @param userIds
//     * @param requestDto
//     * @return
//     */
//    private Set<Long> filterUserIds(Set<Long> userIds,FaultMessageAlarmLogAddDto requestDto) {
//        String groupId=requestDto.getGroupId();
//        Map<String, Object> mqttBody=requestDto.getMqttBody();
//        if (StringUtils.isEmpty(groupId) || groupId.equals(DEFAULT_GROUP)) {
//            // 兼容老版本触发器，header中没有groupId，不做过滤
//            return userIds;
//        }
//        // 根据消息id获取所有的触发器
//        List<RuleTrigger> triggers = triggerService.listByGroupId(groupId);
//        if (triggers.size() == 1) {
//            // 只有一个触发器，不需要过滤
//            return userIds;
//        }
//        // 获取用户绑定的设备列表
//        List<UserIdDeviceIdVo> userIdDeviceIdVos = remoteAppDeviceService.listDeviceIdByUserIds(new ArrayList<>(userIds));
//        // 用户绑定的设备列表转换为map，key:userId, value:Map<productId, deviceId>
//        Map<Long, Map<Long, List<String>>> deviceMap = getDeviceMap(userIdDeviceIdVos);
//        Set<Long> filteredUserId = new HashSet<>();
//        for (Long userId : userIds) {
////            // 判断用户绑定的设备是否满足所有触发器
////            if (deviceMap.get(userId).size() < triggers.size()) {
////                continue;
////            }
//            // 满足的触发器个数
//            int triggeredCount = 0;
//            for(RuleTrigger trigger : triggers){
//                // 获取用户绑定的当前trigger下产品的所有设备列表
//                List<String> deviceIds = deviceMap.get(userId).get(trigger.getProductId());
//                if (CollectionUtils.isEmpty(deviceIds)) {
//                    break;
//                }
//                for (String deviceId : deviceIds) {
//                    // 判断有无满足触发器的设备
//                    String identifier = trigger.getPropertyId();
//                    // 优先从payload中获取上报的属性或者事件参数值，如果多次上报，本次payload获取不到则从影子里获取
//                    Object shadowItem = getValueFromMqttBody(mqttBody, identifier, trigger.getParameter());
//                    if (shadowItem == null) {
//                        if (StringUtils.isNotEmpty(trigger.getParameter())) {
//                            shadowItem = deviceShadowService.getShadowItem(deviceId, identifier, trigger.getParameter());
//                        } else {
//                            shadowItem = deviceShadowService.getShadowItem(deviceId, identifier);
//                        }
//                    }
//                    log.info("deviceId: {}, identifier:{}, parameter: {}, shadowItem:{}",
//                            deviceId, identifier, trigger.getParameter(), shadowItem);
//                    if (shadowItem == null) {
//                        continue;
//                    }
//                    boolean triggered = false;
//                    switch (trigger.getRuleCondition()) {
//                        case EQUALS: {
//                            if (shadowItem.toString().equals(trigger.getValue())) {
//                                triggered = true;
//                            }
//                            break;
//                        }
//                        case INCLUDE: {
//                            if (trigger.getValue().contains(shadowItem.toString())) {
//                                triggered = true;
//                            }
//                            break;
//                        }
//                        case GRATER_THAN: {
//                            try {
//                                if (Long.valueOf(shadowItem.toString()) > Long.valueOf(trigger.getValue())) {
//                                    triggered = true;
//                                }
//                            } catch (Exception exception) {
//                                log.error("数据转换错误，无法进行比较，shadowItem：{}，exception：{}", shadowItem, exception);
//                            }
//                            break;
//                        }
//                        case LESS_THAN: {
//                            try {
//                                if (Long.valueOf(shadowItem.toString()) < Long.valueOf(trigger.getValue())) {
//                                    triggered = true;
//                                }
//                            } catch (Exception exception) {
//                                log.error("数据转换错误，无法进行比较，shadowItem：{}，exception：{}", shadowItem, exception);
//                            }
//                            break;
//                        }
//                        default:
//                            break;
//                    }
//                    if (triggered) {
//                        triggeredCount++;
//                        break;
//                    }
//                }
//            }
//            if (triggeredCount != triggers.size()) {
//                // 不是所有的触发器都满足触发
//                continue;
//            }
//            filteredUserId.add(userId);
//        }
//        return filteredUserId;
//    }

    private Map<Long, Map<Long, List<String>>> getDeviceMap(List<UserIdDeviceIdVo> userIdDeviceIdVos) {
        Map<Long, List<UserIdDeviceIdVo>> userMap = userIdDeviceIdVos.stream()
                .collect(groupingBy(UserIdDeviceIdVo::getUserId));
        Map<Long, Map<Long, List<String>>> deviceMap = new HashMap<>();
        for (Long userId: userMap.keySet()) {
            Map<Long, List<String>> productMap = new HashMap<>();
            for (UserIdDeviceIdVo userIdDeviceIdVo : userMap.get(userId)) {
                Long productId = deviceService.getProductId(userIdDeviceIdVo.getDeviceId());
                if (productMap.containsKey(productId)) {
                    productMap.get(productId).add(userIdDeviceIdVo.getDeviceId());
                } else {
                    List<String> deviceIds = new ArrayList<>();
                    deviceIds.add(userIdDeviceIdVo.getDeviceId());
                    productMap.put(productId, deviceIds);
                }
            }
            deviceMap.put(userId, productMap);
        }
        return deviceMap;
    }


    @Override
    public void chargerReminder(FaultMessageAlarmLogAddDto dto) {
        Device device = getDeviceInfo(dto.getDeviceId());
        if (Objects.isNull(device)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_NOT_EXIST, device.getDeviceId());
        }
        Product product = productService.getOne(new LambdaQueryWrapper<Product>()
                .eq(Product::getProductSnCode, chargerReminderConfig.getProductSnCode())
                .select(Product::getId).last("limit 1"));
        if (null == product) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_NOT_EXIST, chargerReminderConfig.getProductSnCode());
        }
        // 判断上报告警的设备的产品ID是否与当前告警消息的产品ID一致
        if (!Objects.equals(device.getProductId(), product.getId())) {
            log.warn("FaultMessageAlarmLogServiceImpl#chargerReminder -> 触发告警的设备productId({})与当前告警消息productId({})不同,告警无效", device.getProductId(), product.getId());
            return;
        }

        String key = RedisConstant.FAULT_MESSAGE_LIMIT + "charger:reminder" + RedisConstant.SPLIT + dto.getDeviceId() + RedisConstant.SPLIT + dto.getTargetRate();
        String rateKey = RedisConstant.FAULT_MESSAGE_LIMIT + "charger:reminder" + RedisConstant.SPLIT + dto.getDeviceId() + ":rate";
        Object cache = RedisUtils.getCacheObject(key);
        if (cache == null) {
            List<DeviceMessageDto> deviceMessageDtoList = new ArrayList<>();

            //获取用户和设备绑定关系数据
            List<AppUserDeviceDTO>  appUserDeviceDTOList =remoteAppDeviceService.getAppUserDeviceInfoByDeviceId(dto.getDeviceId(),null);
            //用户-用户和设备绑定关系数据
            Map<Long,AppUserDeviceDTO> appUserDeviceDTOMap=appUserDeviceDTOList.stream().collect(Collectors.toMap(AppUserDeviceDTO::getUserId,AppUserDeviceDTO->AppUserDeviceDTO,(k1,k2)->k1));
            // 获取绑定的所有用户Id
            List<Long> userIds = appUserDeviceDTOList.stream().map(x->x.getUserId()).collect(Collectors.toList());
            // 用字符串info记录告警失败原因
            Map<String, AppUserVo> appUserVoMap = remoteAppUserService.listAppUserMap(new ArrayList<>(userIds));
            Map<Long, UserSettingBo> userSettingBoMap = remoteUserSettingService.listUserSettingBoMap(new ArrayList<>(userIds));
            // 推送消息方式
            List<Integer> pushTypes = chargerReminderConfig.getPushMethods();
            for (Long userId : userIds) {
                DeviceMessageDto deviceMessageDto = new DeviceMessageDto();
                deviceMessageDto.setUserId(userId.toString());
                deviceMessageDto.setSystemMessageId("charger_reminder");
                deviceMessageDto.setDeviceId(dto.getDeviceId());
                deviceMessageDto.setDeviceNickName(Optional.ofNullable(appUserDeviceDTOMap.get(userId)).orElse(AppUserDeviceDTO.builder().build()).getDeviceNickName());
                deviceMessageDto.setProductId(product.getId().toString());
                deviceMessageDto.setPushTypes(pushTypes);
                try {
                    // 推送消息设备类型(user-center user表中获取)
                    AppUserVo appUserVo = appUserVoMap.get(userId.toString());
                    if (null == appUserVo) {
                        log.warn("FaultMessageAlarmLogServiceImpl#chargerReminder -> appUserVo({})不存在,自动跳过", userId);
                        continue;
                    }
                    deviceMessageDto.setDeviceType(OsType.valueOf(appUserVo.getAppTypeCode().toUpperCase()));
                    deviceMessageDto.setEmail(appUserVo.getEmail());
                    // 推送消息用户Token(iot-app user_setting表中的push_token字段)
                    UserSettingBo userSettingBo = userSettingBoMap.get(userId);
                    if (userSettingBo == null) {
                        log.warn("FaultMessageAlarmLogServiceImpl#chargerReminder -> userSetting为空({}),自动跳过", userId);
                        continue;
                    } else if (pushTypes.contains(CommonConstant.ZERO) && StringUtils.isEmpty(userSettingBo.getPushToken())) {
                        log.warn("FaultMessageAlarmLogServiceImpl#chargerReminder -> 推送方式包含墓碑但userSetting({})的push_token为空", userId);
                        continue;
                    } else {
                        deviceMessageDto.setToken(userSettingBo.getPushToken());
                    }
                    // 推送消息开关(iot-app user_setting表中的device_message_switch字段)
                    deviceMessageDto.setPushSwitch(userSettingBo.getDeviceMessageSwitch());
                    // 推送消息标题、内容(多语言语种从user_setting表中language字段获取)
                    MessageTemplateBo messageTemplateBo = remoteMessageTemplateService.get(userSettingBo.getLanguage(),
                            chargerReminderConfig.getMessageTemplateId());
                    if (StringUtils.isEmpty(messageTemplateBo.getTitle().getMessage())) {
                        log.warn("FaultMessageAlarmLogServiceImpl#chargerReminder -> userSetting({},{})告警标题为空,自动跳过", userId, userSettingBo.getLanguage());
                        continue;
                    } else {
                        deviceMessageDto.setTitle(messageTemplateBo.getTitle().getMessage());
                    }
                    if (StringUtils.isEmpty(messageTemplateBo.getContent().getMessage())) {
                        log.warn("FaultMessageAlarmLogServiceImpl#chargerReminder -> userSetting({},{})告警内容为空,自动跳过", userId, userSettingBo.getLanguage());
                        continue;
                    } else {
                        deviceMessageDto.setContent(messageTemplateBo.getContent().getMessage());
                    }
                    log.info("appUserVo: {}", appUserVo);
                    deviceMessageDtoList.add(deviceMessageDto);
                } catch (Exception e) {
                    log.error(String.format(TechnologyErrorCode.TECHNOLOGY_COMMON_PARAM_ERROR2.getErrorMessage(), userId, e.getMessage()), e);
                }
                if (!CollectionUtils.isEmpty(deviceMessageDtoList)) {
                    // 推送设备消息
                    remoteMessageService.pushDeviceMessageAsync(deviceMessageDtoList);

                    String cacheRate = RedisUtils.getCacheObject(rateKey);
                    if (cacheRate != null) {
                        RedisUtils.deleteObject(cacheRate);
                    }
                    RedisUtils.setCacheObject(rateKey, key);

                    RedisUtils.setCacheObject(key, 1);
                    RedisUtils.expire(key, Duration.ofSeconds(chargerReminderConfig.getTriggerDuration()));
                }
            }
        }
    }

    @Override
    public void charger1600Reminder(FaultMessageAlarmLogAddDto dto) {
        //查询设备
        Device device = deviceService.getOne(
                Wrappers.<Device>lambdaQuery()
                .eq(Device::getDeviceId, dto.getDeviceId())
                .select(Device::getProductId, Device::getDeviceId)
        );
        if (Objects.isNull(device)) {
            log.error("FaultMessageAlarmLogServiceImpl#charger1600Reminder -> 要告警的设备不存在:deviceId: {}", dto.getDeviceId());
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_NOT_EXIST);
        }
        Product product = productService.getOne(
                Wrappers.<Product>lambdaQuery()
                .eq(Product::getCommodityModel, charger1600ReminderConfig.getCommodityModel())
                .select(Product::getId).last("limit 1"));
        if (Objects.isNull(product)) {
            log.error("FaultMessageAlarmLogServiceImpl#charger1600Reminder -> 配置文件中设置的产品Model不存在: {}", charger1600ReminderConfig.getCommodityModel());
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_NOT_EXIST);
        }
        // 判断上报告警的设备的产品ID是否与当前告警消息的产品ID一致
        if (!Objects.equals(device.getProductId(), product.getId())) {
            log.info("FaultMessageAlarmLogServiceImpl#charger1600Reminder -> 触发告警的设备productId({})与当前告警消息productId({})不同,告警无效", device.getProductId(), product.getId());
            return;
        }

        String key = RedisConstant.FAULT_MESSAGE_LIMIT + "charger1600:reminder" + RedisConstant.SPLIT + dto.getDeviceId() + RedisConstant.SPLIT + dto.getTargetRate();
        String rateKey = RedisConstant.FAULT_MESSAGE_LIMIT + "charger1600:reminder" + RedisConstant.SPLIT + dto.getDeviceId() + ":rate";
        Object cache = RedisUtils.getCacheObject(key);
        if (cache == null) {
            List<DeviceMessageDto> deviceMessageDtoList = new ArrayList<>();
            //获取用户和设备绑定关系数据
            List<AppUserDeviceDTO>  appUserDeviceDTOList =remoteAppDeviceService.getAppUserDeviceInfoByDeviceId(dto.getDeviceId(),null);
            //用户-用户和设备绑定关系数据
            Map<Long,AppUserDeviceDTO> appUserDeviceDTOMap=appUserDeviceDTOList.stream().collect(Collectors.toMap(AppUserDeviceDTO::getUserId,AppUserDeviceDTO->AppUserDeviceDTO,(k1,k2)->k1));
            // 获取绑定的所有用户Id
            List<Long> userIds = appUserDeviceDTOList.stream().map(x->x.getUserId()).collect(Collectors.toList());
            // 用字符串info记录告警失败原因
            Map<String, AppUserVo> appUserVoMap = remoteAppUserService.listAppUserMap(new ArrayList<>(userIds));
            Map<Long, UserSettingBo> userSettingBoMap = remoteUserSettingService.listUserSettingBoMap(new ArrayList<>(userIds));
            // 推送消息方式
            List<Integer> pushTypes = charger1600ReminderConfig.getPushMethods();
            for (Long userId : userIds) {
                DeviceMessageDto deviceMessageDto = new DeviceMessageDto();
                deviceMessageDto.setUserId(userId.toString());
                deviceMessageDto.setDeviceId(dto.getDeviceId());
                deviceMessageDto.setDeviceNickName(Optional.ofNullable(appUserDeviceDTOMap.get(userId)).orElse(AppUserDeviceDTO.builder().build()).getDeviceNickName());
                deviceMessageDto.setProductId(product.getId().toString());
                deviceMessageDto.setPushTypes(pushTypes);
                try {
                    // 推送消息设备类型(user-center user表中获取)
                    AppUserVo appUserVo = appUserVoMap.get(userId.toString());
                    if (null == appUserVo) {
                        log.warn("FaultMessageAlarmLogServiceImpl#charger1600Reminder -> appUserVo({})不存在,自动跳过", userId);
                        continue;
                    }
                    deviceMessageDto.setDeviceType(OsType.valueOf(appUserVo.getAppTypeCode().toUpperCase()));
                    deviceMessageDto.setEmail(appUserVo.getEmail());
                    // 推送消息用户Token(iot-app user_setting表中的push_token字段)
                    UserSettingBo userSettingBo = userSettingBoMap.get(userId);
                    if (userSettingBo == null) {
                        log.warn("FaultMessageAlarmLogServiceImpl#charger1600Reminder -> userSetting为空({}),自动跳过", userId);
                        continue;
                    } else if (pushTypes.contains(CommonConstant.ZERO) && StringUtils.isEmpty(userSettingBo.getPushToken())) {
                        log.warn("FaultMessageAlarmLogServiceImpl#charger1600Reminder -> 推送方式包含墓碑但userSetting({})的push_token为空", userId);
                        continue;
                    } else {
                        deviceMessageDto.setToken(userSettingBo.getPushToken());
                    }
                    // 推送消息开关(iot-app user_setting表中的device_message_switch字段)
                    deviceMessageDto.setPushSwitch(userSettingBo.getDeviceMessageSwitch());
                    // 推送消息标题、内容(多语言语种从user_setting表中language字段获取)
                    MessageTemplateBo messageTemplateBo = remoteMessageTemplateService.get(userSettingBo.getLanguage(),
                            charger1600ReminderConfig.getMessageTemplateId());
                    if (Objects.isNull(messageTemplateBo)||Objects.isNull(messageTemplateBo.getTitle())||StringUtils.isEmpty(messageTemplateBo.getTitle().getMessage())) {
                        log.warn("FaultMessageAlarmLogServiceImpl#charger1600Reminder -> userSetting({},{})告警标题为空,自动跳过", userId, userSettingBo.getLanguage());
                        continue;
                    } else {
                        deviceMessageDto.setTitle(messageTemplateBo.getTitle().getMessage());
                    }
                    if (StringUtils.isEmpty(messageTemplateBo.getContent().getMessage())) {
                        log.warn("FaultMessageAlarmLogServiceImpl#charger1600Reminder -> userSetting({},{})告警内容为空,自动跳过", userId, userSettingBo.getLanguage());
                        continue;
                    } else {
                        deviceMessageDto.setContent(messageTemplateBo.getContent().getMessage());
                    }
                    log.info("appUserVo: {}", appUserVo);
                    deviceMessageDtoList.add(deviceMessageDto);
                } catch (Exception e) {
                    log.error("FaultMessageAlarmLogServiceImpl#charger1600Reminder -> user({}),发生异常: {}", userId, e.getMessage());
                }
                if (!CollectionUtils.isEmpty(deviceMessageDtoList)) {
                    // 推送设备消息
                    remoteMessageService.pushDeviceMessageAsync(deviceMessageDtoList);

                    String cacheRate = RedisUtils.getCacheObject(rateKey);
                    if (cacheRate != null) {
                        RedisUtils.deleteObject(cacheRate);
                    }
                    RedisUtils.setCacheObject(rateKey, key);

                    RedisUtils.setCacheObject(key, 1);
                    RedisUtils.expire(key, Duration.ofSeconds(charger1600ReminderConfig.getTriggerDuration()));
                }
            }
        }
    }

    /**
     * 异步处理特定产品的特殊逻辑
     * @param faultMessageAlarm
     */
    public void processMessageBusiness(FaultMessageAlarmLogAddDto faultMessageAlarm,FaultMessage faultMessage,Event event,List<DeviceMessageDto> messageList) {
        //执行责任链分发处理流程
        Map handlerContext = new HashMap();
        handlerContext.put(ContextAttributes.FaultMessageAlarmLogAddDto.getCode(),faultMessageAlarm);
        handlerContext.put(ContextAttributes.FaultMessage.getCode(),faultMessage);
        handlerContext.put(ContextAttributes.EventType.getCode(),Objects.isNull(event)?"":event.getType());
        handlerContext.put(ContextAttributes.DeviceMessage.getCode(),messageList);
        IMsgHandlerManager handlerManager= SpringUtils.getBean(IMsgHandlerManager.class);
        handlerManager.start(handlerContext);
    }
}
