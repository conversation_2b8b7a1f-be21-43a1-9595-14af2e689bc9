package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.technology.api.dto.DeviceDebugLogDto;
import com.chervon.technology.api.vo.DeviceDebugLogPreVo;
import com.chervon.technology.api.vo.DeviceDebugLogVo;
import com.chervon.technology.domain.dataobject.DeviceDebugLog;
import com.chervon.technology.domain.vo.device.DeviceDebugLogSearchVo;

/**
 * <AUTHOR>
 * @date 2022年12月29日
 */
public interface DeviceDebugLogService extends IService<DeviceDebugLog> {


	Page<DeviceDebugLog> pageList(DeviceDebugLogSearchVo req);

	String getDownloadUrl(String key);

	DeviceDebugLogDto getPartPreSignedUrl(DeviceDebugLogVo appDeviceDebugLogVo);

	DeviceDebugLogDto getNextPartPreSignedUrl(DeviceDebugLogPreVo logVo);
}
