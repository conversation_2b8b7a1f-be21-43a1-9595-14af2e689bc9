package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.enums.ApplicationEnum;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.RemoteCategoryService;
import com.chervon.operation.api.vo.CategoryVo;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.entity.BaseType;
import com.chervon.technology.entity.BaseTypeModel;
import com.chervon.technology.entity.DeviceErrorCode;
import com.chervon.technology.req.BaseDeviceErrorCodePageDto;
import com.chervon.technology.req.CategoryDto;
import com.chervon.technology.req.ModelStoreDto;
import com.chervon.technology.req.ModelStoreItemDto;
import com.chervon.technology.resp.BaseDeviceErrorCodePageVo;
import com.chervon.technology.resp.BaseDeviceErrorCodeVo;
import com.chervon.technology.resp.ModelVo;
import com.chervon.technology.service.BaseDeviceErrorCodeService;
import com.chervon.technology.service.BaseTypeModelService;
import com.chervon.technology.service.BaseTypeService;
import com.chervon.technology.service.DeviceErrorCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/13 10:55
 */
@Service
@Slf4j
public class BaseDeviceErrorCodeServiceImpl implements BaseDeviceErrorCodeService {

    @Autowired
    private BaseTypeService baseTypeService;

    @Autowired
    private BaseTypeModelService baseTypeModelService;

    @DubboReference
    private RemoteCategoryService remoteCategoryService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;


    @Autowired
    private DeviceErrorCodeService deviceErrorCodeService;

    @Override
    public PageResult<BaseDeviceErrorCodePageVo> page(BaseDeviceErrorCodePageDto req) {
        LambdaQueryWrapper<BaseType> wrapper = new LambdaQueryWrapper<BaseType>()
                .eq(req.getCategoryId() != null, BaseType::getCategoryId, req.getCategoryId())
                .like(StringUtils.isNotBlank(req.getCategoryCode()), BaseType::getCode, req.getCategoryCode())
                .ge(StringUtils.isNotBlank(req.getCreateStartTime()), BaseType::getCreateTime, req.getCreateStartTime())
                .le(StringUtils.isNotBlank(req.getCreateEndTime()), BaseType::getCreateTime, req.getCreateEndTime())
                .ge(StringUtils.isNotBlank(req.getUpdateStartTime()), BaseType::getUpdateTime, req.getUpdateStartTime())
                .le(StringUtils.isNotBlank(req.getUpdateEndTime()), BaseType::getUpdateTime, req.getUpdateEndTime())
                .orderByDesc(BaseType::getCreateTime);
        Page<BaseType> page = baseTypeService.page(new Page<>(req.getPageNum(), req.getPageSize()), wrapper);
        PageResult<BaseDeviceErrorCodePageVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        res.setList(page.getRecords().stream().map(e -> {
            BaseDeviceErrorCodePageVo vo = new BaseDeviceErrorCodePageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setBaseId(e.getId());
            vo.setCategoryName(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getCategoryLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            vo.setCategoryCode(e.getCode());
            vo.setModelCount(baseTypeModelService.count(new LambdaQueryWrapper<BaseTypeModel>().eq(BaseTypeModel::getBaseTypeId, e.getId())));
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(CategoryDto req) {
        BaseType one = baseTypeService.getOne(new LambdaQueryWrapper<BaseType>().eq(BaseType::getCategoryId, req.getCategoryId()));
        if (one != null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_CATEGORY_EXIST, req.getCategoryId());
        }
        List<CategoryVo> categoryList = remoteCategoryService.listByIds(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(),
                Collections.singletonList(req.getCategoryId())));
        if (CollectionUtils.isEmpty(categoryList)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_CATEGORY_NOT_FOUND, req.getCategoryId());
        }
        one = baseTypeService.getOne(new LambdaQueryWrapper<BaseType>().eq(BaseType::getCode, req.getCategoryCode()));
        if (one != null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_CATEGORY_CODE_EXIST, req.getCategoryCode());
        }
        CategoryVo categoryVo = categoryList.get(0);
        one = new BaseType();
        one.setCategoryId(req.getCategoryId());
        one.setCategoryLangId(Long.parseLong(categoryVo.getCategoryLangId()));
        MultiLanguageBo lang = remoteMultiLanguageService.getById(categoryVo.getCategoryLangId());
        one.setCategoryLangCode(lang.getLangCode());
        one.setCode(req.getCategoryCode());
        baseTypeService.save(one);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CategoryDto req) {
        if (req.getBaseId() == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_ID_NULL);
        }
        BaseType one = baseTypeService.getById(req.getBaseId());
        if (one == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_NULL, req.getBaseId());
        }
        if (Objects.equals(one.getCategoryId(), req.getCategoryId()) && StringUtils.equals(one.getCode(), req.getCategoryCode())) {
            return;
        }
        // 判断下面是否已经添加设备错误码
        long count = deviceErrorCodeService.count(new LambdaQueryWrapper<DeviceErrorCode>().eq(DeviceErrorCode::getBaseTypeId, req.getBaseId()));
        if (count > 0) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_EXIST, "base_type_id", req.getBaseId());
        }
        BaseType newOne = new BaseType();
        newOne.setId(one.getId());
        if (Objects.equals(one.getCategoryId(), req.getCategoryId())) {
            // 品类id相同，则编码不同
            // 判断编码是否已经存在
            BaseType exist = baseTypeService.getOne(new LambdaQueryWrapper<BaseType>().eq(BaseType::getCode, req.getCategoryCode()).ne(BaseType::getId, one.getId()));
            if (exist != null) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_CATEGORY_CODE_EXIST, req.getCategoryCode());
            }
            newOne.setCode(req.getCategoryCode());
        } else if (StringUtils.equals(one.getCode(), req.getCategoryCode())) {
            // 品类id不相同，则编码相同
            // 判断品类是否存在
            List<CategoryVo> categoryList = remoteCategoryService.listByIds(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(),
                    Collections.singletonList(req.getCategoryId())));
            if (CollectionUtils.isEmpty(categoryList)) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_CATEGORY_NOT_FOUND, req.getCategoryId());
            }
            // 判断品类是否已经存在
            BaseType exist = baseTypeService.getOne(new LambdaQueryWrapper<BaseType>().eq(BaseType::getCategoryId, req.getCategoryId()).ne(BaseType::getId, one.getId()));
            if (exist != null) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_CATEGORY_EXIST, req.getCategoryId());
            }
            newOne.setCategoryId(req.getCategoryId());
            CategoryVo categoryVo = categoryList.get(0);
            newOne.setCategoryLangId(Long.parseLong(categoryVo.getCategoryLangId()));
            MultiLanguageBo lang = remoteMultiLanguageService.getById(categoryVo.getCategoryLangId());
            newOne.setCategoryLangCode(lang.getLangCode());
        } else {
            // 品类id不相同，编码不相同
            // 判断编码是否已经存在
            BaseType exist = baseTypeService.getOne(new LambdaQueryWrapper<BaseType>().eq(BaseType::getCode, req.getCategoryCode()).ne(BaseType::getId, one.getId()));
            if (exist != null) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_CATEGORY_CODE_EXIST, req.getCategoryCode());
            }
            newOne.setCode(req.getCategoryCode());
            // 判断品类是否存在
            List<CategoryVo> categoryList = remoteCategoryService.listByIds(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(),
                    Collections.singletonList(req.getCategoryId())));
            if (CollectionUtils.isEmpty(categoryList)) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_CATEGORY_NOT_FOUND, req.getCategoryId());
            }
            // 判断品类是否已经存在
            exist = baseTypeService.getOne(new LambdaQueryWrapper<BaseType>().eq(BaseType::getCategoryId, req.getCategoryId()).ne(BaseType::getId, one.getId()));
            if (exist != null) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_CATEGORY_EXIST, req.getCategoryId());
            }
            newOne.setCategoryId(req.getCategoryId());
            CategoryVo categoryVo = categoryList.get(0);
            newOne.setCategoryLangId(Long.parseLong(categoryVo.getCategoryLangId()));
            MultiLanguageBo lang = remoteMultiLanguageService.getById(categoryVo.getCategoryLangId());
            newOne.setCategoryLangCode(lang.getLangCode());
        }
        baseTypeService.updateById(newOne);
    }

    @Override
    public BaseDeviceErrorCodeVo detail(Long baseId) {
        if (baseId == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_ID_NULL);
        }
        BaseType baseType = baseTypeService.getById(baseId);
        if (baseType == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_NULL, baseId);
        }
        BaseDeviceErrorCodeVo res = new BaseDeviceErrorCodeVo();
        res.setBaseId(baseType.getId());
        res.setCategoryId(baseType.getCategoryId());
        res.setCategoryName(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(baseType.getCategoryLangCode(), LocaleContextHolder.getLocale().getLanguage()));
        res.setCategoryCode(baseType.getCode());
        List<BaseTypeModel> list = baseTypeModelService.list(new LambdaQueryWrapper<BaseTypeModel>().eq(BaseTypeModel::getBaseTypeId, baseType.getId()));
        res.setModels(list.stream().map(e -> {
            ModelVo model = new ModelVo();
            model.setModelId(e.getId());
            model.setModelCode(e.getCode());
            model.setModelLangId(e.getNameLangId());
            model.setModelName(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getNameLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            return model;
        }).collect(Collectors.toList()));
        return res;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void storeModels(ModelStoreDto req) {
        BaseType baseType = baseTypeService.getById(req.getBaseId());
        if (baseType == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_NULL, req.getBaseId());
        }
        if (CollectionUtils.isEmpty(req.getItems())) {
            return;
        }
        List<String> mcList = req.getItems().stream().map(ModelStoreItemDto::getModelCode).distinct().collect(Collectors.toList());
        if (mcList.size() != req.getItems().size()) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_MODEL_CODE_SAME, JsonUtils.toJson(req.getItems()));
        }
        // 校验模块编码是否重复
        for (ModelStoreItemDto item : req.getItems()) {
            List<BaseTypeModel> ids = baseTypeModelService.list(new LambdaQueryWrapper<BaseTypeModel>()
                    .eq(BaseTypeModel::getBaseTypeId, req.getBaseId())
                    .ne(item.getModelId() != null, BaseTypeModel::getId, item.getModelId())
                    .eq(BaseTypeModel::getCode, item.getModelCode())
                    .select(BaseTypeModel::getId));
            if (!CollectionUtils.isEmpty(ids)) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_MODEL_CODE_SAME2, item.getModelCode());
            }
        }
        Map<String, String> map = new HashMap<>();
        final int[] f = {1};
        List<BaseTypeModel> models = req.getItems().stream().map(e -> {
            BaseTypeModel model = new BaseTypeModel();
            model.setBaseTypeId(req.getBaseId());
            model.setName(e.getModelName());
            model.setCode(e.getModelCode());
            model.setId(e.getModelId());
            if (e.getModelId() == null) {
                map.put(f[0] + "", e.getModelName());
                model.setName(f[0] + "");
                f[0]++;
            }
            return model;
        }).collect(Collectors.toList());

        if (map.size() > 0) {
            Map<String, MultiLanguageBo> languageBoMap = remoteMultiLanguageService.simpleCreateMultiLanguages(ApplicationEnum.IOT_PLATFORM.getName(), map, LocaleContextHolder.getLocale().getLanguage());
            models.forEach(e -> {
                MultiLanguageBo bo = languageBoMap.get(e.getName());
                if (bo != null) {
                    e.setName(map.get(e.getName()));
                    e.setNameLangId(bo.getLangId());
                    e.setNameLangCode(bo.getLangCode());
                }
            });
        }
        baseTypeModelService.saveOrUpdateBatch(models);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteModel(Long modelId) {
        if (modelId == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_MODEL_ID_NULL);
        }
        long count = deviceErrorCodeService.count(new LambdaQueryWrapper<DeviceErrorCode>().eq(DeviceErrorCode::getModelId, modelId));
        if (count > 0) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_EXIST, "model_id", modelId);
        }
        BaseTypeModel baseTypeModel = baseTypeModelService.getById(modelId);
        if (baseTypeModel != null && baseTypeModel.getNameLangCode() != null) {
            remoteMultiLanguageService.deleteByLangCodes(Arrays.asList(baseTypeModel.getNameLangCode()));
        }
        baseTypeModelService.removeById(modelId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long baseId) {
        if (baseId == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_ID_NULL);
        }
        long modelCount = baseTypeModelService.count(new LambdaQueryWrapper<BaseTypeModel>().eq(BaseTypeModel::getBaseTypeId, baseId));
        if (modelCount > 0) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_BASE_MODEL_EXIST, baseId);
        }
        // 判断下面是否已经添加设备错误码
        long count = deviceErrorCodeService.count(new LambdaQueryWrapper<DeviceErrorCode>().eq(DeviceErrorCode::getBaseTypeId, baseId));
        if (count > 0) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_EXIST, "base_type_id", baseId);
        }
        // 删除相关的多语言
        BaseType baseType = baseTypeService.getById(baseId);
        if (baseType != null && baseType.getCategoryLangCode() != null) {
            remoteMultiLanguageService.deleteByLangCodes(Arrays.asList(baseType.getCategoryLangCode()));
        }
        baseTypeService.removeById(baseId);
    }
}
