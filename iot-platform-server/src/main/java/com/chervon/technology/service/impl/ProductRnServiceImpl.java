package com.chervon.technology.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.operation.api.RemoteGroupService;
import com.chervon.technology.api.dto.LastRnPackageDto;
import com.chervon.technology.api.vo.rn.RnItemVo;
import com.chervon.technology.config.*;
import com.chervon.technology.entity.ProductRn;
import com.chervon.technology.mapper.ProductRnMapper;
import com.chervon.technology.req.OriginRnPageDto;
import com.chervon.technology.req.ProductRnAddOrUpdateDto;
import com.chervon.technology.req.ProductRnPageDto;
import com.chervon.technology.resp.ProductRnPageVo;
import com.chervon.technology.resp.ProductRnVo;
import com.chervon.technology.service.ProductRnService;
import com.chervon.technology.util.VersionCompareUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.*;

import static com.chervon.technology.api.exception.TechnologyErrorCode.*;
import static com.chervon.technology.config.RnReleaseStateEnum.*;
import static com.chervon.technology.rpc.DeviceManageRpcSerivceImpl.RN_BUNDLE_NAME_NULL;

/**
 * <AUTHOR>
 * @date 2022/7/19 16:44
 */
@Service
@Slf4j
public class ProductRnServiceImpl extends ServiceImpl<ProductRnMapper, ProductRn> implements ProductRnService {


    @Autowired
    private AppVersionConfig appVersionConfig;

    @DubboReference
    private RemoteGroupService remoteGroupService;

    @Override
    public PageResult<RnItemVo> originRnPage(OriginRnPageDto req) {
        RestTemplate restTemplate = new RestTemplate();
        JSONObject res;
        List<String> tenantAppId = new ArrayList<>();
        if (req.getAppName() == 0) {
            tenantAppId.add("EGO_Connect");
            tenantAppId.add("Fleet_Service");
        } else if (req.getAppName() == 1) {
            tenantAppId.add("EGO_Connect");
        } else if (req.getAppName() == 2) {
            tenantAppId.add("Fleet_Service");
        }
        try {
            res = restTemplate.postForObject(appVersionConfig.getRnPageUrl(), new HashMap<String, Object>(16) {{
                put("tenantAppId", tenantAppId);
                put("name", req.getRnName());
                put("version", req.getRnVersion());
                put("pageNo", req.getPageNum());
                put("pageSize", req.getPageSize());
            }}, JSONObject.class);
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_GET_PAGE_ERROR);
        }
        if (res == null || res.getInt("code") != 200) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_GET_PAGE_ERROR);
        }
        JSONObject data = res.getJSONObject("data");
        PageResult<RnItemVo> page = new PageResult<>();
        page.setPages(data.getInt("pages"));
        page.setPageNum(data.getInt("current"));
        page.setPageSize(data.getInt("size"));
        page.setTotal(data.getLong("total"));
        page.setList(JSONUtil.toList(data.getJSONArray("records"), RnItemVo.class));
        if (page.getList() != null) {
            page.getList().forEach(e -> {
                if (StringUtils.equals(e.getAppId(), "EGO_Connect")) {
                    e.setAppName("1");
                } else if (StringUtils.equals(e.getAppId(), "Fleet_Service")) {
                    e.setAppName("2");
                } else {
                    e.setAppName("unknown");
                }
            });
        }
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addOrUpdate(ProductRnAddOrUpdateDto req) {
        if (req.getProductRnId() == null) {
            //新增操作
            checkRnSelected(req.getRn());
            // 新增
            QueryWrapper<ProductRn> queryWrapper = new QueryWrapper<ProductRn>()
                    .eq("rn_type", req.getRn().getRnType())
                    .eq("product_id", req.getProductId())
                    .eq(StringUtils.isNotEmpty(req.getRn().getAppId()),"app_id",req.getRn().getAppId())
                    .orderByDesc("CONVERT(substring_index(substring_index(substring_index(rn_version,'-',1),'.',1),'.',-1),SIGNED)")
                    .orderByDesc("CONVERT(substring_index(substring_index(substring_index(rn_version,'-',1),'.',2),'.',-1),SIGNED)")
                    .orderByDesc("CONVERT(substring_index(substring_index(substring_index(rn_version,'-',1),'.',3),'.',-1),SIGNED)")
                    .last("limit 1");

            ProductRn versionMaxOne = this.getOne(queryWrapper);
            if (versionMaxOne != null) {
                if (VersionCompareUtils.compareVersion(req.getRn().getRnVersion(), versionMaxOne.getRnVersion()) <= 0) {
                    throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_ADD_VERSION_SMALL);
                }
            }
            ProductRn productRn = new ProductRn();
            BeanUtils.copyProperties(req, productRn);
            BeanUtils.copyProperties(req.getRn(), productRn);
            productRn.setReleaseStatus(WILL_RELEASE.getCode());
            this.save(productRn);
        } else {
            // 修改
            ProductRn one = this.getById(req.getProductRnId());
            if (one == null) {
                throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_NOT_EXIST, req.getProductRnId());
            }
            checkRnSelected(req.getRn());

            QueryWrapper<ProductRn> queryWrapper = new QueryWrapper<ProductRn>().eq("rn_type", req.getRn().getRnType())
                    .eq("product_id", req.getProductId())
                    .eq("app_id",one.getAppId())
                    .orderByDesc("CONVERT(substring_index(substring_index(substring_index(rn_version,'-',1),'.',1),'.',-1),SIGNED)")
                    .orderByDesc("CONVERT(substring_index(substring_index(substring_index(rn_version,'-',1),'.',2),'.',-1),SIGNED)")
                    .orderByDesc("CONVERT(substring_index(substring_index(substring_index(rn_version,'-',1),'.',3),'.',-1),SIGNED)")
                    .ne("id", one.getId())
                    .last("limit 1");

            ProductRn versionMaxOne = this.getOne(queryWrapper);
            if (versionMaxOne != null) {
                if (VersionCompareUtils.compareVersion(req.getRn().getRnVersion(), versionMaxOne.getRnVersion()) <= 0) {
                    throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_ADD_VERSION_SMALL);
                }
            }

            ProductRn productRn = new ProductRn();
            productRn.setId(req.getProductRnId());
            BeanUtils.copyProperties(req.getRn(), productRn);
            productRn.setUpdateContent(req.getUpdateContent());
            productRn.setRemark(req.getRemark());
            this.updateById(productRn);
        }
    }

    private void checkRnSelected(RnItemVo rn) {
        if (StringUtils.isBlank(rn.getAppId())) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_APP_ID_NULL);
        }
        if (StringUtils.isBlank(rn.getAppName())) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_APP_NAME_NULL);
        }
        if (StringUtils.isBlank(rn.getRnName())) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_NAME_NULL);
        }
        if (StringUtils.isBlank(rn.getRnType())) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_TYPE_NULL);
        }
        if (StringUtils.isBlank(rn.getResourceUrl())) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_RESOURCE_URL_NULL);
        }
        if (StringUtils.isBlank(rn.getRnVersion())) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_VERSION_NULL);
        }
        if (StringUtils.isBlank(rn.getVersionMin())) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_VERSION_MIN_NULL);
        }
        if (StringUtils.isBlank(rn.getVersionMax())) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_VERSION_MAX_NULL);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long productRnId) {
        ProductRn one = this.getById(productRnId);
        if (one == null) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_NOT_EXIST, productRnId);
        }
        if(RELEASED.getCode().equals(one.getReleaseStatus())){
            throw ExceptionMessageUtil.getException(TECHNOLOGY_CANNOT_DELETE_RELEASED_RN, productRnId);
        }
        this.removeById(productRnId);
    }

    @Override
    public String getRnUrl(Long productRnId) {
        ProductRn one = this.getById(productRnId);
        if (one == null) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_NOT_EXIST, productRnId);
        }
        return one.getResourceUrl();
    }

    @Override
    public PageResult<ProductRnPageVo> page(ProductRnPageDto req) {
        LambdaQueryWrapper<ProductRn> wrapper = new LambdaQueryWrapper<>();
        if (req.getProductId() == null) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_PRODUCT_ID_NULL);
        }
        if (StringUtils.isNotBlank(req.getVersionAndroidMin()) && StringUtils.isNotBlank(req.getVersionIosMin())) {
            return new PageResult<>(req.getPageNum(), req.getPageSize(), 0);
        }
        wrapper.eq(ProductRn::getProductId, req.getProductId());
        wrapper.eq(StringUtils.isNotBlank(req.getReleaseStatus()),ProductRn::getReleaseStatus, req.getReleaseStatus());
        wrapper.eq(StringUtils.isNotBlank(req.getAppName()), ProductRn::getAppName, req.getAppName());
        wrapper.like(StringUtils.isNotBlank(req.getRnName()), ProductRn::getRnName, req.getRnName());
        wrapper.like(StringUtils.isNotBlank(req.getRnVersion()), ProductRn::getRnVersion, req.getRnVersion());
        wrapper.like(StringUtils.isNotBlank(req.getAppId()), ProductRn::getAppId, req.getAppId());
        if (StringUtils.isNotBlank(req.getVersionIosMin())) {
            wrapper.eq(ProductRn::getRnType, "ios");
            wrapper.like(ProductRn::getVersionMin, req.getVersionIosMin());
        }
        if (StringUtils.isNotBlank(req.getVersionAndroidMin())) {
            wrapper.eq(ProductRn::getRnType, "android");
            wrapper.like(ProductRn::getVersionMin, req.getVersionAndroidMin());
        }
        if (StringUtils.isNotBlank(req.getVersionIosMax())) {
            wrapper.eq(ProductRn::getRnType, "ios");
            wrapper.like(ProductRn::getVersionMax, req.getVersionIosMax());
        }
        if (StringUtils.isNotBlank(req.getVersionAndroidMax())) {
            wrapper.eq(ProductRn::getRnType, "android");
            wrapper.like(ProductRn::getVersionMax, req.getVersionAndroidMax());
        }
        if (null != req.getUpdateStartTime()) {
            wrapper.ge(ProductRn::getUpdateTime, req.getUpdateStartTime());
        }
        if (null != req.getUpdateEndTime()) {
            wrapper.le(ProductRn::getUpdateTime, req.getUpdateEndTime());
        }

        wrapper.orderByDesc(ProductRn::getCreateTime);
        Page<ProductRn> page = this.page(new Page<>(req.getPageNum(), req.getPageSize()), wrapper);
        PageResult<ProductRnPageVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<ProductRnPageVo> list = new ArrayList<>();
        page.getRecords().forEach(e -> {
            ProductRnPageVo vo = new ProductRnPageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setProductRnId(e.getId());
            // 设置rn
            RnItemVo rnVo = new RnItemVo();
            BeanUtils.copyProperties(e, rnVo);
            vo.setRn(rnVo);
            list.add(vo);
        });
        res.setList(list);
        return res;
    }

    @Override
    public ProductRn getMaxRn(LastRnPackageDto req) {
        if (req.getProductId() == null) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_COMMON_PARAM_ERROR);
        }
        if (StringUtils.isBlank(req.getAppType()) || !Arrays.asList("android", "ios").contains(req.getAppType().toLowerCase())) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_LAST_APP_TYPE_ERROR);
        }
        if (StringUtils.isBlank(req.getAppVersion())) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_LAST_APP_VERSION_BLANK);
        }
        // 查询所有符合的版本
        LambdaQueryWrapper<ProductRn> queryWrapper = new LambdaQueryWrapper<ProductRn>()
                .eq(ProductRn::getReleaseStatus, RELEASED.getCode())
                .eq(ProductRn::getRnType, req.getAppType())
                .eq(ProductRn::getAppName, req.getBusinessType())
                .eq(ProductRn::getProductId, req.getProductId());
        List<ProductRn> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        // 比较版本
        ProductRn max = null;
        for (ProductRn e : list) {
            //比较是否在适配APP版本区间內
            if (VersionCompareUtils.compareVersion(e.getVersionMin(), req.getAppVersion()) <= 0 && VersionCompareUtils.compareVersion(e.getVersionMax(), req.getAppVersion()) >= 0) {
                //找出最大版本
                if (max == null || VersionCompareUtils.compareVersion(max.getRnVersion(), e.getRnVersion()) < 0) {
                    max = e;
                }
            }
        }
        return max;
    }

    //设备详情接口查询rn包，避免没有配置 重复查询 缓存rn bundleName
    @Override
    public void rnNameToRedis(String rnName, Long userId, Long productId, String appType, String appVersion) {
        String key =String.format(IotPlatformCommon.RN_BUNDLE_NAME_KEY,userId,productId,
                appType ,appVersion);
        RedisUtils.setCacheObject(key, StringUtils.isBlank(rnName) ? RN_BUNDLE_NAME_NULL : rnName);
        // 设置10分钟有效
        RedisUtils.expire(key, Duration.ofMinutes(10));
    }

    @Override
    public String getLatestBundleName(LastRnPackageDto req, Long userId) {
        String key = String.format(IotPlatformCommon.RN_BUNDLE_NAME_KEY,userId,req.getProductId(),
                req.getAppType() ,req.getAppVersion());

        String rnBundleName = RedisUtils.getCacheObject(key);
        //缓存为空查询数据库
        if (StringUtils.isEmpty(rnBundleName)) {
            ProductRn max = this.getMaxRn(req);
            rnBundleName = max == null ? null : max.getRnName();
            this.rnNameToRedis(rnBundleName, userId, req.getProductId(), req.getAppType(), req.getAppVersion());

            if (StringUtils.isEmpty(rnBundleName)) {
                // 如果rnBundleName为空是这是一个字符，以防后续多次调用
                rnBundleName = RN_BUNDLE_NAME_NULL;
            }
        }
        if (!RN_BUNDLE_NAME_NULL.equals(rnBundleName)) {
            return rnBundleName;
        }
        return null;
    }

    @Override
    public void releaseRn(Long productRnId) {
        //检查并获取
        ProductRn productRn=checkAndGetProductRn(productRnId);
        productRn.setReleaseStatus(RELEASED.getCode());
        updateById(productRn);
    }

    @Override
    public void withdrawReleaseRn(Long productRnId) {
        //检查并获取
        ProductRn productRn=checkAndGetProductRn(productRnId);
        productRn.setReleaseStatus(WITHDRAW_RELEASED.getCode());
        updateById(productRn);
    }


    public ProductRn checkAndGetProductRn(long productRnId){
        ProductRn productRn = this.getById(productRnId);
        if (Objects.isNull(productRn)) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_RN_NOT_EXIST, productRnId);
        }
        return productRn;
    }
}
