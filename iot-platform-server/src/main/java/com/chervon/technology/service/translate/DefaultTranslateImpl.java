package com.chervon.technology.service.translate;

import com.chervon.common.core.enums.EnumUtils;
import com.chervon.common.web.annotation.Translate;
import com.chervon.common.web.core.TranslateService;
import com.chervon.common.web.entity.ConvertType;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 通用多语言翻译
 * <AUTHOR> 2022/12/6
 */
@Component
public class DefaultTranslateImpl implements TranslateService<Object,Object,Object> {

    public String getInstanceCode(){
        return "default";
    }

    @Override
    public Object getSourceValue(Field field, Object dataEntity) throws IllegalAccessException {
        final Translate translate = field.getAnnotation(Translate.class);
        Object objValue = field.get(dataEntity);
        Integer intValue = null;
        if (objValue == null) {
            return null;
        }
        if (translate.type() == ConvertType.ENUM) {
            intValue = Integer.valueOf(objValue.toString());
            return EnumUtils.getDesc(translate.enumType(), intValue);
        } else if (translate.type() == ConvertType.DATEFORMAT) {
            Class<?> aClass = objValue.getClass();
            if (aClass == LocalDateTime.class) {
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(translate.dateFormat());
                return ((LocalDateTime) objValue).format(dateTimeFormatter);
            }
        }else{
            return null;
        }
        return null;
    }

    @Override
    public void setTargetValue(Object dataDto, Object result, List<String> targetField) throws IllegalAccessException {
        if(Objects.isNull(result)){
            return;
        }
        Class<?> aClass = dataDto.getClass();
        List<Field> fields = TranslateUtils.getBindFields(aClass,targetField);
        for (Field field : fields) {
            field.setAccessible(true);
            field.set(dataDto, result);
        }
    }
}
