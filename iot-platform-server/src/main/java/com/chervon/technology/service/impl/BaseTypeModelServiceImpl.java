package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.technology.entity.BaseTypeModel;
import com.chervon.technology.mapper.BaseTypeModelMapper;
import com.chervon.technology.service.BaseTypeModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/9/13 9:41
 */
@Service
@Slf4j
public class BaseTypeModelServiceImpl extends ServiceImpl<BaseTypeModelMapper, BaseTypeModel> implements BaseTypeModelService {

    @Override
    public BaseTypeModel getByCode(String categoryCode, String modelCode) {
        return this.baseMapper.selectByCode(categoryCode, modelCode);
    }
}
