package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.domain.dataobject.Component;
import com.chervon.technology.domain.dto.component.AddComponentDto;
import com.chervon.technology.domain.dto.component.EditComponentDto;
import com.chervon.technology.domain.dto.component.SearchComponentDto;
import com.chervon.technology.domain.vo.component.ComponentVo;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-14
 */
public interface ComponentService extends IService<Component> {

    /**
     * 添加总成零件
     * @param addComponentParts
     */
    void addComponent(AddComponentDto addComponentParts);

    /**
     * 编辑总成零件
     * @param editComponentParts
     */
    void editComponent(EditComponentDto editComponentParts);

    /**
     * 删除总成零件
     * @param id
     */
    void deleteComponent(Long id);

    /**
     * 根据Id获取总成零件
     * @param id
     * @return
     */
    ComponentVo getDetail(Long id);

    /**
     * 分页获取总成零件列表
     * @param search
     * @return
     */
    PageResult<ComponentVo> list(SearchComponentDto search);

    /**
     * 获取所有总成
     * @return
     */
    List<ComponentVo> all();

    /**
     * 根据总成零件号获取总成
     * @param componentNo
     * @return
     */
    Component getByNo(String componentNo);

    /**
     * 根据List获取总成零件
     * @param componentNos
     * @return
     */
    List<Component> getByNos(List<String> componentNos);
}
