package com.chervon.technology.service.impl;

import com.amazonaws.services.s3.model.PartETag;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.oss.constant.RedisKeyConstant;
import com.chervon.common.oss.uitl.S3Util;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.middle.api.service.RemoteDeviceService;
import com.chervon.technology.api.dto.DeviceDebugLogDto;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.vo.DeviceDebugLogPreVo;
import com.chervon.technology.api.vo.DeviceDebugLogVo;
import com.chervon.technology.domain.dataobject.DeviceDebugLog;
import com.chervon.technology.domain.vo.device.DeviceDebugLogSearchVo;
import com.chervon.technology.mapper.DeviceDebugLogMapper;
import com.chervon.technology.service.DeviceDebugLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022年12月29日
 */
@Service
@Slf4j
public class DeviceDebugLogServiceImpl extends ServiceImpl<DeviceDebugLogMapper, DeviceDebugLog> implements DeviceDebugLogService {

    @Autowired
    private S3Util s3Util;

    @Autowired
    private AwsProperties awsProperties;

    public static final String DEBUG_LOG = "debugLog";

    @DubboReference
    private RemoteDeviceService remoteDeviceService;

    @Override
    public Page<DeviceDebugLog> pageList(DeviceDebugLogSearchVo req) {
        LambdaQueryWrapper<DeviceDebugLog> wrapper = new LambdaQueryWrapper<DeviceDebugLog>()
                .eq(StringUtils.isNotBlank(req.getDeviceId()), DeviceDebugLog::getDeviceId, req.getDeviceId())
                .like(StringUtils.isNotBlank(req.getFileName()), DeviceDebugLog::getFileName, req.getFileName())
                .ge(StringUtils.isNotBlank(req.getCreateStartTime()), DeviceDebugLog::getCreateTime, req.getCreateStartTime())
                .le(StringUtils.isNotBlank(req.getCreateEndTime()), DeviceDebugLog::getCreateTime, req.getCreateEndTime())
                .ge(StringUtils.isNotBlank(req.getUpdateStartTime()), DeviceDebugLog::getUpdateTime, req.getUpdateStartTime())
                .le(StringUtils.isNotBlank(req.getUpdateEndTime()), DeviceDebugLog::getUpdateTime, req.getUpdateEndTime())
                .eq(DeviceDebugLog::getIsDeleted, 0);
        return this.page(new Page<>(req.getPageNum(), req.getPageSize()), wrapper);
    }

    @Override
    public String getDownloadUrl(String key) {
        Date expiration = new Date(System.currentTimeMillis() + OtaJobServiceImpl.HALF_HOUR_MILLIS);
        return s3Util.getPreSignedGetPrivateUrl(awsProperties.getPictureBucket().getName(), key, expiration);
    }


    @Override
    public DeviceDebugLogDto getPartPreSignedUrl(DeviceDebugLogVo appDeviceDebugLogVo) {
        //入库
        DeviceDebugLog convert = ConvertUtil.convert(appDeviceDebugLogVo, DeviceDebugLog.class);
        //ota/uploadUrl
        String key = DEBUG_LOG + appDeviceDebugLogVo.getDeviceId() + OtaJobServiceImpl.FILE_SPLIT_CODE + appDeviceDebugLogVo.getFileName();
        String uploadId = s3Util.createMultipartUpload(awsProperties.getPictureBucket().getName(), key, appDeviceDebugLogVo.getTotalPartNum(), true);
        Date expiration = new Date(System.currentTimeMillis() + OtaJobServiceImpl.HALF_HOUR_MILLIS);
        String preSignedUrl = s3Util.getPartPreSignedUrl(awsProperties.getPictureBucket().getName(), key, expiration, true, false, uploadId, 1);
        convert.setUploadId(uploadId);
        convert.setFileKey(key);
        this.save(convert);
        DeviceDebugLogDto deviceDebugLogDto = new DeviceDebugLogDto();
        deviceDebugLogDto.setPreSignedUrl(preSignedUrl);
        deviceDebugLogDto.setUploadId(uploadId);
        deviceDebugLogDto.setPartId(1);
        return deviceDebugLogDto;
    }

    /**
     * 上传结果
     *
     * @return
     */
    @Override
    public DeviceDebugLogDto getNextPartPreSignedUrl(DeviceDebugLogPreVo logVo) {
        if (logVo.getResultCode().equals(CommonConstant.ONE)) {
            //失败打印失败信息
            log.error(String.format(TechnologyErrorCode.TECHNOLOGY_DEVICE_DEBUG_FILE_MULTIPART_UPLOAD_ERROR.getErrorMessage(), logVo.getDeviceId(), logVo.getUploadId(), logVo.getErrorMsg()));
            return null;
        }
        LambdaQueryWrapper<DeviceDebugLog> eq = new LambdaQueryWrapper<DeviceDebugLog>()
                .eq(DeviceDebugLog::getUploadId, logVo.getUploadId());
        DeviceDebugLog deviceDebugLog = this.getOne(eq);
        String key = deviceDebugLog.getFileKey();
        String eTagKey = RedisKeyConstant.S3_PART_ETAG_PRE + logVo.getUploadId();
        RedisUtils.setCacheMapValue(eTagKey, String.valueOf(logVo.getPartNum()), logVo.getTagStr());
        if (logVo.getPartNum() < deviceDebugLog.getTotalPartNum()) {
            //小于总段数-获取下一段的
            Date expiration = new Date(System.currentTimeMillis() + OtaJobServiceImpl.HALF_HOUR_MILLIS);
            String preSignedUrl = s3Util.getPartPreSignedUrl(awsProperties.getPictureBucket().getName(), key, expiration, true, false, deviceDebugLog.getUploadId(), logVo.getPartNum() + 1);
            DeviceDebugLogDto deviceDebugLogDto = new DeviceDebugLogDto();
            deviceDebugLogDto.setPreSignedUrl(preSignedUrl);
            deviceDebugLogDto.setUploadId(deviceDebugLog.getUploadId());
            this.update(new DeviceDebugLog(), new LambdaUpdateWrapper<DeviceDebugLog>().set(DeviceDebugLog::getSuccessPartNum, logVo.getPartNum()).eq(DeviceDebugLog::getUploadId, logVo.getUploadId()));
            return deviceDebugLogDto;
        }
        Map<String, Object> hashValue = RedisUtils.getCacheMap(eTagKey);
        List<PartETag> partETags = new ArrayList<>();
        for (String numberKey : hashValue.keySet()) {
            PartETag eTag = new PartETag(Integer.parseInt(numberKey), (String) hashValue.get(numberKey));
            partETags.add(eTag);
        }
        //合并分段
        Boolean aBoolean = s3Util.completeMultipartUpload(awsProperties.getPictureBucket().getName(), key, deviceDebugLog.getUploadId(), partETags);
        if (aBoolean) {
            this.update(new DeviceDebugLog(), new LambdaUpdateWrapper<DeviceDebugLog>().set(DeviceDebugLog::getSuccessPartNum, deviceDebugLog.getTotalPartNum())
                    .eq(DeviceDebugLog::getUploadId, logVo.getUploadId()));
        }
        return new DeviceDebugLogDto();

    }


}
