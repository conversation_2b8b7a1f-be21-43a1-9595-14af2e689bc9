package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.technology.domain.dataobject.ShortcutFunction;
import com.chervon.technology.domain.dto.shortcutFunction.ShortcutFunctionAddDto;
import com.chervon.technology.domain.dto.shortcutFunction.ShortcutFunctionDto;
import com.chervon.technology.domain.dto.shortcutFunction.ShortcutFunctionEditDto;
import com.chervon.technology.domain.vo.shortcutFunction.ShortcutFunctionDetailVo;
import com.chervon.technology.domain.vo.shortcutFunction.ShortcutFunctionVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ShortcutFunctionService extends IService<ShortcutFunction> {
    /**
     * 添加
     * @param shortcutFunctionAddDto 参数
     */
    void add(ShortcutFunctionAddDto shortcutFunctionAddDto);

    /**
     * 修改
     * @param shortcutFunctionEditDto 参数
     */
    void edit(ShortcutFunctionEditDto shortcutFunctionEditDto);

    /**
     * 删除
     * @param id 主键id
     */
    void delete(Long id);

    /**
     * 列表
     * @param shortcutFunctionDto 参数
     * @return 结果集
     */
    List<ShortcutFunctionVo> query(ShortcutFunctionDto shortcutFunctionDto);

    /**
     * 详情
     * @param id 主键id
     * @return 结果集
     */
    ShortcutFunctionDetailVo getDetail(Long id);

    /**
     * 根据设备id查询快捷键功能列表
     * @param deviceId 设备id
     * @return 结果集
     */
    List<ShortcutFunctionVo> listByDeviceId(String deviceId);
}
