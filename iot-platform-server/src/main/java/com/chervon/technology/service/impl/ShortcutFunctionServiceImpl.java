package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.iot.middle.api.pojo.thingmodel.Event;
import com.chervon.iot.middle.api.pojo.thingmodel.Property;
import com.chervon.iot.middle.api.service.RemoteThingModelService;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.domain.bo.DictBo;
import com.chervon.technology.domain.bo.DictNodeBo;
import com.chervon.technology.domain.dataobject.Device;
import com.chervon.technology.domain.dataobject.Product;
import com.chervon.technology.domain.dataobject.ShortcutFunction;
import com.chervon.technology.domain.dto.shortcutFunction.ShortcutFunctionAddDto;
import com.chervon.technology.domain.dto.shortcutFunction.ShortcutFunctionDto;
import com.chervon.technology.domain.dto.shortcutFunction.ShortcutFunctionEditDto;
import com.chervon.technology.domain.enums.EventEnum;
import com.chervon.technology.domain.vo.shortcutFunction.ShortcutFunctionDetailVo;
import com.chervon.technology.domain.vo.shortcutFunction.ShortcutFunctionVo;
import com.chervon.technology.mapper.ProductMapper;
import com.chervon.technology.mapper.ShortcutFunctionMapper;
import com.chervon.technology.service.DeviceService;
import com.chervon.technology.service.DictService;
import com.chervon.technology.service.ShortcutFunctionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date Created in 2022/11/8 17:15
 */
@Slf4j
@Service
@AllArgsConstructor
public class ShortcutFunctionServiceImpl extends ServiceImpl<ShortcutFunctionMapper, ShortcutFunction>
        implements ShortcutFunctionService {
    @DubboReference
    private RemoteThingModelService iotThingModelService;
    private final ProductMapper productMapper;
    private static final String ACCESSMODE = "accessMode";
    @Autowired
    private DictService dictService;
    @Resource
    private DeviceService deviceService;
    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ShortcutFunctionAddDto shortcutFunctionAddDto) {
        ShortcutFunction shortcutFunction = ConvertUtil.convert(shortcutFunctionAddDto, ShortcutFunction.class);
        this.save(shortcutFunction);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(ShortcutFunctionEditDto shortcutFunctionEditDto) {
        ShortcutFunction shortcutFunction = this.getById(shortcutFunctionEditDto.getId());
        if (null == shortcutFunction) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_SHORTCUT_FUNCTION_NOT_EXIST, shortcutFunctionEditDto.getId());
        }
        shortcutFunction = ConvertUtil.convert(shortcutFunctionEditDto, ShortcutFunction.class);
        shortcutFunction.setId(shortcutFunctionEditDto.getId());
        this.updateById(shortcutFunction);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        ShortcutFunction shortcutFunction = this.getById(id);
        if (shortcutFunction != null && shortcutFunction.getMultiLanguageId() != null) {
            // 清理多语言
            remoteMultiLanguageService.deleteByLangIds(Arrays.asList(Long.valueOf(shortcutFunction.getMultiLanguageId())));
        }
        this.removeById(id);
    }

    @Override
    public List<ShortcutFunctionVo> query(ShortcutFunctionDto shortcutFunctionDto) {
        LambdaQueryWrapper<Product> productWrapper = new LambdaQueryWrapper<Product>()
                .eq(Product::getId, shortcutFunctionDto.getProductId().toString())
                .eq(Product::getIsDeleted, CommonConstant.ZERO);
        Product product = productMapper.selectOne(productWrapper);
        List<ShortcutFunctionVo> voList = new ArrayList<>();
        LambdaQueryWrapper<ShortcutFunction> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShortcutFunction::getProductId, shortcutFunctionDto.getProductId());
        wrapper.orderByDesc(ShortcutFunction::getCreateTime);
        List<ShortcutFunction> list = this.list(wrapper);
        if (list != null && !list.isEmpty()) {
            for (ShortcutFunction shortcutFunction : list) {
                ShortcutFunctionVo shortcutFunctionVo = ConvertUtil.convert(shortcutFunction, ShortcutFunctionVo.class);
                if ((CommonConstant.ZERO.toString()).equals(shortcutFunction.getConditionType())) {
                    Property property = iotThingModelService.getProperty(shortcutFunctionDto.getProductId().toString(), shortcutFunction.getPropertyId());
                    if (property != null) {
                        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList(ACCESSMODE));
                        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
                        shortcutFunctionVo.setType(collect.get(ACCESSMODE).getNodes()
                                .stream()
                                .filter(i -> org.apache.commons.lang3.StringUtils.equals(i.getLabel(), property.getAccessMode() + ""))
                                .findFirst()
                                .orElse(new DictNodeBo())
                                .getDescription());
                    }
                } else if ((CommonConstant.ONE.toString()).equals(shortcutFunction.getConditionType())) {
                    Event event = iotThingModelService.getEvent(shortcutFunctionDto.getProductId().toString(), shortcutFunction.getPropertyId());
                    if (event != null) {
                        shortcutFunctionVo.setType(EventEnum.getLabelByValue(event.getType()));
                    }
                }
                MultiLanguageBo nameBo = remoteMultiLanguageService.getById(shortcutFunction.getMultiLanguageId());
                MultiLanguageVo nameVo = new MultiLanguageVo(nameBo.getLangId(),
                        com.chervon.technology.config.MultiLanguageUtil.getByLangCode(nameBo.getLangCode(), LocaleContextHolder.getLocale().getLanguage()));
                shortcutFunctionVo.setFunctionName(nameVo);
                voList.add(shortcutFunctionVo);
            }
        }
        return voList;
    }

    @Override
    public ShortcutFunctionDetailVo getDetail(Long id) {
        ShortcutFunction shortcutFunction = this.getById(id);
        if (null == shortcutFunction) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_SHORTCUT_FUNCTION_NOT_EXIST, id);
        }
        return ConvertUtil.convert(shortcutFunction, ShortcutFunctionDetailVo.class);
    }

    @Override
    public List<ShortcutFunctionVo> listByDeviceId(String deviceId) {
        LambdaQueryWrapper<Device> wrapperDevice = new LambdaQueryWrapper<>();
        wrapperDevice.eq(Device::getDeviceId, deviceId);
        Device device = deviceService.getOne(wrapperDevice);
        LambdaQueryWrapper<Product> productWrapper = new LambdaQueryWrapper<Product>()
                .eq(Product::getId, device.getProductId().toString())
                .eq(Product::getIsDeleted, CommonConstant.ZERO);
        Product product = productMapper.selectOne(productWrapper);
        List<ShortcutFunctionVo> voList = new ArrayList<>();
        LambdaQueryWrapper<ShortcutFunction> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShortcutFunction::getProductId, device.getProductId());
        wrapper.orderByDesc(ShortcutFunction::getCreateTime);
        List<ShortcutFunction> list = this.list(wrapper);
        if (list != null && !list.isEmpty()) {
            for (ShortcutFunction shortcutFunction : list) {
                ShortcutFunctionVo shortcutFunctionVo = ConvertUtil.convert(shortcutFunction, ShortcutFunctionVo.class);
                if ((CommonConstant.ZERO.toString()).equals(shortcutFunction.getConditionType())) {
                    Property property = iotThingModelService.getProperty(device.getProductId().toString(), shortcutFunction.getPropertyId());
                    List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList(ACCESSMODE));
                    Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
                    shortcutFunctionVo.setType(collect.get(ACCESSMODE).getNodes()
                            .stream()
                            .filter(i -> org.apache.commons.lang3.StringUtils.equals(i.getLabel(), property.getAccessMode() + ""))
                            .findFirst()
                            .orElse(new DictNodeBo())
                            .getDescription());
                } else if ((CommonConstant.ONE.toString()).equals(shortcutFunction.getConditionType())) {
                    Event event = iotThingModelService.getEvent(device.getProductId().toString(), shortcutFunction.getPropertyId());
                    shortcutFunctionVo.setType(EventEnum.getLabelByValue(event.getType()));
                }
                MultiLanguageBo nameBo = remoteMultiLanguageService.getById(shortcutFunction.getMultiLanguageId());
                MultiLanguageVo nameVo = new MultiLanguageVo(nameBo.getLangId(),
                        com.chervon.technology.config.MultiLanguageUtil.getByLangCode(nameBo.getLangCode(), LocaleContextHolder.getLocale().getLanguage()));
                shortcutFunctionVo.setFunctionName(nameVo);
                voList.add(shortcutFunctionVo);
            }
        }
        return voList;
    }
}
