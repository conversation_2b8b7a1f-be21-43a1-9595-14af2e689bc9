package com.chervon.technology.service.fault_message.impl;

import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.app.api.RemoteAppDeviceService;
import com.chervon.iot.app.api.dto.AppUserDeviceDTO;
import com.chervon.iot.app.api.vo.TargetUserVo;
import com.chervon.iot.middle.api.service.RemoteIotDataService;
import com.chervon.operation.api.RemoteMessageTemplateService;
import com.chervon.operation.api.vo.MessageTemplateBo;
import com.chervon.technology.domain.dataobject.Device;
import com.chervon.technology.service.fault_message.MessageTemplateHandler;
import com.chervon.technology.service.fault_message.entity.FaultContextAttributes;
import com.chervon.technology.service.impl.AwsMapServiceImpl;
import com.chervon.usercenter.api.vo.AppUserVo;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 消息模板解析处理：
 * EMERGENCY! {0}’s {1} has rolled over at {2} Street in {3} city  at (40.6892° N, 74.0445° W) right now.  Please contact him/her immediately!
 * 占位符号内容：
 * 0: accountName firstName+lastName
 * 1: Device name nickName/deviceName
 * 2: Street
 * 3: city
 * 4: coordinate
 * 61001翻车报警配置特殊模板
 * <AUTHOR> 2024/7/22
 */
@Service
public class MessageTemplateHandlerImpl implements MessageTemplateHandler {
    @DubboReference
    private RemoteMessageTemplateService remoteMessageTemplateService;
    @DubboReference
    private RemoteIotDataService remoteIotDataService;
    @DubboReference
    RemoteAppDeviceService remoteAppDeviceService;
    @Autowired
    private AwsMapServiceImpl awsMapService;
    private String field="idf_1036";//轨迹坐标字段
    private static Pattern pattern = Pattern.compile("\\{\\d+\\}");

    @Override
    public String getMessageTemplate() {
        return "EMERGENCY";
    }

    public MessageTemplateBo getPushMessageTemplate(HashMap<String,Object> faultContext, String language, Long templateId, Long userId) {
        // 推送消息标题、内容(多语言语种从user_setting表中language字段获取)
        MessageTemplateBo messageTemplateBo = remoteMessageTemplateService.get(language, templateId);
        if (Objects.isNull(messageTemplateBo)) {
            throw new ServiceException(ErrorCode.DATA_NOT_FOUND, "messageTemplateId:" + templateId);
        }
        if(Objects.isNull(userId) || userId.equals(0L)){
            return messageTemplateBo;
        }
        Map<String, AppUserVo> appUserVoMap = (Map<String, AppUserVo>) faultContext.get(FaultContextAttributes.AppBindUser.getCode());

        AppUserVo targetUserVo = appUserVoMap.get(userId.toString());
        if(Objects.isNull(targetUserVo)){
            return null;
        }

        final Device device = (Device) faultContext.get(FaultContextAttributes.DeviceInfo.getCode());
        if (Objects.isNull(messageTemplateBo.getContent()) || StringUtils.isEmpty(messageTemplateBo.getContent().getMessage())) {
            throw new ServiceException(ErrorCode.DATA_NOT_FOUND, "messageTemplateContent:" + templateId);
        }
        Matcher matcher = pattern.matcher(messageTemplateBo.getContent().getMessage());
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        if (count != 5) {
            return messageTemplateBo;
        }
        String deviceName = device.getDeviceName();
        final List<AppUserDeviceDTO> appUserDeviceInfo = remoteAppDeviceService.getAppUserDeviceInfoByDeviceId(device.getDeviceId(), userId);
        if(!CollectionUtils.isEmpty(appUserDeviceInfo) && !StringUtils.isEmpty(appUserDeviceInfo.get(0).getDeviceNickName())){
            deviceName=appUserDeviceInfo.get(0).getDeviceNickName();
        }
//        String messageTitle = messageTemplateBo.getTitle().getMessage();
        String messageContent = messageTemplateBo.getContent().getMessage();
        String condition= " idf_1036 is not null and idf_1036 like '%,%' and length(idf_1036)>15";
        final Map<String, Object> latestColumnLog = remoteIotDataService.getLatestColumnLog(device.getProductId().toString(), device.getDeviceId(), field,condition );
        if (!CollectionUtils.isEmpty(latestColumnLog)) {
            //118.802170,31.859940
            final String coordinate = latestColumnLog.get(field).toString();
            if (coordinate.contains(",")) {
                String longitude = coordinate.split(",")[0];
                String latitude = coordinate.split(",")[1];
                String coordinateLocation = MessageFormat.format("({0}° N, {1}° W)",longitude,latitude); //"(40.6892° N, 74.0445° W)";
                final Map<String, Object> placeInfo = awsMapService.getAddressByPosition(longitude, latitude);
                String city = (String) placeInfo.get("Municipality");//city
                String street = (String) placeInfo.get("Label");//street
                final String formatContent = MessageFormat.format(messageContent, targetUserVo.getName(), deviceName, street, city, coordinateLocation);
                messageTemplateBo.getContent().setMessage(formatContent);
            }
        }
        return messageTemplateBo;
    }
}
