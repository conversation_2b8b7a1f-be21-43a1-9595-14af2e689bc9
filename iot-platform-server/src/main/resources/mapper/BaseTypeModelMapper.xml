<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.technology.mapper.BaseTypeModelMapper">


    <select id="selectByCode" resultType="com.chervon.technology.entity.BaseTypeModel">
        SELECT t1.*
        FROM base_type_model as t1
                 left join base_type as t2 on t1.base_type_id = t2.id
        where t1.is_deleted = 0
          and t2.is_deleted = 0
          and t1.code = #{modelCode}
          and t2.code = #{categoryCode}
    </select>
</mapper>
