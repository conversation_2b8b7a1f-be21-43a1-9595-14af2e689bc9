<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.technology.mapper.OtaJobGroupMapper">

  <select id="getReadyJobIds" resultType="java.lang.String">
    select OJG.job_id, OJG.create_time from ota_job_group OJG
    left join ota_job OJ on OJ.id = OJG.job_id
    where OJG.group_name in
    <foreach collection="groupNames" item="groupName" index="index" open="(" close=")" separator=",">
      #{groupName}
    </foreach> and (OJ.release_status = 'RELEASED' or (OJ.release_status = 'TESTING' and OJG.group_type = 0))
    and not exists (SELECT id from ota_device_result where device_id = #{deviceId} and job_id = OJ.id
    and status = 'SUCCEEDED')
    and exists (SELECT id from device where device_id = #{deviceId} and product_id = OJ.product_id)
    and OJG.is_deleted = 0 and OJ.is_deleted = 0
    group by OJG.job_id order by OJ.release_status,OJ.create_time desc
  </select>
  <select id="getJobIdsByGroupName" resultType="java.lang.Long">
    select distinct job_id from ota_job_group where group_name = #{groupName} and is_deleted = 0
  </select>
</mapper>
