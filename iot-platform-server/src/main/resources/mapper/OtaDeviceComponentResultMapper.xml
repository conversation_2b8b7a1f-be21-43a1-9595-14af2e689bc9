<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.technology.mapper.OtaDeviceComponentResultMapper">
  <select id="getDeviceJobResult"
    resultType="com.chervon.technology.api.vo.ota.DeviceJobResultVo">
      SELECT sum(packageCount) as packageCount, sum(currentDownload) currentDownload,
      sum(currentUpgrade) currentUpgrade,
      sum(downloadProgress) downloadProgress, sum(upgradeProgress) upgradeProgress from (
      select count(0) as packageCount, 0 as currentDownload, 0 as currentUpgrade,
      0 as downloadProgress, 0 as upgradeProgress  from ota_device_component_result where
      job_id=#{deviceJobResultDto.jobId} and device_id=#{deviceJobResultDto.deviceId}
      UNION all
      select 0 as packageCount, count(0) as currentDownload, 0 as currentUpgrade,
      0 as downloadProgress, 0 as upgradeProgress from ota_device_component_result where
      job_id=#{deviceJobResultDto.jobId} and device_id=#{deviceJobResultDto.deviceId}	and status = 1
      UNION all
      select 0 as packageCount, 0 as currentDownload, count(0) as currentUpgrade,
       0 as downloadProgress, 0 as upgradeProgress from ota_device_component_result where
       job_id=#{deviceJobResultDto.jobId} and device_id=#{deviceJobResultDto.deviceId}	and status = 2
      UNION all
      select  0 as packageCount, 0 as currentDownload, 0 as currentUpgrade,
      IFNULL(sum(detail),0) as downloadProgress, 0 as upgradeProgress from ota_device_component_result
      where job_id=#{deviceJobResultDto.jobId} and device_id=#{deviceJobResultDto.deviceId}	and status = 1
      UNION all
      select  0 as packageCount, 0 as currentDownload, 0 as currentUpgrade,
       0 as downloadProgress,IFNULL(sum(detail),0) as upgradeProgress from ota_device_component_result
       where job_id=#{deviceJobResultDto.jobId} and device_id=#{deviceJobResultDto.deviceId}	and status = 2) aaa;
    </select>
</mapper>
