<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.technology.mapper.RuleEngineMapper">

    <select id="ruleEngineFaultMessageList" parameterType="com.chervon.technology.domain.dto.rule.engine.RuleEngineFaultMessageDto"
            resultType="com.chervon.technology.domain.vo.rule.engine.RuleEngineFaultMessageVo" flushCache="true">
        SELECT
        b.product_id as productId,
        a.fault_message_id as faultMessageId
        FROM
        rule_engine_fault_message a,
        fault_message b
        WHERE
        a.fault_message_id = b.id
        AND a.is_deleted = 0
        AND b.is_deleted =0
        <if test="ruleEngineId != '' and ruleEngineId != null">
           and a.rule_engine_id=#{ruleEngineId}
        </if>
        <if test="productIds != null and productIds.size() > 0">
            and b.product_id in
            <foreach collection="productIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="notProductIds != null and notProductIds.size() > 0">
            and b.product_id not in
            <foreach collection="notProductIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY b.product_id,a.fault_message_id
    </select>
</mapper>