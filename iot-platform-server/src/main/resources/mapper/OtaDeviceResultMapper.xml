<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.technology.mapper.OtaDeviceResultMapper">
  <resultMap id="ReleaseDetailMap" type="com.chervon.technology.api.vo.ota.OtaResultItemVo">
    <id column="device_id" jdbcType="VARCHAR" property="deviceId"/>
    <id column="device_name" jdbcType="VARCHAR" property="deviceName"/>
    <id column="group_name" jdbcType="VARCHAR" property="groupName"/>
    <id column="group_id" jdbcType="VARCHAR" property="groupId"/>
    <id column="job_Id" jdbcType="VARCHAR"/>
    <!--<collection property="componentResults" column="{deviceId=device_id, jobId=job_Id}"-->
    <!--ofType="com.chervon.technology.api.vo.ota.ComponentResultVo"-->
    <!--select="getComponentResults">-->
    <!--</collection>-->
  </resultMap>

  <select id="getOtaHistory" resultType="com.chervon.technology.api.vo.ota.OtaHistoryVo">
    select old_version, new_version, content, update_time upgradeTime from ota_device_result
    where status = 'SUCCEEDED' and device_id = #{deviceId} and app_user_id = #{userId} order by create_time desc
  </select>

  <select id="pageOtaResult" resultMap="ReleaseDetailMap">
    select ODR.device_id, D.device_name, ODR.group_name, ODR.job_id from ota_device_result ODR
    left join device D on D.device_id = ODR.device_id
    left join ota_device_component_result ODC on ODR.device_id = ODC.device_id and  ODR.job_id = ODC.job_id
    <where>
      ODR.job_id = #{otaResultDto.jobId}
      <if test="otaResultDto.deviceId != null and otaResultDto.deviceId != ''">
        and ODR.device_id like concat('%',#{otaResultDto.deviceId}, '%')
      </if>
      <if test="otaResultDto.groupName != null and otaResultDto.groupName != ''">
        and ODR.group_name like concat('%',#{otaResultDto.groupName}, '%')
      </if>
      <if test="otaResultDto.deviceName != null and otaResultDto.deviceName != ''">
        and D.device_name like concat('%',#{otaResultDto.deviceName}, '%')
      </if>
      <include refid="resultFilter"></include>
      and ODR.is_deleted = 0
      and D.is_deleted=0
      and ODC.is_deleted=0
      GROUP BY ODR.device_id
    </where>
  </select>

  <sql id="resultFilter">
    <if test="otaResultDto.statusInt != null">
      and ODC.status = #{otaResultDto.statusInt}
    </if>
    <if test="otaResultDto.componentNo != null and otaResultDto.componentNo != ''">
      and ODC.component_no like concat('%',#{otaResultDto.componentNo},'%')
    </if>
    <if test="otaResultDto.componentName != null and otaResultDto.componentName != ''">
      and ODC.component_name like concat('%',#{otaResultDto.componentName},'%')
    </if>
    <if test="otaResultDto.oldVersion != null and otaResultDto.oldVersion != ''">
      and ODC.old_version like concat('%',#{otaResultDto.oldVersion},'%')
    </if>
    <if test="otaResultDto.newVersion != null and otaResultDto.newVersion != ''">
      and ODC.new_version like concat('%',#{otaResultDto.newVersion},'%')
    </if>
    <if test="otaResultDto.resultStartTime != null and otaResultDto.resultStartTime != ''">
      and ODC.update_time &gt;= #{otaResultDto.resultStartTime}
    </if>
    <if test="otaResultDto.resultEndTime != null and otaResultDto.resultEndTime != ''">
      and ODC.update_time &lt;= #{otaResultDto.resultEndTime}
    </if>
  </sql>

  <select id="getComponentResults"
    resultType="com.chervon.technology.api.vo.ota.ComponentResultVo">
    select ODC.device_Id,ODC.component_no, ODC.component_name, ODC.old_version, ODC.new_version,
    CASE ODC.status
         WHEN 0 THEN 'WAITING'
         WHEN 1 THEN 'DOWNLOADING'
         WHEN 2 THEN 'UPGRADING'
         WHEN 3 THEN 'SUCCEED'
         WHEN 4 THEN 'FAILED'
    ELSE 'unknown' END as status,ODC.new_version,
    ODC.update_time as resultTime
    from ota_device_component_result ODC
    <where>
      ODC.job_id = #{otaResultDto.jobId}
      <if test="otaResultDto.deviceId != null">
        and ODC.device_id in
        <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
          #{deviceId}
        </foreach>
      </if>
      <include refid="resultFilter"></include>
      and ODC.is_deleted = 0
    </where>
  </select>
</mapper>
