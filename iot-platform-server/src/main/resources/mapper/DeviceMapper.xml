<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.technology.mapper.DeviceMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.chervon.technology.domain.dataobject.Device" id="deviceMap">
        <result property="id" column="id"/>
        <result property="productId" column="product_id"/>
        <result property="deviceId" column="device_id"/>
        <result property="sn" column="sn"/>
        <result property="deviceName" column="device_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="isOnline" column="is_online"/>
        <result property="activationUserId" column="activation_user_id"/>
        <result property="activationTime" column="activation_time"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="status" column="status"/>
        <result property="deviceIcon" column="device_icon"/>
        <result property="usageStatus" column="usage_status"/>
        <result property="configurationTable" column="configuration_table"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isReal" column="is_real"/>
    </resultMap>

    <select id="getDevicePage" resultType="com.chervon.technology.domain.vo.device.DeviceListVo"
            parameterType="com.chervon.technology.domain.dto.device.SearchDeviceDto">
        select d.device_id as deviceId,
        d.sn as sn,
        d.product_id as pid,
        p.category_id as categoryName,
        p.brand_id as brandName,
        p.model as productModel,
        p.commodity_model as commodityModel,
        p.product_type as productType,
        d.is_online as isOnline,
        d.status as status,
        d.usage_status as usageStatus,
        d.activation_time as activationTime,
        d.last_login_time as lastLoginTime
        from device as d
        left join device_user_ref as duf on d.device_id = duf.device_id and duf.is_deleted = 0
        left join product as p on d.product_id = p.id and p.is_deleted = 0
        <where>
            d.is_deleted = 0
            <if test="param2.deviceId != null and param2.deviceId != ''">
                and d.device_id like concat('%', #{param2.deviceId}, '%')
            </if>
            <if test="param2.sn != null and param2.sn != ''">
                and d.sn like concat('%', #{param2.sn}, '%')
            </if>
            <if test="param2.pid != null and param2.pid != ''">
                and d.product_id like concat('%', #{param2.pid}, '%')
            </if>
            <if test="param2.categoryId != null">
                and p.category_id = #{param2.categoryId}
            </if>
            <if test="param2.brandId != null">
                and p.brand_id = #{param2.brandId}
            </if>
            <if test="param2.productModel != null and param2.productModel != ''">
                and p.model like concat('%', #{param2.productModel}, '%')
            </if>
            <if test="param2.commodityModel != null and param2.commodityModel != ''">
                and p.commodity_model like concat('%', #{param2.commodityModel}, '%')
            </if>
            <if test="param2.productType != null and param2.productType != ''">
                and p.product_type = #{param2.productType}
            </if>
            <if test="param2.isOnline != null">
                and d.is_online = #{param2.isOnline}
            </if>
            <if test="param2.status != null">
                and d.status = #{param2.status.value}
            </if>
            <if test="param2.usageStatus != null">
                and d.usage_status = #{param2.usageStatus.value}
            </if>
            <if test="param2.activationStartTime != null and param2.activationStartTime != ''">
                and d.activation_time &gt;= #{param2.activationStartTime}
            </if>
            <if test="param2.activationEndTime != null and param2.activationEndTime != ''">
                and d.activation_time &lt;= #{param2.activationEndTime}
            </if>
            <if test="param2.lastLoginStartTime != null and param2.lastLoginStartTime != ''">
                and d.last_login_time &gt;= #{param2.lastLoginStartTime}
            </if>
            <if test="param2.lastLoginEndTime != null and param2.lastLoginEndTime != ''">
                and d.last_login_time &lt;= #{param2.lastLoginEndTime}
            </if>
        </where>
        order by d.create_time desc
    </select>

    <select id="getDeviceList" resultType="com.chervon.technology.domain.vo.device.DeviceListVo"
            parameterType="com.chervon.technology.domain.dto.device.SearchDeviceDto">
        select d.device_id as deviceId,
        d.sn as sn,
        d.product_id as pid,
        p.category_id as categoryName,
        p.brand_id as brandName,
        p.model as productModel,
        p.commodity_model as commodityModel,
        p.product_type as productType,
        d.is_online as isOnline,
        d.status as status,
        d.usage_status as usageStatus,
        d.activation_time as activationTime,
        d.last_login_time as lastLoginTime
        from device as d
        left join device_user_ref as duf on d.device_id = duf.device_id  and duf.is_deleted = 0
        left join product as p on d.product_id = p.id and p.is_deleted = 0
        <where>
            d.is_deleted = 0
            <if test="searchDevice.deviceId != null and searchDevice.deviceId != ''">
                and d.device_id like concat('%', #{searchDevice.deviceId}, '%')
            </if>
            <if test="searchDevice.sn != null and searchDevice.sn != ''">
                and d.sn like concat('%', #{searchDevice.sn}, '%')
            </if>
            <if test="searchDevice.pid != null and searchDevice.pid != ''">
                and d.product_id like concat('%', #{searchDevice.pid}, '%')
            </if>
            <if test="searchDevice.categoryId != null">
                and p.category_id = #{searchDevice.categoryId}
            </if>
            <if test="searchDevice.brandId != null">
                and p.brand_id = #{searchDevice.brandId}
            </if>
            <if test="searchDevice.productModel != null and searchDevice.productModel != ''">
                and p.model like concat('%', #{searchDevice.productModel}, '%')
            </if>
            <if test="searchDevice.commodityModel != null and searchDevice.commodityModel != ''">
                and p.commodity_model like concat('%', #{searchDevice.commodityModel}, '%')
            </if>
            <if test="searchDevice.productType != null and searchDevice.productType != ''">
                and p.product_type = #{searchDevice.productType}
            </if>
            <if test="searchDevice.isOnline != null">
                and d.is_online = #{searchDevice.isOnline}
            </if>
            <if test="searchDevice.status != null">
                and d.status = #{searchDevice.status.value}
            </if>
            <if test="searchDevice.usageStatus != null">
                and d.usage_status = #{searchDevice.usageStatus.value}
            </if>
            <if test="searchDevice.activationStartTime != null and searchDevice.activationStartTime != ''">
                and d.activation_time &gt;= #{searchDevice.activationStartTime}
            </if>
            <if test="searchDevice.activationEndTime != null and searchDevice.activationEndTime != ''">
                and d.activation_time &lt;= #{searchDevice.activationEndTime}
            </if>
            <if test="searchDevice.lastLoginStartTime != null and searchDevice.lastLoginStartTime != ''">
                and d.last_login_time &gt;= #{searchDevice.lastLoginStartTime}
            </if>
            <if test="searchDevice.lastLoginEndTime != null and searchDevice.lastLoginEndTime != ''">
                and d.last_login_time &lt;= #{searchDevice.lastLoginEndTime}
            </if>
        </where>
        order by d.create_time desc
    </select>
    <select id="getDeviceDetail" resultType="com.chervon.technology.domain.vo.device.DeviceDetailVo">
        select d.device_id           as deviceId,
               d.sn                  as sn,
               d.product_id          as pid,
               d.nick_name           as nickName,
               p.model               as productModel,
               p.commodity_model     as commodityModel,
               p.product_type        as productType,
               p.product_sn_code     as productSnCode,
               d.is_online           as isOnline,
               d.status              as status,
               d.usage_status        as usageStatus,
               d.activation_user_id  as activationUserId,
               d.activation_time     as activationTime,
               d.last_login_time     as lastLoginTime,
               d.configuration_table as configurationTable,
               p.brand_id            as brandName,
               p.category_id         as categoryName,
               d.technology_version  as technologyVersion,
               d.custom_version      as customVersion
        from iot_platform.device as d
                 join iot_platform.product as p on d.product_id = p.id
        where d.is_deleted = 0 and p.is_deleted = 0
          and d.device_id = #{deviceId}
    </select>

    <select id="getDeviceRpcVoList" resultType="com.chervon.technology.api.vo.DeviceRpcVo">
        select d.product_id as productId,
        d.device_id as deviceId,
        d.sn as sn,
        d.device_name as deviceName,
        d.nick_name as nickName,
        d.custom_version as version,
        d.is_online as isOnline,
        d.status as status,
        p.product_icon as deviceIcon,
        p.icon_type as iconType,
        p.product_type as productType,
        p.commodity_model as commodityModel,
        d.activation_time     as activationTime,
        p.model as model,
        p.category_id as categoryId
        from iot_platform.device as d
        join iot_platform.product as p on d.product_id = p.id
        <where>
            d.is_deleted = 0 and
            p.is_deleted = 0
            and d.device_id in
            <foreach collection="deviceIds" item="index" index="index" open="(" separator="," close=")">
                #{index}
            </foreach>
        </where>
    </select>

    <select id="getSimpleDeviceRpcVoList" resultType="com.chervon.technology.api.vo.DeviceRpcVo">
        select t1.product_id as productId,t1.device_id as deviceId,t2.product_type as productType,t1.activation_time
        ,t1.sn as sn,t2.commodity_model as commodityModel,t2.product_icon as deviceIcon
        from iot_platform.device as t1
        join iot_platform.product as t2 on t1.product_id = t2.id
        <where>
            t1.is_deleted = 0 and t2.is_deleted = 0
            and t1.device_id in
            <foreach collection="deviceIds" item="index" index="index" open="(" separator="," close=")">
                #{index}
            </foreach>
        </where>
    </select>

    <select id="getDeviceNameAndProductTypeByDeviceIds"
            resultType="com.chervon.iot.middle.api.vo.device.DeviceTopologyVo">
        select d.device_id as deviceId,
        d.nick_name as deviceName,
        p.product_type as deviceType
        from iot_platform.device as d
        join iot_platform.product as p on d.product_id = p.id
        <where>
            d.is_deleted = 0
            and d.device_id in
            <foreach collection="deviceIds" item="index" index="index" open="(" separator="," close=")">
                #{index}
            </foreach>
        </where>
    </select>

    <select id="listMapByIds" resultType="java.util.Map">
        select D.device_id as deviceId, P.model as productModel, D.is_online as isOnline
        from device D left join product P on D.product_id = P.id
        <where>
            D.device_id in
            <foreach collection="deviceIds" item="index" index="index" open="(" separator="," close=")">
                #{index}
            </foreach>
        </where>
    </select>

    <select id="selectListDeviceIdByProductInfo" resultType="java.lang.String">
        select t1.device_id from device as t1
        left join product as t2 on t1.product_id = t2.id
        where t1.is_deleted = 0 and t2.is_deleted = 0
        <if test="bindDeviceId != null and bindDeviceId !=''">
            and t1.device_id like concat('%', #{bindDeviceId}, '%')
        </if>
        <if test="bindDeviceSn != null and bindDeviceSn !=''">
            and t1.sn like concat('%', #{bindDeviceSn}, '%')
        </if>
        <if test="bindDeviceCategoryId != null">
            and t2.category_id = #{bindDeviceCategoryId}
        </if>
        <if test="bindDeviceBrandId != null">
            and t2.brand_id = #{bindDeviceBrandId}
        </if>
        <if test="model != null and model !=''">
            and t2.model like concat('%', #{model}, '%')
        </if>
        <if test="commodityModel != null and commodityModel !=''">
            and t2.commodity_model like concat('%', #{commodityModel}, '%')
        </if>
    </select>
    <select id="selectListDeviceIdByProductId" resultType="java.lang.String">
        select t1.device_id from device as t1
        where t1.is_deleted = 0
        <if test="productId != null">
            and t1.product_id = #{productId}
        </if>
    </select>
    <select id="selectListDeviceProductByDeviceIds" resultType="com.chervon.technology.api.dto.DeviceProductDto">
        select
        t1.device_id as bindDeviceId,
        t1.sn as bindDeviceSn,
        t2.category_id as bindDeviceCategoryId,
        t2.brand_id as bindDeviceBrandId,
        t2.model as model,
        t2.commodity_model as commodityModel
        from device as t1
        left join product as t2 on t1.product_id = t2.id
        where t1.is_deleted = 0 and t2.is_deleted = 0
        and t1.device_id in
        <foreach collection="deviceIds" item="index" index="index" open="(" separator="," close=")">
            #{index}
        </foreach>
    </select>

    <update id="fixMissingDeviceSnFromDeviceCode">
        update
            iot_platform.device inner join device_code on device.device_id=device_code.device_id
            set device.sn=device_code.sn
        where device.sn is null
    </update>



</mapper>
