<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.technology.mapper.TriggerMapper">
    <select id="listByFaultMessageId" resultType="com.chervon.technology.domain.dataobject.RuleTrigger">
        SELECT * from rule_trigger R
        LEFT JOIN fault_message_trigger MT on MT.trigger_id = R.id
        WHERE R.group_id = #{groupId} AND MT.fault_message_id = #{faultMessageId}
    </select>
</mapper>