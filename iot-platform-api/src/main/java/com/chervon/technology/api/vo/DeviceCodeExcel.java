package com.chervon.technology.api.vo;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 设备多码表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-07
 */
@Data
public class DeviceCodeExcel implements Serializable {

    @Alias("设备ID")
    private String deviceId;

    @Alias("设备SN")
    private String sn;

    @Alias("MO code")
    private String moCode;

    @Alias("MES #")
    private String mes;

    @Alias("ITEM code")
    private String itemCode;

    @Alias("Production Date")
    private String productionDate;

    @Alias("状态")
    private String status;

    @Alias("创建人")
    private String createBy;

    @Alias("创建时间")
    private String createTime;

    @Alias("修改人")
    private String updateBy;

    @Alias("修改时间")
    private String updateTime;

    public String getDeviceId() {
        return CsvUtil.format(this.deviceId);
    }

    public String getSn() {
        return CsvUtil.format(this.sn);
    }

    public String getMoCode() {
        return CsvUtil.format(this.moCode);
    }

    public String getMes() {
        return CsvUtil.format(this.mes);
    }

    public String getItemCode() {
        return CsvUtil.format(this.itemCode);
    }

    public String getProductionDate() {
        return CsvUtil.format(this.productionDate);
    }

    public String getStatus() {
        return CsvUtil.format(this.status);
    }

    public String getCreateBy() {
        return CsvUtil.format(this.createBy);
    }

    public String getCreateTime() {
        return CsvUtil.format(this.createTime);
    }

    public String getUpdateBy() {
        return CsvUtil.format(this.updateBy);
    }

    public String getUpdateTime() {
        return CsvUtil.format(this.updateTime);
    }
}
