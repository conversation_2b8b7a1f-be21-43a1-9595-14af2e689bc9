package com.chervon.technology.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 设备注册Dto
 *
 * <AUTHOR>
 */
@Data
public class DeviceRegisterDto implements Serializable {

    /**
     * 烧录在设备中的id信息
     */
    private String deviceId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 设备注册昵称
     */
    private String deviceNickName;

    /**
     * 设备sn（非IOT设备添加是注册设备使用）
     */
    private String sn;
}
