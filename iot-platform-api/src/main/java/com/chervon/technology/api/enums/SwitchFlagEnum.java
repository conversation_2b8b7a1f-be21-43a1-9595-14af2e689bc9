package com.chervon.technology.api.enums;

import com.chervon.common.core.enums.TypeEnum;

import java.util.Arrays;

/**
 * 开关枚举：1打开  0关闭
 */
public enum SwitchFlagEnum implements TypeEnum {
    /**
     * 1：打开状态
     */
    OPEN(1, "打开状态"),
    /**
     * 0:关闭状态
     */
    CLOSE(0, "关闭状态"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    /**
     * 业务类型构造函数
     * @param type 类型id
     * @param desc 描述
     */
    SwitchFlagEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }
    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    public String getStrType(){
        return String.valueOf(type);
    }

    /**
     * 获取枚举类型值
     * @param value 类型值
     * @return 返回值
     */
    public static String getDesc(int value) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == value)
                .map(SwitchFlagEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static SwitchFlagEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
