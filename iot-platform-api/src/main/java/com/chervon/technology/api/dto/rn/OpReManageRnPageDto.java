package com.chervon.technology.api.dto.rn;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/8 17:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "RN发布管理的分页查询条件")
public class OpReManageRnPageDto extends PageRequest implements Serializable {

    @ApiModelProperty(value = "产品id")
    private String productId;

    @ApiModelProperty(value = "品类id")
    private Long categoryId;

    @ApiModelProperty(value = "产品型号")
    private String model;

//    @ApiModelProperty(value = "品牌Id")
//    private Long brandId;
//
//    @ApiModelProperty("商品型号Model#")
//    private String commodityModel;

    @ApiModelProperty(value = "rn名称")
    private String rnName;

    @ApiModelProperty(value = "rn版本")
    private String rnVersion;

    @ApiModelProperty(value = "应用Id")
    private String appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

//    @ApiModelProperty(value = "申请人id")
//    private Long applicant;

    @ApiModelProperty(value = "rn包发布状态")
    private String releaseStatusCode;
    @ApiModelProperty(value = "安卓最低兼容版本号")
    private String androidVersionMin;
    @ApiModelProperty(value = "IOS最低兼容版本号")
    private String iosVersionMin;
    @ApiModelProperty(value = "申请人")
    private String applyBy;
    @ApiModelProperty(value = "审批人")
    private String approveBy;
}
