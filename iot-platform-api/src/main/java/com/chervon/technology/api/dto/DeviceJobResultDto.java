package com.chervon.technology.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @className DeviceJobResultVo
 * @description
 * @date 2022/7/13 11:52
 */
@Data
@ApiModel("设备升级结果")
public class DeviceJobResultDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务id
     **/
    @ApiModelProperty("任务id")
    @NotEmpty
    private List<Long> jobId;

    /**
     * 任务id
     **/
    @ApiModelProperty("设备id")
    @NotBlank
    private String deviceId;
    /**
     * 任务id
     **/
    @ApiModelProperty("用户id-app不用传")
    private Long userId;

    /**
     * 操作的用户平台: 1 Ego Connect  2 Ego Fleet
     **/
    @ApiModelProperty("操作的用户平台: 1 Ego Connect  2 Ego Fleet")
    private Integer businessType;
}
