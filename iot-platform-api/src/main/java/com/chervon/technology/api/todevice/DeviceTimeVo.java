package com.chervon.technology.api.todevice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceTimeVo implements Serializable {


    /**
     * yyyy-MM-dd HH:mm:ss格式的时间
     */
    private String time;

    /**
     * 1 是夏令时，0非夏令时
     */
    private Boolean daylightTime;

    /**
     * 时间戳
     */
    private Long timeStamp;
}
