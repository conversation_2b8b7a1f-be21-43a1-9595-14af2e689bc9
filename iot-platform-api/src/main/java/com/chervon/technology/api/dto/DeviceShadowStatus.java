package com.chervon.technology.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/1 10:20
 */
@Data
@Accessors(chain = true)
public class DeviceShadowStatus implements Serializable {

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 设备运行时间，1016
     */
    private Integer usageTime1016;

    /**
     * 总耗电量，1025
     */
    private Integer powerConsumption1025;

    /**
     * 卷轮工作状态，4003
     */
    private Boolean workStatus4003;
}
