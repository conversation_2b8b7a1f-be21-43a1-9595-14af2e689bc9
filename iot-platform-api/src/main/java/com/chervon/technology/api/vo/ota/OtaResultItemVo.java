package com.chervon.technology.api.vo.ota;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @className OtaResultItemVo
 * @description
 * @date 2022/7/13 13:45
 */
@ApiModel("升级结果列表")
@Data
public class OtaResultItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备ID")
    private String deviceId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("分组ID")
    private String groupId;

    @ApiModelProperty("分组名称")
    private String groupName;

    @ApiModelProperty("总成升级结果")
    private List<ComponentResultVo> componentResults;
}
