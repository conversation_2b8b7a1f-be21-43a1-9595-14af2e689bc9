package com.chervon.technology.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/12 14:49
 */
@Data
@ApiModel(description = "用户产品权限操作对象")
public class SysUserProductConfigDto implements Serializable {

    @ApiModelProperty(value = "产品id")
    private Long productId;

    @ApiModelProperty(value = "选中的用户")
    private List<PsysUserDto> users;
}
