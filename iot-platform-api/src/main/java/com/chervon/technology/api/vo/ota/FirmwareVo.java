package com.chervon.technology.api.vo.ota;

import com.chervon.technology.api.enums.PackageTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 固件包vo
 * <AUTHOR>
 * @date 18:36 2022/7/29
 **/
@Data
public class FirmwareVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总成零件号
     **/
    @ApiModelProperty("总成零件号")
    private String componentNo;

    /**
     * 总成零件名称
     **/
    @ApiModelProperty("总成零件名称")
    private String componentName;

    /**
     * 总成零件类型，mcu：MCU，subDevice：子设备，bleModule：蓝牙模组，4gModule：4G
     **/
    @ApiModelProperty("总成零件类型，mcu：MCU，subDevice：子设备，bleModule：蓝牙模组，4gModule：4G")
    private String componentType;

    /**
     * 固件名称
     **/
    @ApiModelProperty("固件名称")
    private String packageName;

    /**
     * 固件版本号
     **/
    @ApiModelProperty("固件版本号")
    private String packageVersion;

    /**
     * 显示版本号，生成用户版本号使用
     **/
    @ApiModelProperty("显示版本号，生成用户版本号使用")
    private String displayVersion;

    /**
     * 文件大小 单位字节
     **/
    @ApiModelProperty("文件大小 单位字节")
    private Integer size;

    /**
     * 固件类型, FULL_PACKAGE:全包, DELTA_PACKAGE:差分包
     **/
    @ApiModelProperty("最低兼容的版本")
    private String minimumVersion;

    /**
     * 固件类型, FULL_PACKAGE:全包, DELTA_PACKAGE:差分包
     **/
    @ApiModelProperty("固件类型, FULL_PACKAGE:全包, DELTA_PACKAGE:差分包")
    private PackageTypeEnum packageType;

    /**
     * 固件类型, FULL_PACKAGE:全包, DELTA_PACKAGE:差分包
     **/
    @ApiModelProperty("固件包存放在s3中的key")
    private String key;

    /**
     * 升级包的hash值, sha1算法
     **/
    @ApiModelProperty("升级包的hash值, sha1算法")
    private String hash;

    /**
     * 排序编号
     **/
    @ApiModelProperty("排序编号")
    private Integer orderNum;
}
