package com.chervon.technology.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 产品在运营平台产品管理操作
 * <AUTHOR>
 * @date 2022-08-04
 */
@Data
public class ProductReleaseOperationVo implements Serializable {
    @ApiModelProperty("能否确认测试通过")
    private boolean canEnsureTest;
    @ApiModelProperty("能否驳回测试验证")
    private boolean canRefuseTest;
    @ApiModelProperty("能否查看测试验证被驳回原因")
    private boolean canViewRefuseTestReason;

    @ApiModelProperty("能否确认发布")
    private boolean canEnsureApply;
    @ApiModelProperty("能否驳回发布")
    private boolean canRefuseApply;
    @ApiModelProperty("能否查看发布被驳回原因")
    private boolean canViewRefuseReleaseReason;

    @ApiModelProperty("能否确认更新测试")
    private boolean canEnsureTestUpdate;
    @ApiModelProperty("能否驳回更新测试验证")
    private boolean canRefuseTestUpdate;
    @ApiModelProperty("能否查看更新测试验证被驳回原因")
    private boolean canViewRefuseUpdateTestReason;

    @ApiModelProperty("能否确认更新")
    private boolean canEnsureUpdate;
    @ApiModelProperty("能否驳回更新")
    private boolean canRefuseUpdate;
    @ApiModelProperty("能否查看更新被驳回原因")
    private boolean canViewRefuseUpdateReason;

    @ApiModelProperty("能否确认下架")
    private boolean canEnsureOff;
    @ApiModelProperty("能否驳回下架")
    private boolean canRefuseOff;
    @ApiModelProperty("能否查看下架申请被驳回原因")
    private boolean canViewRefuseOffReason;

}
