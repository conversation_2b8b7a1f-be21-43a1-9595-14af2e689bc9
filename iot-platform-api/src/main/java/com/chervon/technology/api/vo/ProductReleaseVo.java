package com.chervon.technology.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-08-01
 */
@Data
public class ProductReleaseVo extends ProductReleaseOperationVo implements Serializable {
    @ApiModelProperty("产品Id")
    private Long id;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("产品名称多语言Id")
    private String productNameId;

    @ApiModelProperty("产品图标")
    private String productIcon;

    @ApiModelProperty("产品snCode")
    private String productSnCode;

    @ApiModelProperty("产品model")
    private String model;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("商品型号Model#")
    private String commodityModel;

    @ApiModelProperty("品牌id")
    private Long brandId;

    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("品类Id")
    private Long categoryId;

    @ApiModelProperty("品类名称")
    private String categoryName;

    @ApiModelProperty("设备类型：设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，" +
            "gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    private String productType;

    @ApiModelProperty("产品发布状态:无状态 -, 待发布 to_be_release, 发布审核 release_approve, 已发布 released，" +
            "发布被驳回 release_refuse， ")
    private String releaseStatus;

    /**
     * 运营平台的备注
     */
    @ApiModelProperty("运营平台备注")
    private String operationRemark;

    @ApiModelProperty("审批人id")
    private String approveBy;

    @ApiModelProperty("申请人Id")
    private String applyBy;

    @ApiModelProperty("申请人用户信息")
    private String applyUserName;

    @ApiModelProperty("审批人用户信息")
    private String approveUserName;

    @ApiModelProperty("审批时间")
    private Date approveTime;

    @ApiModelProperty("申请时间")
    private Date applyTime;

}
