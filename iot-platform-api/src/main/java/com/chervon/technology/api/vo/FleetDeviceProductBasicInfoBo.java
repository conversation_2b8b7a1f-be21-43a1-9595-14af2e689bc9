package com.chervon.technology.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-17 17:52
 **/
@Data
@ApiModel("给二期提供的设备+产品基础信息的Bo")
public class FleetDeviceProductBasicInfoBo implements Serializable {
    @ApiModelProperty("设备id")
    private String deviceId;
    @ApiModelProperty("设备sn")
    private String deviceSn;
    @ApiModelProperty("设备名称")
    private String deviceName;
    @ApiModelProperty("产品id")
    private Long productId;
    @ApiModelProperty("产品图片")
    private String productIconUrl;
    @ApiModelProperty("产品商品型号")
    private String commodityModel;
    @ApiModelProperty("产品型号")
    private String productModel;
}
