package com.chervon.technology.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/27 16:47
 */
@Data
@ApiModel(description = "通用-产品对象")
public class CommonProductVo implements Serializable {

    private Long productId;

    @ApiModelProperty("品类名称")
    private String categoryName;

    private Long categoryId;

    @ApiModelProperty("产品型号")
    private String model;

    @ApiModelProperty("商品型号Model#")
    private String commodityModel;

    @ApiModelProperty("产品名称")
    private String productName;
}
