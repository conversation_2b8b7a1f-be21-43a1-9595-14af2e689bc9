package com.chervon.technology.api.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 设备短信息Vo
 * 用来在在Redis中根据userId存储用户绑定的设备列表信息
 *
 * <AUTHOR>
 * @since 2023-02-15 16:23
 **/
@Data
public class DeviceShortInfoVo implements Serializable {
    /**
     * 设备Id
     */
    private String deviceId;
    /**
     * sn
     */
    private String sn;
    /**
     * 产品Id
     */
    private String productId;
    /**
     * 产品类型
     * <p>
     * directConnectedDevice:直连设备,
     * gatewayDevice:网关设备,
     * gatewaySubDevice:网关子设备,
     * notIotDevice:非Iot设备,
     * oldIotDevice:老iot设备
     */
    private String productType;
    /**
     * 排序值(从大到小)
     */
    private Integer sort;
}
