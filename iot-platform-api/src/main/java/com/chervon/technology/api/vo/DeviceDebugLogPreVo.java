package com.chervon.technology.api.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022年12月29日
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeviceDebugLogPreVo implements Serializable {

	@ApiModelProperty(value = "设备id", required = true)
	@NotEmpty
	private String deviceId;

	@ApiModelProperty(value = "文件名称", required = true)
	@NotEmpty
	private String fileName;

	@ApiModelProperty(value = "分段上传任务标识id", required = true)
	@NotEmpty
	private String uploadId;


	@ApiModelProperty(value = "结果code-0:成功 1:失败", required = true)
	@NotNull
	private Integer resultCode;


	@ApiModelProperty(value = "结果返回的文件标识", required = true)
	@NotEmpty
	private String tagStr;


	@ApiModelProperty(value = "当前分段id", required = true)
	@NotNull
	private Integer partNum;

	@ApiModelProperty("errorMsg")
	private String errorMsg;


}
