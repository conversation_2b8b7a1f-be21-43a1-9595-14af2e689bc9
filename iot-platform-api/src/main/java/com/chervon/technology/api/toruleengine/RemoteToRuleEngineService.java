package com.chervon.technology.api.toruleengine;

import com.chervon.technology.api.toruleengine.FaultMessageAlarmLogAddDto;
import com.chervon.technology.api.toruleengine.CheckJobDto;
import com.chervon.technology.api.toruleengine.JobStatusRuleDto;
import com.chervon.technology.api.toruleengine.PackageResultDto;
import com.chervon.technology.api.toruleengine.DeviceIccidDto;
import com.chervon.technology.api.toruleengine.DeviceMapPathDto;
import com.chervon.technology.api.toruleengine.DeviceRPhoneDto;
import com.chervon.technology.api.toruleengine.IotUpdateOnlineStatusDto;
import com.chervon.technology.api.toruleengine.JobActionVo;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface RemoteToRuleEngineService {

    /**
     * 接收规则引擎目标地址确认token
     *
     * @param confirmationToken: 目标地址确认token
     * <AUTHOR>
     * @date 16:53 2022/5/16
     * @return: void
     **/
    void receiveConfirmationToken(String confirmationToken);

    /**
     * 接收规则引擎转发的设备连接状态更新
     * 对应的主题:$aws/events/presence/+/#
     * 规则引擎: *_online_state_update
     * eg:  $aws/events/presence/disconnected/XTR012283419494
     * $aws/events/presence/connected/XTR012283419494
     *
     * @param onlineStatusDto:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/

    void receiveOnlineStatus(IotUpdateOnlineStatusDto onlineStatusDto);

    /**
     * 获取固件包最新的下载链接请求
     * 对应的topic: aws/things/+/url/get
     * 规则引擎：*_url_get
     *
     * @param packageKeys:
     * @param topic:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    void getUrl(List<String> packageKeys, String topic) throws IOException;

    /**
     * 更新是否允许升级
     * 对应topic：aws/things/+/jobs/+/action'
     * 规则引擎：*_job_action
     *
     * @param topic:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    void reportJobAction(String topic, JobActionVo action);

    /**
     * 规则引擎回调
     *
     * @param ruleId 故障消息Id
     * @param dto    添加故障消息告警记录Dto
     */
    void reportRuleEngineAction(String ruleId, FaultMessageAlarmLogAddDto dto);

    /**
     * C充电桩充电提醒回调
     * 对应topic:$aws/things/+/shadow/update/accepted
     * 规则引擎: *_c_charging_completion_reminder
     *
     * @param dto 添加故障消息告警记录Dto
     */
    void reportChargerReminderAction(FaultMessageAlarmLogAddDto dto);

    /**
     * 获取是否允许升级
     * 对应的主题：aws/things/+/jobs/+/action/get
     * 规则引擎：*_job_action_get
     *
     * @param topic:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    void getJobAction(String topic) throws IOException;

    /**
     * 更新总成升级状态
     * 对应的主题：aws/things/+/jobs/+/update
     * 规则引擎：*_update_component_status
     *
     * @param topic:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    void updatePackageStatus(String topic,
                             List<PackageResultDto> resultDtos);

    /**
     * 更新路径点位数据
     * 对应的topic：topic: aws/things/{deviceId}/path
     * 规则引擎：*_map_path
     *
     * @param topic topic
     * @param req   请求参数
     */
    void updateMapPath(String topic,
                       DeviceMapPathDto req);

    /**
     * 上报更新总成版本号
     * 对应的topic：$aws/things/+/shadow/name/componentVersion/update/accepted
     * 规则引擎：*_update_component_version
     *
     * @param deviceId:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    void updatePackageVersion(String deviceId,
                              Map<String, String> versionMap);

    /**
     * 更新设备执行job状态
     * 对应的topic;'$aws/events/jobExecution/+/+'
     * 规则引擎： *_job_status
     *
     * @param jobStatusRuleDto:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    void reportJobStatus(JobStatusRuleDto jobStatusRuleDto);

    /**
     * 检查升级更新
     * 对应的topic：'aws/things/+/ota/check'
     * 规则引擎：*_ota_check_rule
     *
     * @param jsonObject:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    void otaCheck(CheckJobDto jsonObject) throws IOException;

    /**
     * 更新设备iccid信息
     * 对应的topic：topic: $aws/things/+/shadow/update/accepted
     * 规则引擎：*_iccid_callback
     *
     * @param topic topic
     * @param req   请求参数
     */
    void updateMapPath(String topic,
                       DeviceIccidDto req);

    /**
     * 更新R设备手机号信息
     * 对应的topic：topic: aws/things/r/{deviceId}/phone
     * 规则引擎：*_r_phone_callback
     *
     * @param topic topic
     * @param req   请求参数
     */
    void updateRPhone(String topic,
                      DeviceRPhoneDto req);
}
