package com.chervon.technology.api;

import com.chervon.technology.api.dto.DeviceProductDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/31 15:59
 */
public interface RemoteDeviceIdService {

    /**
     * 根据产品信息查询deviceId集合
     *
     * @param bindDeviceId         bindDeviceId
     * @param bindDeviceSn         已绑定设备SN
     * @param bindDeviceCategoryId 已绑定设备品类id
     * @param bindDeviceBrandId    已绑定设备品牌id
     * @param model                已绑定设备产品型号code
     * @param commodityModel       已绑定设备商品型号/Model #
     * @return deviceId集合
     */
    List<String> listDeviceIdByProductInfo(String bindDeviceId,
                                           String bindDeviceSn,
                                           Long bindDeviceCategoryId,
                                           Long bindDeviceBrandId,
                                           String model,
                                           String commodityModel);
    /**
     * 根据产品id获得deviceId集合
     * @param productId 产品id
     * @return deviceId集合
     */
    List<String> selectListDeviceIdByProductId(Long productId);

    /**
     * 根据设备id集合查询设备产品信息
     *
     * @param deviceIds 设备id集合
     * @return 设备产品信息集合
     */
    List<DeviceProductDto> listDeviceProductByDeviceIds(List<String> deviceIds);

}
