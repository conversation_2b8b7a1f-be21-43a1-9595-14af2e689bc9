package com.chervon.technology.api.vo.charging;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/2/25 14:18
 */
@Data
public class FlowChargingDetailExcel implements Serializable {

    @Alias("设备ID")
    private String deviceId;

    @Alias("SIM ID")
    private String iccid;

    @Alias("计费开始时间")
    private String chargingStartTime;

    @Alias("计费结束时间")
    private String chargingEndTime;

    @Alias("单台设备流量总计（MB）")
    private String totalFlow;

    public String getDeviceId() {
        return CsvUtil.format(this.deviceId);
    }

    public String getIccid() {
        return CsvUtil.format(this.iccid);
    }

    public String getChargingStartTime() {
        return CsvUtil.format(this.chargingStartTime);
    }

    public String getChargingEndTime() {
        return CsvUtil.format(this.chargingEndTime);
    }

    public String getTotalFlow() {
        return CsvUtil.format(this.totalFlow);
    }

}
