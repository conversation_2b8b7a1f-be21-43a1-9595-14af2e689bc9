package com.chervon.technology.api.toruleengine;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * <AUTHOR>
 * @className JobActionVo
 * @description
 * @date 2022/7/13 11:52
 */
@Data
@ApiModel("是否允许设备升级")
public class JobActionVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("消息唯一标识")
    private String messageId;

    @ApiModelProperty("是否允许设备升级消息时间（毫秒）")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime timestamp;

    /**
     * 是否允许设备升级
     **/
    @ApiModelProperty("是否允许设备升级")
    private Boolean allow;
}
