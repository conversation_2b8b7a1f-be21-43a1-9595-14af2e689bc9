package com.chervon.technology.api.vo.map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/15 20:37
 */
@Data
@ApiModel(description = "地图数据出参")
public class DeviceMapVo implements Serializable {

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "地图文件url")
    private String url;
}
