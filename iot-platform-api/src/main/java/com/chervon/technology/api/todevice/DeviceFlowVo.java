package com.chervon.technology.api.todevice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceFlowVo implements Serializable {

    /**
     * 流量卡类型: 0国内 1国际
     */
    private Integer type;

    /**
     * 国内：总共流量 国外：当月总流量
     */
    private String total;

    /**
     * 国内：总共使用流量 国外：当月总共使用流量
     */
    private String totalUsed;

    /**
     * 国内：剩余流量 国外：当月剩余流量
     */
    private String surplusFlow;
}
