package com.chervon.technology.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 告警消息推送结果统计Dto
 *
 * <AUTHOR>
 * @since 2022年12月22日
 **/
@Data
public class FaultMessageResultCountDto implements Serializable {

	@ApiModelProperty("系统消息、营销消息、告警消息")
	private String systemMessageId;

	@ApiModelProperty("更新方式：0累加更新  1覆盖更新")
	private Integer updateType=1;

	@ApiModelProperty("成功数量")
	private Integer successNum = 0;

	@ApiModelProperty("失败")
	private Integer failNum = 0;


	public void incrementSuccessNum() {
		successNum = successNum + 1;
	}

	public void incrementFailNum() {
		failNum = failNum + 1;
	}

}
