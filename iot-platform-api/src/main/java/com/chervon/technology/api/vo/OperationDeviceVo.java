package com.chervon.technology.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6 10:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OperationDeviceVo extends OperationDevicePageVo implements Serializable {

    @ApiModelProperty("设备昵称")
    private String nickName;

    @ApiModelProperty("所属产品SnCode")
    private String productSnCode;

    @ApiModelProperty("通讯方式，wifiAndBle：Wifi+BLE，4GAndBle：4G+BLE，ble：BLE，DOrT：D/T，wifi：WIFI，4G：4G，lan：LAN，notNetworked：不联网")
    private List<String> communicateMode;

    @ApiModelProperty("设备固件客户版本")
    private String customVersion;

    @ApiModelProperty("品类名称")
    private String categoryName;

    @ApiModelProperty("品牌名称")
    private String brandName;

}
