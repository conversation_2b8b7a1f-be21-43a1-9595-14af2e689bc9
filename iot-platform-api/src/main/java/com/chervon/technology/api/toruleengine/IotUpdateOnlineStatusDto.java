package com.chervon.technology.api.toruleengine;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * <AUTHOR>
 * @className IotUpdateOnlineStatusDto
 * @description
 * @date 2022/3/23 16:16
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class IotUpdateOnlineStatusDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private String clientId;

    private String status;

    private String timestamp;
}
