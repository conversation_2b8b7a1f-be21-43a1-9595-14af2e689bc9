package com.chervon.technology.api.vo.map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/15 20:37
 */
@Data
@ApiModel(description = "路径数据出参")
public class DevicePathNewVo implements Serializable {

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "区域id")
    private String areaId;

    @ApiModelProperty(value = "轨迹id")
    private String pathId;

    @ApiModelProperty(value = "t集合，每个数据去掉pathId和区域id，即前32位")
    private List<String> t;

}
