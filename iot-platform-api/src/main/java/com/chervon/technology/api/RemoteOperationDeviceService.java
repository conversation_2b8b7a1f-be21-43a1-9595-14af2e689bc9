package com.chervon.technology.api;

import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.dto.OperationComponentOtaPageDto;
import com.chervon.technology.api.dto.OperationComponentPageDto;
import com.chervon.technology.api.dto.OperationDevicePageDto;
import com.chervon.technology.api.enums.DeviceStatusEnum;
import com.chervon.technology.api.vo.*;

import java.util.List;

public interface RemoteOperationDeviceService {

    /**
     * 设备分页查询
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<OperationDevicePageVo> page(OperationDevicePageDto req);

    /**
     * 根据条件查询设备列表
     *
     * @param req 查询条件
     * @return 列表数据
     */
    List<OperationDeviceExcel> list(OperationDevicePageDto req);

    /**
     * 停用、启用设备
     *
     * @param deviceId 设备id
     * @param status   目标状态
     */
    void editStatus(String deviceId, DeviceStatusEnum status);

    /**
     * 根据设备id查询设备详情
     *
     * @param deviceId 设备id
     * @return 设备详情
     */
    OperationDeviceVo detail(String deviceId);

    /**
     * 通过设备Id获取总成零件分页
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<OperationComponentVo> componentPage(OperationComponentPageDto req);

    /**
     * 查看总成固件升级记录
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<OperationComponentOtaVo> componentOtaList(OperationComponentOtaPageDto req);
}
