package com.chervon.technology.api.exception;

import com.chervon.common.core.exception.base.BaseException;

/**
 * <AUTHOR>
 * @date 2022/6/26 22:06
 */
public class TechnologyException extends BaseException {

    private static final long serialVersionUID = 1L;

    private String tempMessage;

    public void setTempMessage(String tempMessage) {
        this.tempMessage = tempMessage;
    }

    public TechnologyException(String code, Object... args) {
        super("iot-platform", code, args);
    }

    public TechnologyException(TechnologyErrorCode code, Object... args) {
        super("iot-platform", code.getCode(), code.getDefaultMessage(), code.getErrorMessage(), args);
    }

    @Override
    public String getMessage() {
        if (tempMessage != null) {
            return this.tempMessage;
        }
        return this.getDefaultMessage() == null ? this.getCode() : String.format(this.getDefaultMessage(), this.getArgs());
    }

}
