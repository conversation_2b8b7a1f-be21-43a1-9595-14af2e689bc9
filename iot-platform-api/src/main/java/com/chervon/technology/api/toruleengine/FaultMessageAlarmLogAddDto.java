package com.chervon.technology.api.toruleengine;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-09-14 19:58
 **/
@Data
public class FaultMessageAlarmLogAddDto implements Serializable {
    /**
     * 故障消息Id(规则请求头中的ruleId)
     */
    @ApiModelProperty("故障消息Id(规则请求头中的ruleId)")
    private Long faultMessageId;
    /**
     * 或条件分组groupId(规则请求头中的groupId)
     */
    @ApiModelProperty("或条件分组groupId(规则请求头中的groupId)")
    private String groupId;
    /**
     * 设备Id
     */
    @ApiModelProperty("设备Id")
    private String deviceId;
    /**
     * 物模型功能id
     */
    @ApiModelProperty("物模型功能id")
    private String propertyId;
    /**
     * 参数
     * 事件类型功能的输出参数，一个触发器可能有多个参数
     */
    @ApiModelProperty("参数")
    private String parameter;
    /**
     * 功能名称多语言id
     */
    @ApiModelProperty("功能名称多语言id")
    private String multiLanguageId;
    /**
     * 触发告警物模型的值
     */
    @ApiModelProperty("触发告警物模型的值")
    private String value;
    /**
     * MQTT消息体
     */
    @ApiModelProperty("MQTT消息体")
    private Map<String, Object> mqttBody;

    private Integer currentRate;

    private Integer targetRate;
}
