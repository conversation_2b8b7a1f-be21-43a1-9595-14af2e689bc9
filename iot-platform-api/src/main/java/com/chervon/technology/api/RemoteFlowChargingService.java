package com.chervon.technology.api;

import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.core.BaseRemoteReqDto;
import com.chervon.technology.api.dto.charging.FlowChargingDetailPageDto;
import com.chervon.technology.api.dto.charging.FlowChargingPageDto;
import com.chervon.technology.api.vo.charging.FlowChargingDetailExcel;
import com.chervon.technology.api.vo.charging.FlowChargingDetailVo;
import com.chervon.technology.api.vo.charging.FlowChargingExcel;
import com.chervon.technology.api.vo.charging.FlowChargingVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/25 14:15
 */
public interface RemoteFlowChargingService {

    /**
     * 流量计费分页查询
     *
     * @param req 分页查询条件
     * @return 分页数据
     */
    PageResult<FlowChargingVo> flowChargingPage(BaseRemoteReqDto<FlowChargingPageDto> req);

    /**
     * 流量计费列表查询
     *
     * @param req 列表查询条件
     * @return 列表数据
     */
    List<FlowChargingExcel> flowChargingList(BaseRemoteReqDto<FlowChargingPageDto> req);

    /**
     * 流量计费详情分页查询
     *
     * @param req 分页查询条件
     * @return 分页数据
     */
    PageResult<FlowChargingDetailVo> flowChargingDetailPage(BaseRemoteReqDto<FlowChargingDetailPageDto> req);

    /**
     * 流量计费详情列表查询
     *
     * @param req 列表查询条件
     * @return 列表数据
     */
    List<FlowChargingDetailExcel> flowChargingDetailList(BaseRemoteReqDto<FlowChargingDetailPageDto> req);
}
