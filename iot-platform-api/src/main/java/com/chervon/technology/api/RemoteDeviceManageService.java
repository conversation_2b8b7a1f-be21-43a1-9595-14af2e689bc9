package com.chervon.technology.api;

import com.chervon.technology.api.dto.DeviceEditDto;
import com.chervon.technology.api.dto.LastRnPackageDto;
import com.chervon.technology.api.vo.*;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.Map;

public interface RemoteDeviceManageService {

    /**
     * 根据设备deviceId获取设备信息
     *
     * @param deviceId 设备ID
     * @return DevicerpcVo
     */
    DeviceRpcVo deviceDetail(String deviceId);

    /**
     * 获取简单设备信息列表
     * @param listDeviceId
     * @return
     */
    List<DeviceRpcVo> getSimpleDeviceInfo(List<String> listDeviceId);

    /**
     * 获取设备详情
     *
     * @param deviceId 设备id
     * @return 设备详情
     */
    DeviceDetailInfoRpcVo getDeviceDetailByDeviceId(String deviceId, LastRnPackageDto lastRnPackageDto, Long userId);

    /**
     * 根据设备sn获取设备信息
     *
     * @param sn 设备SN
     * @return DeviceRpcVo
     */
    DeviceRpcVo deviceDetailBySn(String sn);

    /**
     * 获取设备详情
     *
     * @param deviceId 设备id
     * @return 设备详情
     */
    DeviceDetailInfoRpcVo getDeviceDetailBySn(String deviceId, LastRnPackageDto lastRnPackageDto, Long userId);

    /**
     * 根据设备Id获取设备列表信息，获取剪短的信息
     *
     * @param deviceIds
     * @return
     */
    List<DeviceListInfoRpcVo> listInfoByDeviceId(List<String> deviceIds);

    /**
     * 根据设备Ids,获取设备列表
     * 此为简化接口,只返回设备的少部分常用信息
     *
     * @param deviceIds 设备Id列表
     * @return DeviceRpcVo列表
     */
    List<DeviceRpcVo> listSimpleByDeviceId(List<String> deviceIds);

    /**
     * 编辑设备
     *
     * @param deviceEditDto 编辑设备信息
     */
    void editDevice(DeviceEditDto deviceEditDto);

    /**
     * 获取设备指定字段 "deviceId" "productModel" "isOnline"
     *
     * @param deviceIds:
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @date 20:32 2022/7/28
     **/
    List<Map<String, Object>> listMapByIds(List<String> deviceIds);

    /**
     * 通过deviceId绑定方法
     *
     * @param deviceId     设备ID
     * @param businessType app类型 1 ego 2 fleet
     * @param userId       用户id
     * @param companyId    租户id，fleet使用
     * @return DeviceBindBo
     */
    DeviceBindBo bindByDeviceId(String deviceId, int businessType, Long userId, Long companyId);

    /**
     * 通过sn绑定方法
     *
     * @param sn           设备SN
     * @param businessType app类型 1 ego 2 fleet
     * @param userId       用户id
     * @param companyId    租户id，fleet使用
     * @return DeviceBindBo
     */
    DeviceBindBo bindBySn(String sn, int businessType, Long userId, Long companyId);

    /**
     * 绑定设备之后回调逻辑
     * 1.设置iot_platform.device表nick_name字段
     */
    @Async
    void callBackAfterEditDevice(String nickName, String deviceId, Long userId);

    /**
     * 绑定设备后,设置设备绑定关系表
     *
     * @param deviceId     设备id
     * @param userId       用户id
     * @param companyId    租户id
     * @param businessType app 类型 1 ego  2 fleet
     */
    void setCurrentBusinessTypeAfterBind(String deviceId, Long userId, Long companyId, Integer businessType);

    /**
     * 更新当前app类型未未绑定
     *
     * @param deviceId 设备id
     */
    void setCurrentBusinessTypeAfterUnbind(String deviceId);

    /**
     * 批量查询设备信息
     *
     * @param deviceIds 设备id集合
     * @return 设备信息
     */
    List<CommonDeviceVo> batchDevice(List<String> deviceIds);


    /**
     * 根据deviceId更新设备表sn
     * @param deviceId
     * @param sn
     */
    boolean updateSnByDeviceId(String deviceId,String sn);
}
