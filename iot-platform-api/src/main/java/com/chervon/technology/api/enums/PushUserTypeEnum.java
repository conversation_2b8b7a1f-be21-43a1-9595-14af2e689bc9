package com.chervon.technology.api.enums;

import com.chervon.common.core.enums.TypeEnum;

import java.util.Arrays;

/**
 * 推送目标用户 1：绑定用户 2 app自定义用户
 */
public enum PushUserTypeEnum implements TypeEnum {
    /**
     * 1：EGO_BIND_USER
     */
    EGO_LOGIN_USER(1, "绑定用户"),
    /**
     * 2 app自定义用户
     */
    APP_CUSTOM_USER(2, "app自定义用户"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    /**
     * 业务类型构造函数
     * @param type 类型id
     * @param desc 描述
     */
    PushUserTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    public String getStrType(){
        return String.valueOf(type);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(PushUserTypeEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static PushUserTypeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
