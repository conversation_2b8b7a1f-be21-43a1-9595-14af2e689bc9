package com.chervon.technology.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-07-13
 */
@Data
@ApiModel(description = "获取最新rn包信息入参")
public class LastRnPackageDto implements Serializable {

    @ApiModelProperty("产品productId")
    private Long productId;

    @ApiModelProperty("宿主app类型 android  ios")
    private String appType;

    @ApiModelProperty("宿主包（android、ios 应用的版本）")
    private String appVersion;

    @ApiModelProperty("产品下的当前rn包版本（非必填，如果是空，则找最新的）")
    private String rnVersion;

    private Integer businessType;

}
