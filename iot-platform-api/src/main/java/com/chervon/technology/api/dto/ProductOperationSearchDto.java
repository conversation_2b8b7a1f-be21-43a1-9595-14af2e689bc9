package com.chervon.technology.api.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-07-01
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductOperationSearchDto extends PageRequest implements Serializable {
    @ApiModelProperty("产品Pid")
    private String id;

    @ApiModelProperty("品类Id")
    private Long categoryId;

    @ApiModelProperty("产品model")
    private String model;

    @ApiModelProperty("商品型号Model#")
    private String commodityModel;

    @ApiModelProperty("产品状态")
    private String status;

    @ApiModelProperty("产品发布状态")
    private String releaseStatus;

    @ApiModelProperty("产品品牌Id")
    private Long brandId;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("产品类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，" +
            "notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    private String productType;

    @ApiModelProperty("产品SnCode")
    private String productSnCode;

    @ApiModelProperty(value = "申请人")
    private String applyBy;

    @ApiModelProperty(value = "审核人")
    private String approveBy;

}