package com.chervon.technology.api.dto.ota;

import com.chervon.common.core.domain.PageRequest;
import com.chervon.technology.api.enums.OtaJobDevelopStatus;
import com.chervon.technology.api.enums.OtaJobReleaseStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @className JobConfigListDto
 * @description 任务配置列表搜索条件
 * @date 2022/7/13 11:06
 */
@ApiModel("任务配置列表搜索条件")
@EqualsAndHashCode(callSuper = true)
@Data
public class JobConfigListDto extends PageRequest {

    /**
     * 产品pid
     **/
    @ApiModelProperty(value = "产品pid")
    private Long productId;

    /**
     * 任务id
     **/
    @ApiModelProperty("任务id")
    private String jobId;

    /**
     * 升级任务的开发状态
     **/
    @ApiModelProperty("升级任务的开发状态")
    private OtaJobDevelopStatus developStatus;

    /**
     * 升级任务的发布状态
     **/
    @ApiModelProperty("升级任务的发布状态")
    private OtaJobReleaseStatus releaseStatus;
}
