package com.chervon.technology.api.vo.ota;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @className OtaHistoryVo
 * @description
 * @date 2022/7/13 16:11
 */
@ApiModel("升级历史")
@Data
public class OtaHistoryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前版本
     **/
    @ApiModelProperty("升级前版本")
    private String oldVersion;

    /**
     * 最新版本
     **/
    @ApiModelProperty("升级后版本")
    private String newVersion;

    /**
     * 升级内容
     **/
    @ApiModelProperty("升级内容")
    private String content;

    /**
     * 升级时间
     **/
    @ApiModelProperty("升级时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime upgradeTime;

}
