package com.chervon.common.web.core;

import com.chervon.common.web.entity.MetaContext;

import java.lang.reflect.Field;
import java.util.List;

/**
 * 转换器适配器服务：code绑定名称
 * * <AUTHOR> 2022/12/6
 */
public interface TranslateService<T,R,S> {
    /**
     * 获取适配转换器处理实例
     * @return
     */
    String getInstanceCode();

    /**
     * * 根据原字段信息读取对应目标对象信息
     * @param field
     * @param dataEntity
     * @return
     * @throws IllegalAccessException
     */
    default R getSourceValue(Field field, T dataEntity) throws IllegalAccessException{
        return null;
    }

    /**
     * * 将读取的目标对象信息赋值给集合对象
     * @param dataEntity
     * @param result
     * @param targetField
     * @throws IllegalAccessException
     */
    default void setTargetValue(T dataEntity, R result, List<String> targetField) throws IllegalAccessException{

    }

    /**
     * 批量根据原字段信息读取对应目标对象信息
     * @param context
     */
    default void batchGetSourceValue(MetaContext<S,R> context)
    {}

    /**
     * * 批量将读取的目标对象信息赋值给集合对象
     * @param dataEntity
     * @param context
     * @throws IllegalAccessException
     */
    default void batchSetTargetValue(List<T> dataEntity, MetaContext<S,R> context) throws IllegalAccessException
    {}
}
