package com.chervon.common.web.header;

/**
 * Header 上下文的持有者
 *
 * 
 * @date 2023/5/11
 */
public class HeaderContextHolder {
    /**
     * 上下文Local
     */
    public static final ThreadLocal<HeaderContext> HEADER_CONTEXT_THREAD_LOCAL = new InheritableThreadLocal<>();

    /**
     * 获取Header 上下文
     *
     * @return
     */
    public static HeaderContext getCtx() {
        return HEADER_CONTEXT_THREAD_LOCAL.get();
    }

    /**
     * 设置Header上下文
     *
     * @param ctx header 上下文
     */
    public static void setCtx(HeaderContext ctx) {
        HEADER_CONTEXT_THREAD_LOCAL.set(ctx);
    }

    /**
     * 移除上下文
     */
    public static void remove() {
        HEADER_CONTEXT_THREAD_LOCAL.remove();
    }

}
