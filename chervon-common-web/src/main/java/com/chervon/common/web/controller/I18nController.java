package com.chervon.common.web.controller;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.enums.TypeEnum;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.entity.I18nEnumAttribute;
import com.chervon.common.web.entity.I18nResource;
import com.chervon.common.web.util.HeaderUtils;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.web.entity.I18nEnumDto;
import com.chervon.common.web.entity.I18nErrorDto;
import com.chervon.common.web.util.I18nUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.io.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 国际化资源相关接口，错误码，枚举
 * @Date: 2023/05/19
 */
@Slf4j
@RestController
@RequestMapping(value = "/i18n")
public class I18nController implements ApplicationListener<ApplicationReadyEvent> {

    public static final String DEFAULT_LANGUAGE = "en";
    private static final Map<String, List<I18nEnumDto>> I18N_ENUM_MAP = new HashMap<>(50);
    private static final Map<String, List<I18nErrorDto>> I18N_ERROR_MAP = new HashMap<>(50);
    private static final Map<String, Map<String,String>> I18N_RESOURCE_MAP = new HashMap<>(50);
    private static final Map<String, Map<String,I18nErrorDto>> I18N_ERROR_DbMAP = new HashMap<>(50);
    public static final Map<String, Map<String, String>> I18N_FILE_RESOURCE_MAP = new HashMap<>(10);
    private static final String I18N_PATH = "i18n/**/**.json";
    private static final String FILE_RESOURCE_PATH = "i18n/**/**.html";
    private static final String ENUM = "enum";
    private static final String ERROR = "error";
    private static final String RESOURCE="resource";

    @PostMapping("/enum/list")
    public R<List<I18nEnumDto>> list(@RequestBody(required = false) List<String> keys) {
        //如果key不传的默认加载所有的枚举
        String language = DEFAULT_LANGUAGE;
        if (StringUtils.hasText(LocaleContextHolder.getLocale().getLanguage())) {
            language = LocaleContextHolder.getLocale().getLanguage();
        }
        return R.ok(getBatchEnum(language, keys));
    }

    //@ApiOperation("获取枚举列表接口给前端加载列表（支持国际化）")
    @GetMapping("/enum/{enumClassName}")
    public R<List<I18nEnumDto>> getByEnumType(@PathVariable(value = "enumClassName") String enumClassName) {
        //如果key不传的默认加载所有的枚举
        String language = DEFAULT_LANGUAGE;
        if (StringUtils.hasText(HeaderUtils.getLanguage())) {
            language = HeaderUtils.getLanguage();
        }
        final List<I18nEnumDto> anEnum = getBatchEnum(language, Arrays.asList(enumClassName));
        return R.ok(anEnum);
    }

    //@ApiOperation("返回全量配置的错误代码国际化列表：code：reason")
    @GetMapping("/error/list")
    public R<List<I18nErrorDto>> list() {
        String language = DEFAULT_LANGUAGE;
        if (!StringUtils.isEmpty(HeaderUtils.getLanguage())) {
            language = HeaderUtils.getLanguage();
        }
        List<I18nErrorDto> i18nErrorDtoList = I18N_ERROR_MAP.get(language);
        if (CollectionUtils.isEmpty(i18nErrorDtoList)) {
            return null;
        }
        return R.ok(i18nErrorDtoList);
    }

    public static I18nEnumDto getEnumListByType(String language, TypeEnum enumType){
        final String simpleName = enumType.getClass().getSimpleName();
        final List<I18nEnumDto> anEnum = getBatchEnum(language, Arrays.asList(simpleName));
        if(!CollectionUtils.isEmpty(anEnum)){
            return anEnum.get(0);
        }
        return new I18nEnumDto().setKey(simpleName).setAttributeList(new ArrayList<>());
    }

    /**
     * 获取枚举信息
     * <p>
     * @param language 语言标记
     * @param keys     keys
     * @return list
     */
    public static List<I18nEnumDto> getBatchEnum(String language, List<String> keys) {
        Assert.hasText(language, ErrorCode.PARAMETER_NOT_PROVIDED, "language");

        List<I18nEnumDto> i18nEnumDtoList = I18N_ENUM_MAP.get(language);
        if (CollectionUtils.isEmpty(i18nEnumDtoList)) {
            return null;
        }
        if (CollectionUtils.isEmpty(keys)) {
            return i18nEnumDtoList;
        }

        Map<String, I18nEnumDto> i18nEnumDtoMap = i18nEnumDtoList.stream().collect(Collectors.toMap(I18nEnumDto::getKey, x -> x));
        List<I18nEnumDto> resultList = new ArrayList<>(keys.size());
        for (String key : keys) {
            I18nEnumDto dto = i18nEnumDtoMap.get(key);
            if (Objects.nonNull(dto)) {
                resultList.add(dto);
            }
        }
        return resultList;
    }

    /**
     * 获取枚举信息
     * @param language 语言标记
     * @param enumName 类名
     * @param item 具体项目
     * @return list
     */
    public static String getEnumItemValue(String language, String enumName,String item) {
        List<I18nEnumDto> i18nEnumDtoList = I18N_ENUM_MAP.get(language);
        if (CollectionUtils.isEmpty(i18nEnumDtoList)) {
            return null;
        }
        final Optional<I18nEnumDto> first = i18nEnumDtoList.stream().filter(a -> a.getKey().equals(enumName)).findFirst();
        if(!first.isPresent()){
            return null;
        }
        final Optional<I18nEnumAttribute> optItem = first.get().getAttributeList().stream().filter(a -> a.getType().equals(item)).findFirst();
        if(!optItem.isPresent()){
            return null;
        }
        return optItem.get().getDesc();
    }

    /**
     * 获取枚举信息
     * @param language 语言标记
     * @param key     keys
     * @return list
     */
    public static List<I18nEnumAttribute> getEnum(String language, String key) {
        Assert.hasText(language, ErrorCode.PARAMETER_NOT_PROVIDED, "language");
        List<I18nEnumDto> i18nEnumDtoList = I18N_ENUM_MAP.get(language);
        if (CollectionUtils.isEmpty(i18nEnumDtoList)) {
            return Collections.emptyList();
        }
        Map<String, I18nEnumDto> i18nEnumDtoMap = i18nEnumDtoList.stream().collect(Collectors.toMap(I18nEnumDto::getKey, x -> x));
        List<I18nEnumAttribute> resultList = new ArrayList<>();
        I18nEnumDto dto = i18nEnumDtoMap.get(key);
        if (Objects.nonNull(dto)) {
            resultList.addAll(dto.getAttributeList());
        }else{
            return Collections.emptyList();
        }
        return resultList;
    }

    /**
     * 获取枚举信息
     * @param language 语言标记
     * @param errorCode     keys
     * @return list
     */
    public static I18nErrorDto getI18NError(String language, String errorCode) {
        Assert.hasText(language, ErrorCode.PARAMETER_NOT_PROVIDED, "language");
        Map<String,I18nErrorDto> i18nEnumDtoMap = I18N_ERROR_DbMAP.get(language);
        if (CollectionUtils.isEmpty(i18nEnumDtoMap)) {
            return null;
        }
        if (StringUtils.isEmpty(errorCode)) {
            return null;
        }
        return i18nEnumDtoMap.get(errorCode);
    }

    /**
     * * 根据业务Id获取配置的静态国际化资源
     * @param language 语言编号
     * @param resourceId 资源id
     * @return 国际化资源内容
     */
    public static String getResourceById(String language,String resourceId) {
        if (CollectionUtils.isEmpty(I18N_RESOURCE_MAP)) {
            return "";
        }
        Map<String, String> map = I18N_RESOURCE_MAP.get(language);
        if (CollectionUtils.isEmpty(map)) {
            return "";
        }
        return map.get(resourceId);
    }

    /**
     * * 根据业务Id从文件资源获取配置的静态国际化资源
     * @param language 语言编号
     * @param resourceId 资源id
     * @return 国际化资源内容
     */
    public static String getFileResourceById(String language,String resourceId) {
        if (CollectionUtils.isEmpty(I18N_FILE_RESOURCE_MAP)) {
            return "";
        }
        Map<String, String> map = I18N_FILE_RESOURCE_MAP.get(language);
        if (CollectionUtils.isEmpty(map)) {
            return "";
        }
        return map.get(resourceId);
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        //扫描i18n包下文件 .json
        Resource[] resources = I18nUtils.findFiles(I18N_PATH);
        if (Objects.isNull(resources) || resources.length < 1) {
            return;
        }
        List<Resource> enumList = new ArrayList<>();
        List<Resource> errorList = new ArrayList<>();
        List<Resource> resourceList=new ArrayList<>();
        for (Resource resource : resources) {
            String fileName = resource.getFilename();
            if (!StringUtils.hasText(fileName)) {
                return;
            }
            //文件名必须以下划线分割，规则为：语言_**_enum.json 例如：zh-cn_rpm_enum.json
            String[] nameArray = fileName.split("_");
            //判断文件是枚举还是错误类型
            String lastFileName = nameArray[nameArray.length - 1].split("\\.")[0];
            if (lastFileName.equalsIgnoreCase(ENUM)) {
                enumList.add(resource);
            } else if (lastFileName.equalsIgnoreCase(ERROR)) {
                errorList.add(resource);
            } else if (lastFileName.equalsIgnoreCase(RESOURCE)) {
                resourceList.add(resource);
            }
        }

        //初始化枚举
        initAllEnum(enumList);
        //初始化错误信息
        initAllError(errorList);
        //初始化所有键值对常量资源
        initAllResource(resourceList);
        //初始化文件资源
        initAllFileResource();
    }

    private static void initAllFileResource() {
        org.springframework.core.io.Resource[] resources = I18nUtils.findFiles(FILE_RESOURCE_PATH);
        if (Objects.isNull(resources) || resources.length < 1) {
            return;
        }
        for (org.springframework.core.io.Resource resource : resources) {
            String fileName = resource.getFilename();
            if (!StringUtils.hasText(fileName)) {
                return;
            }
            //文件名必须以下划线分割，规则为：语言_**_enum.json 例如：zh-cn_rpm_enum.json
            String[] nameArray = fileName.split("_");
            String lang = nameArray[0];
            String id = removeExtension(nameArray[1]);
            String content = I18nUtils.readFile(resource);
            if (!StringUtils.hasText(content)) {
                return;
            }
            Map<String,String> listFileResource = I18N_FILE_RESOURCE_MAP.get(lang);
            if(listFileResource==null){
                listFileResource=new HashMap<>();
            }
            listFileResource.put(id,content);
            I18N_FILE_RESOURCE_MAP.put(lang,listFileResource);
        }
        log.info("init file Resource size:{}", I18N_FILE_RESOURCE_MAP.size());
    }

    /**
     * 初始化错误信息
     */
    private static void initAllError(List<Resource> errorList) {
        for (Resource resource : errorList) {
            String fileName = resource.getFilename();
            //文件名必须以下划线分割，规则为：语言_**_enum.json 例如：zh-cn_rpm_enum.json
            String[] nameArray = fileName.split("_");
            String language = nameArray[0];
            String json = I18nUtils.readFile(resource);
            if (!StringUtils.hasText(json)) {
                return;
            }
            List<I18nErrorDto> i18List = JsonUtils.toObject(json, new TypeReference<List<I18nErrorDto>>() {
            });
            if (CollectionUtils.isEmpty(i18List)) {
                return;
            }
            List<I18nErrorDto> i18nErrorDtoList;
            if (CollectionUtils.isEmpty(I18N_ERROR_MAP.get(language))) {
                i18nErrorDtoList = new ArrayList<>(i18List.size());
            } else {
                i18nErrorDtoList = I18N_ERROR_MAP.get(language);
            }
            i18nErrorDtoList.addAll(i18List);
            //解析出来所有的错误信息增加到I18N_ERROR_MAP中
            if (CollectionUtils.isEmpty(I18N_ERROR_MAP.get(language))) {
                I18N_ERROR_MAP.put(language, i18nErrorDtoList);
            }
        }
        //解析出来所有的错误信息增加到I18N_ERROR_MAP中
        if (!CollectionUtils.isEmpty(I18N_ERROR_MAP)) {
            for(Map.Entry<String,List<I18nErrorDto>> entry:I18N_ERROR_MAP.entrySet()){
                Map<String, I18nErrorDto> errorDtoMap = entry.getValue().stream().collect(Collectors.toMap(a -> a.getCode(), x -> x,(k,v)->v));
                I18N_ERROR_DbMAP.put(entry.getKey(),errorDtoMap);
            }
        }
        log.info("initAllError init map->{}", JsonUtils.toJson(I18N_ERROR_MAP));
    }
    /**
     * 初始化错误信息
     */
    private static void initAllResource(List<Resource> resourceList) {
        for (Resource resource : resourceList) {
            String fileName = resource.getFilename();
            //文件名必须以下划线分割，规则为：语言_**_enum.json 例如：zh-cn_rpm_enum.json
            String[] nameArray = fileName.split("_");
            String language = nameArray[0];
            String json = I18nUtils.readFile(resource);
            if (!StringUtils.hasText(json)) {
                return;
            }
            List<I18nResource> i18List = JsonUtils.toObject(json, new TypeReference<List<I18nResource>>() {
            });
            if (CollectionUtils.isEmpty(i18List)) {
                return;
            }
            Map<String, String> currentFileResourceMap = i18List.stream().collect(Collectors.toMap(a -> a.getId(), x -> x.getName(), (k, v) -> v));
            if (Objects.isNull(I18N_RESOURCE_MAP.get(language))) {
                I18N_RESOURCE_MAP.put(language,currentFileResourceMap);
            } else {
                Map<String, String> innerResourceMap = I18N_RESOURCE_MAP.get(language);
                innerResourceMap.putAll(currentFileResourceMap);
            }
        }
        log.info("initAllResource init map->{}", JsonUtils.toJson(I18N_RESOURCE_MAP));
    }
    /**
     * 初始化所有枚举
     */
    private static void initAllEnum(List<Resource> enumList) {
        for (Resource resource : enumList) {
            String fileName = resource.getFilename();
            //文件名必须以下划线分割，规则为：语言_**_enum.json 例如：zh-cn_rpm_enum.json
            String[] nameArray = fileName.split("_");
            String language = nameArray[0];
            String json = I18nUtils.readFile(resource);
            if (!StringUtils.hasText(json)) {
                return;
            }
            List<I18nEnumDto> i18List = JsonUtils.toObject(json, new TypeReference<List<I18nEnumDto>>() {
            });
            if (CollectionUtils.isEmpty(i18List)) {
                return;
            }
            List<I18nEnumDto> mapI18nEnumList;
            if (CollectionUtils.isEmpty(I18N_ENUM_MAP.get(language))) {
                mapI18nEnumList = new ArrayList<>(i18List.size());
            } else {
                mapI18nEnumList = I18N_ENUM_MAP.get(language);
            }
            mapI18nEnumList.addAll(i18List);
            //解析出来所有的枚举信息增加到I18N_ENUM_MAP中
            if (CollectionUtils.isEmpty(I18N_ENUM_MAP.get(language))) {
                I18N_ENUM_MAP.put(language, mapI18nEnumList);
            }
        }
        log.info("initAllEnum init map->{}", JsonUtils.toJson(I18N_ENUM_MAP));
    }
    public static String removeExtension(String fileName) {
        int pos = fileName.lastIndexOf('.');
        if(pos > -1){
            return fileName.substring(0, pos);
        }
        else{
            return fileName;
        }
    }

    public static Map<String, List<I18nEnumDto>> getI18nEnumMap(){
        return I18N_ENUM_MAP;
    }
    public static Map<String, List<I18nErrorDto>> getI18nErrorMap(){
        return I18N_ERROR_MAP;
    }
}
