package com.chervon.common.web.header;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.WebApplicationType;
import org.springframework.http.HttpHeaders;
import org.springframework.util.ClassUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.*;
import java.util.function.Supplier;

/**
 * 伪请求参数
 * 
 * @date 2022/9/11
 */
@Slf4j
public class HeaderContext {
    private static final String[] SERVLET_INDICATOR_CLASSES = {
            "javax.servlet.Servlet",
            "org.springframework.web.context.ConfigurableWebApplicationContext"
    };
    private static final String WEBMVC_INDICATOR_CLASS = "org.springframework.web.servlet.DispatcherServlet";
    private static final String WEBFLUX_INDICATOR_CLASS = "org.springframework.web.reactive.DispatcherHandler";
    private static final String JERSEY_INDICATOR_CLASS = "org.glassfish.jersey.servlet.ServletContainer";
    /**
     * 计算服务运行环境
     */
    private static final Supplier<WebApplicationType> APPLICATION_TYPE_SUPPLIER =
            new Supplier<WebApplicationType>() {
                private volatile WebApplicationType instance;

                @Override
                public WebApplicationType get() {
                    if (instance == null) {
                        synchronized (HeaderContext.class) {
                            if (instance == null) {
                                instance = deduceFromClasspath();
                            }
                        }
                    }
                    return instance;
                }
            };


    private Map<String, String> headers = null;
    private HttpHeaders httpHeaders;


    private HeaderContext() {

    }

    /**
     * 是否是MVC项目
     *
     * @return
     */
    public boolean isMvc() {
        return APPLICATION_TYPE_SUPPLIER.get() == WebApplicationType.SERVLET;
    }

    /**
     * 获取Header信息
     * PS:reactive 模式，返回的是List 这由底层实现决定
     *
     * @param name Header 名
     * @return
     */
    public String getHeader(String name) {
        if (APPLICATION_TYPE_SUPPLIER.get() == WebApplicationType.SERVLET) {
            //正常的请求Header上下文
            HttpServletRequest request = null;
            try {
                ServletRequestAttributes attributes = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes());
                if (attributes != null) {
                    request = attributes.getRequest();
                    //默认取第一个Header  暂不支持多个Header返回
                    String result = request.getHeader(name);
                    if (null == result) {
                        return null;
                    }
                    return URLDecoder.decode(result, "UTF-8");
                }
            } catch (Exception e) {
                log.warn(e.getMessage(), e);
            }
        } else if (APPLICATION_TYPE_SUPPLIER.get() == WebApplicationType.REACTIVE && httpHeaders != null) {
            List<String> value = httpHeaders.get(name);
            if (value != null) {
                return String.join(";", value);
            }
        } else if (headers != null) {
            return headers.get(name);
        }
        //上下文都没有找到
        return null;
    }

    public void refreshCache() {
        Map<String, String> innerHeaderMap = new HashMap<>();
        if (APPLICATION_TYPE_SUPPLIER.get() == WebApplicationType.SERVLET) {
            //正常的请求Header上下文
            HttpServletRequest request;
            try {
                ServletRequestAttributes attributes = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes());
                if (attributes != null) {
                    request = attributes.getRequest();
                    Enumeration<String> names = request.getHeaderNames();
                    while (names.hasMoreElements()) {
                        String name = names.nextElement();
                        String value = getHeader(name);
                        innerHeaderMap.put(name, value);
                    }
                }
            } catch (Exception e) {
                log.warn(e.getMessage(), e);
            }
        } else if (APPLICATION_TYPE_SUPPLIER.get() == WebApplicationType.REACTIVE && httpHeaders != null) {
            for (String name : httpHeaders.keySet()) {
                List<String> value = httpHeaders.get(name);
                if (value == null) {
                    continue;
                }
                innerHeaderMap.put(name, String.join(";", value));
            }
        }

        if (innerHeaderMap.size() > 0) {
            headers = innerHeaderMap;
        }
    }

    /**
     * 获取所有的Header
     *
     * @return
     */
    public Map<String, String> getAllHeader() {
        refreshCache();
        return Collections.unmodifiableMap(headers);
    }

    /**
     * 创建上下文
     *
     * @return 上下文
     */
    public static HeaderContext of() {
        return new HeaderContext();
    }

    /**
     * 创建上下文
     *
     * @param headers header内容
     * @return 上下文
     */
    public static HeaderContext of(Map<String, String> headers) {
        HeaderContext ctx = new HeaderContext();
        ctx.headers = new HashMap<>(headers);
        return ctx;
    }

    /**
     * 创建上下文
     *
     * @param headers header内容
     * @return 上下文
     */
    public static HeaderContext of(HttpHeaders headers) {
        HeaderContext ctx = new HeaderContext();
        ctx.httpHeaders = headers;
        return ctx;
    }


    /**
     * 验证当前的运行环境
     * copy by spring WebApplicationType
     *
     * @return WebApplicationType
     */
    public static WebApplicationType deduceFromClasspath() {
        if (ClassUtils.isPresent(WEBFLUX_INDICATOR_CLASS, null) && !ClassUtils.isPresent(WEBMVC_INDICATOR_CLASS, null)
                && !ClassUtils.isPresent(JERSEY_INDICATOR_CLASS, null)) {
            return WebApplicationType.REACTIVE;
        }
        for (String className : SERVLET_INDICATOR_CLASSES) {
            if (!ClassUtils.isPresent(className, null)) {
                return WebApplicationType.NONE;
            }
        }
        return WebApplicationType.SERVLET;
    }
}
