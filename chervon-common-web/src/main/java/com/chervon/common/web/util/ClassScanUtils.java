package com.chervon.common.web.util;

import com.chervon.common.core.utils.SpringUtils;
import lombok.SneakyThrows;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternUtils;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;
import java.util.regex.Pattern;

/**
 * 类型扫描工具
 *
 *
 * @date 2022/9/11
 */
public class ClassScanUtils {
    /**
     * 扫描basePackage下的类
     *
     * @param basePackage basePackage 基包
     * @return 复合条件的类型
     */
    @SneakyThrows
    public static List<Class<?>> doScan(String basePackage) {
        return doScan(basePackage, metadataReader -> true);
    }


    /**
     * 扫描basePackage下的类
     *
     * @param basePackage 基包
     * @param filter      条件
     * @return 复合条件的类型
     */
    @SneakyThrows
    public static List<Class<?>> doScan(String basePackage, Predicate<MetadataReader> filter) {
        if (!StringUtils.hasText(basePackage)) {
            return new ArrayList<>(0);
        }
        List<Class<?>> clazzList = new ArrayList<>();
        Pattern pattern = Pattern.compile("[\\w\\.*]+");
        if (pattern.matcher(basePackage).matches()) {
            ResourcePatternResolver resourcePatternResolver = ResourcePatternUtils.getResourcePatternResolver(SpringUtils.getApplicationContext());
            MetadataReaderFactory metaReader = new CachingMetadataReaderFactory(SpringUtils.getApplicationContext());
            Resource[] resources = resourcePatternResolver.getResources("classpath*:" + basePackage.replace('.', '/') + "/**/*.class");
            for (Resource r : resources) {
                MetadataReader reader = metaReader.getMetadataReader(r);
                if (filter.test(reader)) {
                    clazzList.add(Class.forName(reader.getClassMetadata().getClassName()));
                }
            }
            return clazzList;
        }

        return new ArrayList<>(0);
    }
}
