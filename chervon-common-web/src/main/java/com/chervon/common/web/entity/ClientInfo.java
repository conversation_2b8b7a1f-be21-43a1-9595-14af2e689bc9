package com.chervon.common.web.entity;

import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> 2023/6/26
 */
@Data
public class ClientInfo {
    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 全名
     */
    private String name;
    /**
     *  角色类型
     */
    private Integer userType;
    /**
     * 登录设备类型
     */
    private Integer deviceType;
    private String deviceId;
    private String language;
    private String traceId;
    private Long companyId;

//    public void setUserId(String strUserId) {
//        if (StringUtils.hasText(strUserId)) {
//            userId = Long.valueOf(strUserId);
//        }
//    }
//
//    public void setUserType(String strUserType) {
//        if (StringUtils.hasText(strUserType)) {
//            userType = Integer.valueOf(strUserType);
//        }
//    }
//
//    public void setCompanyId(String strCompanyId) {
//        if (StringUtils.hasText(strCompanyId)) {
//            companyId = Long.valueOf(strCompanyId);
//        }
//    }
//
//    public void setDeviceType(String strDeviceType) {
//        if (StringUtils.hasText(strDeviceType)) {
//            deviceType = Integer.valueOf(strDeviceType);
//        }
//    }
}
