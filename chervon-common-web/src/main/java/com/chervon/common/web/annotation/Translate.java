package com.chervon.common.web.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自动转换器注解
 * <AUTHOR> 2022/11/30
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Translate {
    /**
     * 转换类型:ConvertType.ENUM, ConvertType.DATEFORMAT
     * @return
     */
    int type() default 0;

    /**
     * 转换处理器：具体转换处理的类实例名称
     * @return
     */
    String adapter() default "";

    /**
     * 转换后将值绑定到的目标字段，支持多个如：
     * targetField = {"categoryName","publishStatusName"}
     * 需自行在转换器中实现
     * @return
     */
    String[] targetField();

    /**
     * 日期格式的转换输出，用于指定输出格式
     * @return
     */
    String dateFormat() default "yyyy-MM-dd HH:mm:ss";

    /**
     * 指定此参数一般用于根据枚举key获取枚举名称或描述
     * 指定需要转换的枚举类型,枚举需实现类型：EnumUtils.IEnum
     * @return
     */
    Class enumType() default Void.class;

    /**
     * 指定此枚举名称用于解析I18N中枚举的名称，根据国际化配置的获取键值名称信息
     * @return
     */
    String enumName() default "";
}