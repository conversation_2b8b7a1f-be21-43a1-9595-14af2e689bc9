package com.chervon.authority.api.exception;

import com.chervon.common.core.exception.ErrorCodeI;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统一权限103
 *
 * <AUTHOR>
 * @date 2022/6/26 18:24
 */
@AllArgsConstructor
@Getter
public enum AuthorityErrorCode implements ErrorCodeI {

    /**
     * 资源相关 001
     */
    AUTHORITY_RESOURCE_NAME_ALREADY_EXIST("1030012001", "resource name already exist", "resource name: [%s] already exist"),
    AUTHORITY_RESOURCE_CODE_ALREADY_EXIST("1030012002", "resource code already exist", "resource code: [%s] already exist"),
    AUTHORITY_PARENT_RESOURCE_NOT_EXIST("1030012003", "parent resource is not exist", "parent resource is not exist, resource: [%s]"),

    /**
     * 存在子级菜单资源，不能删除
     */
    AUTHORITY_RESOURCE_EXIST_SUB_LEVEL_RESOURCE("1030012004", "exist sub level resource, cant not delete", "exist sub level resource, cant not delete"),
    /**
     * 有角色和资源绑定，请先删除绑定关系
     */
    AUTHORITY_RESOURCE_CANNOT_DELETE("1030012005", "please remove first %s and %s menu association,then delete", "please remove first %s and %s menu association,then delete"),
    AUTHORITY_ROLE_NOT_EXIST("1030012006", "role is not exist", "role: [%s] is not exist"),
    AUTHORITY_ROLE_ADMIN_CAN_NOT_DELETE("1030012007", "role is admin, can not delete", "role: [%s] is admin, can not delete"),
    AUTHORITY_ROLE_RELATE_USER_CAN_NOT_DELETE("1030012008", "role is relate user, can not delete", "role: [%s] is relate user, can not delete"),

    /**
     * 授权关系已存在
     */
    AUTHORITY_ALREADY_EXISTED("1030012009", "authority already existed", "authority already existed"),

    /**
     * 角色不存在
     */
    AUTHORITY_ROLE_IS_NOT_FOUND("1030012010", "authority role is not found", "authority role is not found, metaId: [%s]"),

    /**
     * 系统名称必须最大36个字符，不能包含有空格和特殊字符
     */
    AUTHORITY_PLATFORM_NAME_INVALID("1030012011", "authority platform name invalid", "authority platform name invalid, platformName: [%s]"),

    /**
     * AppId必须最大36个字符，不能包含有空格和特殊字符
     */
    AUTHORITY_PLATFORM_APPID_INVALID("1030012012", "authority platform appid invalid", "authority platform appid invalid"),

    /**
     * 平台描述最大为500个字符，不能包含有空格和特殊字符
     */
    AUTHORITY_PLATFORM_DESCRIPTION_INVALID("1030012013", "authority platform description invalid", "authority platform description invalid, desc: [%s]"),

    /**
     * 系统名称已经存在
     */
    AUTHORITY_PLATFORM_NAME_ALREADY_EXIST("1030012014", "authority platform name already exist", "authority platform name already exist, platformName: [%s]"),

    /**
     * 系统不存在
     */
    AUTHORITY_PLATFORM_NOT_EXIST("1030012015", "authority platform not exist", "authority platform not exist, platformIds: [%s]"),

    /**
     * 资源不存在
     */
    AUTHORITY_RESOURCE_NOT_EXIST("1030012016", "authority resource not exist", "authority resource not exist, id: [%s]"),

    /**
     * 资源排序无效
     */
    AUTHORITY_RESOURCE_SORT_INVALID("1030012017", "authority resource sort invalid", "authority resource sort invalid"),


    AUTHORITY_PARAM_ERROR("1030012018", "authority param error", "authority param error"),

    AUTHORITY_ROLE_ALREADY_EXISTED("1030012019", "authority role already existed", "authority role already existed"),

    AUTHORITY_DATA_NOT_EXIST("1030012020", "authority data not exist", "authority data not exist, roleId: [%s]"),

    AUTHORITY_META_ID_IS_EXIST("1030012021", "authority meta id is exist", "authority meta id is exist metaId: [%s]"),

    /**
     * 用户角色不存在
     */
    AUTHORITY_ROLE_BINDING_USER_NOT_EXIST("1030012022", "authority role binding user not exist", "authority role binding user not exist"),
    /**
     * 角色名已存在
     */
    AUTHORITY_ROLE_NAME_IS_EXIST("1030012023", "authority role name is exist", "authority role name is exist, roleName: [%s]"),
    /**
     * 管理员角色不可编辑
     */
    AUTHORITY_ADMIN_ROLE_NOT_EDITABLE("1030012024", "authority admin role not editable", "authority admin role not editable, role: [%s]"),
    /**
     * 全量组织用户树未同步，请稍后重试
     */
    AUTHORITY_ORG_USER_TREE_NOT_EXIST("1030012025", "org user tree not synced, please try again later", "org user tree: [%s] not synced, please try again later"),

    /**
     * appId已经存在
     */
    AUTHORITY_PLATFORM_APPID_ALREADY_EXIST("1030012026", "authority platform appid already exist", "authority platform appid already exist, appId: [%s]"),

    /**
     * 获取用户信息异常
     */
    AUTHORITY_GET_USERINFO_ERROR("1030092005", "get login info error", "get login info error"),

    /**
     * 权限校验 - appId为空
     */
    AUTHORITY_CHECK_APPID_IS_BLANK("1030092001", "permission check appId is blank", "permission check appId: [%s] is blank"),

    /**
     * 权限校验 - url非法
     */
    AUTHORITY_CHECK_URL_IS_INVALID("1030092001", "permission check appId is blank", "permission check appId: [%s] is blank"),


    /**
     * 没有权限
     */
    NO_PERMISSION("403", "no permission", "no permission"),

    AUTHORITY_GET_ORG_FAILED("1030012027","RPC failed to obtain the organization name", "RPC failed to obtain the organization name, guid: [%s]"),

    AUTHORITY_GET_USER_FAILED("1030012028","RPC failed to obtain the user", "RPC failed to obtain the user, guid: [%s]"),
    ;



    private final String code;

    private final String defaultMessage;

    private final String errorMessage;
}
