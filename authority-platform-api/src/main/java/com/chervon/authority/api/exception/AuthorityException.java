package com.chervon.authority.api.exception;

import com.chervon.common.core.exception.base.BaseException;

/**
 * <AUTHOR>
 * @date 2022/6/26 18:24
 */
public class AuthorityException extends BaseException {

    private static final long serialVersionUID = 1L;

    private String tempMessage;

    public void setTempMessage(String tempMessage) {
        this.tempMessage = tempMessage;
    }

    public AuthorityException(AuthorityErrorCode code, Object... args) {
        super("authority-platform", code.getCode(), code.getDefaultMessage(), code.getErrorMessage(), args);
    }

    public AuthorityException(String msg) {
        super("authority-platform", msg);
    }

    // 统一权限管理中心相关

    /**
     * 系统下的相关菜单
     */
    public static final String AUTHORITY_PLATFORM_SYSTEM = "related menu under the system:";
    /**
     * 请解除关联
     */
    public static final String AUTHORITY_PLATFORM_DISASSOCIATE = "please disassociate";
    /**
     * 系统删除提示语
     */
    public static final String AUTHORITY_PLATFORM_FROM = "from";
    /**
     * 系统删除提示语然后删除菜单
     */
    public static final String AUTHORITY_PLATFORM_MENU_THEN_DELETE = "menu then delete";
    /**
     * 系统删除提示语系统删除提示角色信息
     */
    public static final String AUTHORITY_PLATFORM_SYSTEM_ROLES = "and associated roles:this system is associated with the";
    /**
     * 系统删除提示
     */
    public static final String AUTHORITY_PLATFORM_REMOVE_BEFORE_DELETING = "menu please remove before deleting";
    /**
     * 角色被关联和授权：此系统
     */
    public static final String AUTHORITY_PLATFORM_REMOVE_ROLES_AUTHORIZED = "and the role is associated and authorized: this system is associated with the";

    @Override
    public String getMessage() {
        if (tempMessage != null) {
            return this.tempMessage;
        }
        return this.getDefaultMessage() == null ? this.getCode() : String.format(this.getDefaultMessage(), this.getArgs());
    }

}
