package com.chervon.iot.app.service.impl;

import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.app.AppApplication;
import com.chervon.iot.app.domain.dto.RobotCheckEmailCodeDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RobotService集成测试类
 * 使用实际的AWS SES服务发送邮件
 */
@SpringBootTest(classes = AppApplication.class)
public class RobotServiceImplIntegrationTest {

    @Autowired
    private RobotServiceImpl robotService;

    // 测试邮箱地址 - 请替换为实际可用的邮箱地址
    private static final String TEST_EMAIL_1 = "<EMAIL>";
    private static final String TEST_EMAIL_2 = "<EMAIL>";

    @Test
    void testSendEmailCode_SingleRecipient() {
        // 准备测试数据 - 单个收件人
        List<String> emails = new ArrayList<>();
        emails.add(TEST_EMAIL_1);

        // 执行测试
        String verificationCode = robotService.sendEmailCode(emails);

        // 验证结果
        assertNotNull(verificationCode);
        assertTrue(StringUtils.isNotEmpty(verificationCode));
        System.out.println("Generated verification code: " + verificationCode);

    }

    @Test
    void testSendEmailCode_MultipleRecipients() {
        // 准备测试数据 - 多个收件人
        List<String> emails = Arrays.asList(TEST_EMAIL_1, TEST_EMAIL_2);

        System.out.println(LocaleContextHolder.getLocale().getLanguage());

        // 执行测试
        String verificationCode = robotService.sendEmailCode(emails);

        // 验证结果
        assertNotNull(verificationCode);
        assertTrue(StringUtils.isNotEmpty(verificationCode));
        System.out.println("Generated verification code for multiple recipients: " + verificationCode);
    }

    @Test
    void testSendEmailCode_FilterInvalidEmails() {
        // 准备测试数据 - 包含无效邮箱
        List<String> emails = new ArrayList<>();
        emails.add(TEST_EMAIL_1);
        emails.add(" ");
        emails.add("");
        emails.add(null);

        // 执行测试
        String verificationCode = robotService.sendEmailCode(emails);

        // 验证结果
        assertNotNull(verificationCode);
        assertTrue(StringUtils.isNotEmpty(verificationCode));
        System.out.println("Generated verification code after filtering invalid emails: " + verificationCode);
    }
}