<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.iot.app.mapper.DeviceShareMapper">
    <select id="countShareNum" resultType="java.util.HashMap">
        SELECT device_id,count(*) as num
        FROM device_share
        WHERE is_deleted=0
          AND master_id=#{masterId}
        GROUP BY device_id
    </select>
</mapper>
