package com.chervon.iot.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.iot.app.config.ExceptionMessageUtil;
import com.chervon.iot.app.domain.dataobject.UserDealer;
import com.chervon.iot.app.domain.dto.dealer.DealerListDto;
import com.chervon.iot.app.mapper.UserDealerMapper;
import com.chervon.iot.app.service.UserDealerService;
import com.chervon.operation.api.RemoteDealerService;
import com.chervon.operation.api.vo.AppDealerVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.chervon.iot.app.api.exception.AppErrorCode.*;

/**
 * <AUTHOR>
 * @date 2022/11/19 16:27
 */
@Service
@Slf4j
public class UserDealerServiceImpl extends ServiceImpl<UserDealerMapper, UserDealer> implements UserDealerService {

    @DubboReference
    private RemoteDealerService remoteDealerService;

    @Override
    public List<AppDealerVo> list(DealerListDto req) {
        List<AppDealerVo> res = new ArrayList<>();
        if (req.isUploadLocation()) {
            double lat;
            double lng;
            try {
                lat = Double.parseDouble(req.getLat());
            } catch (Exception ex) {
                throw ExceptionMessageUtil.getException(APP_DEALER_LAT_ILLEGAL, req.getLat());
            }
            try {
                lng = Double.parseDouble(req.getLng());
            } catch (Exception ex) {
                throw ExceptionMessageUtil.getException(APP_DEALER_LNG_ILLEGAL, req.getLng());
            }
            res = remoteDealerService.list(req.getCategory(), lat, lng, LocaleContextHolder.getLocale().getLanguage());
            if (!CollectionUtils.isEmpty(res)) {
                List<UserDealer> list = this.list(new LambdaQueryWrapper<UserDealer>().eq(UserDealer::getUserId, StpUtil.getLoginIdAsLong()));
                if (!CollectionUtils.isEmpty(list)) {
                    List<String> dealerIds = list.stream().map(i -> String.valueOf(i.getDealerId())).collect(Collectors.toList());
                    res.forEach(e -> e.setCollection(dealerIds.contains(e.getDealerId())));
                }
            }
        } else {
            List<UserDealer> list = this.list(new LambdaQueryWrapper<UserDealer>().eq(UserDealer::getUserId, StpUtil.getLoginIdAsLong()).orderByDesc(UserDealer::getCreateTime));
            if (!CollectionUtils.isEmpty(list)) {
                List<Long> dealerIds = list.stream().map(UserDealer::getDealerId).collect(Collectors.toList());
                res = remoteDealerService.listByIds(dealerIds, LocaleContextHolder.getLocale().getLanguage());
                res.forEach(e -> e.setCollection(true));
            }
        }
        return res;
    }

    @Override
    public List<AppDealerVo> list2(DealerListDto req) {
        Assert.isTrue(req.isUploadLocation(), ErrorCode.PARAMETER_ERROR, "uploadLocation");
        Assert.hasText(req.getCategory(), ErrorCode.PARAMETER_ERROR, "category");
        Assert.isTrue(Math.abs(Double.parseDouble(req.getLat())) > 0, ErrorCode.PARAMETER_ERROR, "lat");
        Assert.isTrue(Math.abs(Double.parseDouble(req.getLng())) > 0, ErrorCode.PARAMETER_ERROR, "lng");
        Assert.isTrue(req.getDistance() > 0, ErrorCode.PARAMETER_ERROR, "distance");
        return remoteDealerService.list(req.getCategory(), Double.parseDouble(req.getLat()), Double.parseDouble(req.getLng()),
                req.getDistance());
    }

    @Override
    public void collection(Long dealerId) {
        if (dealerId == null) {
            throw ExceptionMessageUtil.getException(APP_DEALER_COLLECTION_ID_NULL);
        }
        UserDealer one = this.getOne(new LambdaQueryWrapper<UserDealer>()
                .eq(UserDealer::getUserId, StpUtil.getLoginIdAsLong())
                .eq(UserDealer::getDealerId, dealerId));
        if (one != null) {
            throw ExceptionMessageUtil.getException(APP_DEALER_ALREADY_COLLECTION);
        }
        one = new UserDealer();
        one.setUserId(StpUtil.getLoginIdAsLong());
        one.setDealerId(dealerId);
        this.save(one);
    }

    @Override
    public void cancelCollection(Long dealerId) {
        if (dealerId == null) {
            throw ExceptionMessageUtil.getException(APP_DEALER_COLLECTION_ID_NULL);
        }
        UserDealer one = this.getOne(new LambdaQueryWrapper<UserDealer>()
                .eq(UserDealer::getUserId, StpUtil.getLoginIdAsLong())
                .eq(UserDealer::getDealerId, dealerId));
        if (one == null) {
            throw ExceptionMessageUtil.getException(APP_DEALER_NOT_COLLECTION, dealerId);
        }
        this.removeById(one.getId());
    }
}
