package com.chervon.iot.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.iot.app.domain.dataobject.AppUserDevice;
import com.chervon.iot.app.domain.dto.device.AppUserNotIotDeviceDto;
import com.chervon.iot.app.domain.dto.device.AppUserOtherDeviceDto;
import com.chervon.iot.app.domain.vo.device.AppIotMinSortDeviceVo;
import com.chervon.iot.app.domain.vo.device.AppUserSortDeviceVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-18
 */
@Mapper
public interface AppUserDeviceMapper extends BaseMapper<AppUserDevice> {
    /**
     * 非iot集合正序
     *
     * @param appUserNotIotDeviceDto 参数
     * @return 返回值
     */
    List<AppUserSortDeviceVo> appUserNotIotDeviceList(AppUserNotIotDeviceDto appUserNotIotDeviceDto);

    /**
     * 其他用户绑定设备集合（除了非iot集合正序）
     *
     * @param appUserOtherDeviceDto 参数
     * @return 返回值
     */
    List<AppUserSortDeviceVo> appUserOtherDeviceList(AppUserOtherDeviceDto appUserOtherDeviceDto);

    /**
     * 当前用户最大拍序号
     *
     * @param userId 用户id
     * @return 最大排序号
     */
    Integer getMaxSort(@Param("userId") Long userId);

    /**
     * 查询最大序号的用户设备信息
     *
     * @param userId 用户id
     * @param cModel 模糊匹配的商品型号
     * @return 用户设备信息
     */
    List<AppUserDevice> selectMaxOrder(@Param("userId") Long userId, @Param("cModel") String cModel);
}
