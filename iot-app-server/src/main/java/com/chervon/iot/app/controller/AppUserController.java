package com.chervon.iot.app.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.domain.SingleInfoResp;
import com.chervon.common.core.utils.AesUtils;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.iot.app.domain.dto.AesEncryptDto;
import com.chervon.iot.app.domain.dto.user.*;
import com.chervon.iot.app.domain.vo.user.LoginUserResp;
import com.chervon.iot.app.service.AppUserService;
import com.chervon.usercenter.api.dto.PhoneInfoDto;
import com.chervon.usercenter.api.vo.UserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.regex.Pattern;

/**
 * App用户相关接口
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "App用户相关接口")
@RestController
@RequestMapping("/user")
@Validated
public class AppUserController {

    @Autowired
    private AppUserService appUserService;

    private final Pattern VALID_PATTERN = Pattern.compile("^(?=.*\\d)(?=.*[a-zA-Z]).{8,16}$");

    /**
     * 注册账户，添加用户，注册流程3
     * 不需要登录鉴权
     *
     * @param req
     * @return
     */
    @PostMapping("/register")
    @ApiOperation("用户邮箱注册")
    public LoginUserResp register(@Validated @RequestBody RegisterDto req, @RequestHeader("lang") String lang) {
        LoginUserResp register = appUserService.register(req, lang);
        return register;
    }


    /**
     * 校验用户填写注册邮箱格式及是否存在
     * @param email
     */
    @PostMapping("/email/check")
    @ApiOperation("校验用户填写注册邮箱")
    public void checkEmail(@RequestBody SingleInfoReq<String> email){
         appUserService.checkEmail(email.getReq());
    }


    /**
     * 用户注销
     *
     * @return 注销结果
     */
    @PostMapping("/cancel")
    @ApiOperation("用户注销")
    public Boolean cancelUser() {
        return appUserService.cancelUser();
    }

    /**
     * 登录
     * 不需要登录鉴权
     *
     * @param query
     * @return
     */
    @PostMapping("/login")
    @ApiOperation("登录")
    public LoginUserResp login(@Validated @RequestBody LoginDto query, @RequestHeader("lang") String lang) {
        LoginUserResp login = appUserService.login(query, lang);
        return login;
    }

    /**
     * 登出
     * 不需要登录鉴权
     *
     * @return
     */
    @PostMapping("/logout")
    @ApiOperation("登出")
    public void logout() {
        appUserService.logout();
    }

    /**
     * 注销用户
     *
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation("注销用户")
    public void delete() {
        appUserService.delete();
    }


    /**
     * 上传用户头像接口
     * @param req
     * @return
     */
    @PostMapping("/upload/photo")
    @ApiOperation("上传用户头像接口")
    public SingleInfoResp<String> uploadPhoto(@Validated @RequestBody PhotoDto req) {
        return new SingleInfoResp<>(appUserService.uploadPhoto(req));
    }

    /**
     * 获取登录用户信息
     *
     * @return
     */
    @PostMapping("/info")
    @ApiOperation("获取登录用户信息")
    public UserVo info() {
        return appUserService.info();
    }

    /**
     * 忘记密码，重置密码，忘记密码流程3
     * 不需要登录鉴权
     *
     * @param req
     * @return
     */
    @PostMapping("/resetPassword")
    @ApiOperation("忘记密码，重置密码")
    public void resetPassword(@Validated @RequestBody ResetPwdDto req) {
        appUserService.resetPassword(req);
    }

    /**
     * 修改密码，确认旧的密码，修改密码流程1
     *
     * @param req
     * @return
     */
    @PostMapping("/confirmPassword")
    @ApiOperation("修改密码，确认旧的密码")
    public SingleInfoResp<Boolean> confirmPassword(@Validated @RequestBody ConfirmPwdDto req) {
        return new SingleInfoResp<>(appUserService.confirmPassword(req));
    }

    /**
     * 修改密码，修改密码，修改密码流程2
     *
     * @param req
     * @return
     */
    @PostMapping("/editPassword")
    @ApiOperation("修改密码")
    public void editPassword(@Validated @RequestBody EditPwdDto req) {
        appUserService.editPassword(req);
    }

    /**
     * 更新用户信息
     *
     * @param req
     * @return
     */
    @PostMapping("/editInfo")
    @ApiOperation("更新用户信息")
    public UserVo editInfo(@Validated @RequestBody SaveDto req) {
        return appUserService.editInfo(req);
    }


    /**
     * 提供给压力测试的AES加密接口
     *
     * @param aesEncryptDto AES加密Dto
     * @return 加密后密码
     */
    @PostMapping("/aes/encrypted")
    @ApiOperation("提供给压力测试的AES加密接口")
    public SingleInfoResp<String> getEncryptPassword(@RequestBody @Validated AesEncryptDto aesEncryptDto) {
        // 校验密码复杂度：数字和字母
        String passwordStr = "ASWEEDdr2";
        if (!VALID_PATTERN.matcher(passwordStr).matches()) {
            log.error("VALID_PATTERN--> 不满足同时包含数字和字母");
        }
        // 秘钥
        String aes = aesEncryptDto.getAes();
        // 真实密码
        String sourceContent = aesEncryptDto.getRealPassword();
        String encrypted = AesUtils.encrypt(sourceContent, aes);
        return new SingleInfoResp<>(encrypted);
    }

    /**
     * 上报手机信息,分组使用
     *
     * @param phoneInfoDto:
     * @return void
     * <AUTHOR>
     * @date 10:59 2022/8/30
     **/
    @PostMapping("/report/phone")
    @ApiOperation("上报手机信息,分组使用")
    public void reportPhoneInfo(@Validated @RequestBody PhoneInfoDto phoneInfoDto) {
        appUserService.reportPhoneInfo(phoneInfoDto);
    }
}
