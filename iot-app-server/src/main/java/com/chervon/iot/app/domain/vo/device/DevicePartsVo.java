package com.chervon.iot.app.domain.vo.device;

import com.chervon.common.core.domain.MultiLanguageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2022/10/27 20:15
 */
@Data
public class DevicePartsVo implements Serializable {
    @ApiModelProperty("主键id")
    private Long id;
    @ApiModelProperty("配件名称")
    private MultiLanguageVo name;
    @ApiModelProperty("配件图标")
    private String partsIcon;
    @ApiModelProperty("总寿命(天)")
    private int totalLife;
    @ApiModelProperty("剩余寿命(天)")
    private int residualLife;
    @ApiModelProperty("配件短描述")
    private MultiLanguageVo shortDesc;
}
