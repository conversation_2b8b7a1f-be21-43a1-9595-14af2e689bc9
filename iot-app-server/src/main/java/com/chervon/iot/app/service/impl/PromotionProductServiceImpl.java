package com.chervon.iot.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.iot.app.domain.dataobject.promotion.PromotionProduct;
import com.chervon.iot.app.mapper.PromotionProductMapper;
import com.chervon.iot.app.service.PromotionProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PromotionProductServiceImpl extends ServiceImpl<PromotionProductMapper, PromotionProduct> implements PromotionProductService {

    @Override
    public List<PromotionProduct> selectByPromotionProductCategory(String category) {
        LambdaQueryWrapper<PromotionProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PromotionProduct::getProductCategory, category)
                .orderByAsc(PromotionProduct::getImageNo);
        return this.list(queryWrapper);
    }

}
