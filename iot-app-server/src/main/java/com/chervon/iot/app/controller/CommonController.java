package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.domain.SingleInfoResp;
import com.chervon.iot.app.domain.dto.user.EmailCodeDto;
import com.chervon.iot.app.domain.dto.user.PreSignedUrlDto;
import com.chervon.iot.app.domain.vo.KeyVo;
import com.chervon.iot.app.service.CommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/user")
@Validated
@Api(tags = "App服务通用接口")
public class CommonController {

    @Autowired
    private CommonService commonService;

    /**
     * 获取临时aes密码
     * 不需要登录鉴权
     *
     * @param req
     * @return
     */
    @PostMapping("/get/encrypt/secret")
    @ApiOperation("获取临时aes密码")
    public SingleInfoResp<String> getEncryptSecret(@Validated @RequestBody EmailCodeDto req) {
        return new SingleInfoResp<>(commonService.getEncryptSecret(req));
    }

    /**
     * 获取签名Url
     *
     * @return
     */
    @PostMapping("/get/signedUrl")
    @ApiOperation("获取签名Url")
    public SingleInfoResp<String> getPreSignedUrl(@Validated @RequestBody PreSignedUrlDto req) {
        return new SingleInfoResp<>(commonService.getPreSignedUrl(req));
    }

    @ApiOperation("根据上传S3的key，获取完整资源地址")
    @PostMapping("/get/s3Url")
    public String getS3Url(@RequestBody SingleInfoReq<String> req) {
        return commonService.getS3Url(req.getReq());
    }

    @ApiOperation("获取密钥")
    @PostMapping("/keys/retrieve")
    public KeyVo retrieveKeys(){
        return commonService.retrieveKeys();
    }

}
