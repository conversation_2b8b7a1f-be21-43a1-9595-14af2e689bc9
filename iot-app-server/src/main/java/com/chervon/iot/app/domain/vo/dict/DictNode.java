package com.chervon.iot.app.domain.vo.dict;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/3 15:07
 * @desc 描述
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictNode {

    @ApiModelProperty(value = "标签")
    private String label;

    @ApiModelProperty(value = "描述")
    private String description;

}
