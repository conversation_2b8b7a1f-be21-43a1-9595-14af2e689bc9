package com.chervon.iot.app.domain.vo.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022年12月8日
 */
@Data
public class DeviceRunDateVo implements Serializable {

	@ApiModelProperty("设备id")
	private String deviceId;

	@ApiModelProperty("总距离(单位M)对应功能Id：23")
	private Integer totalDistance;

	@ApiModelProperty("割草时间(单位秒)对应功能Id：22")
	private Integer totalMowingTime;

	@ApiModelProperty("平均速度（单位KM/H）")
	private Double averageSpeed;

	@ApiModelProperty("最大速度（单位KM/H）")
	private Double maxSpeed;


	@ApiModelProperty("割草面积（单位平方米）对应功能Id：25")
	private Integer totalMowingArea;

	@ApiModelProperty("平均割草面积（单位平方米/每小时）")
	private Double averageMowingArea;

	@ApiModelProperty("二氧化氮减排量（单位g）")
	private Double totalCo2EmissionReduced;

	@ApiModelProperty("记录创建时间")
	private Long createTime;

}
