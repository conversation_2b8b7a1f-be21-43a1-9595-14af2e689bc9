package com.chervon.iot.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.iot.app.domain.dataobject.promotion.RecommendedAccessory;
import com.chervon.iot.app.mapper.RecommendAccessoryMapper;
import com.chervon.iot.app.service.RecommendAccessoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RecommendAccessoryServiceImpl extends ServiceImpl<RecommendAccessoryMapper, RecommendedAccessory> implements RecommendAccessoryService {

    @Override
    public RecommendedAccessory selectAccessoryBySnCode(String snCode) {
        LambdaQueryWrapper<RecommendedAccessory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RecommendedAccessory::getProductSnCode, snCode);
        return this.getOne(queryWrapper);
    }
}
