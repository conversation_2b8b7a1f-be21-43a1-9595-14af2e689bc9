package com.chervon.iot.app.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.app.api.enums.BusinessTypeEnum;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.config.ExceptionMessageUtil;
import com.chervon.technology.api.RemoteOtaJobService;
import com.chervon.technology.api.dto.DeviceJobResultDto;
import com.chervon.technology.api.dto.ota.ComponentResultDto;
import com.chervon.technology.api.vo.ota.DeviceJobResultVo;
import com.chervon.technology.api.vo.ota.OtaHistoryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022-07-13
 */
@Api(tags = "升级相关接口")
@RestController
@RequestMapping("/ota")
public class IotOtaController {

    @DubboReference
    private RemoteOtaJobService remoteOtaJobService;

    /**
     * 获取设备升级历史
     *
     * @return
     */
    @ApiOperation("获取设备升级历史")
    @PostMapping("/history")
    public R<List<OtaHistoryVo>> getOtaHistory(@RequestBody SingleInfoReq<String> deviceId) {
        long userId = StpUtil.getLoginIdAsLong();
        List<OtaHistoryVo> otaHistoryVoList = remoteOtaJobService.getOtaHistory(deviceId.getReq(), userId);
        return R.ok(otaHistoryVoList);
    }

    /**
     * 获取设备总成升级状态
     *
     * @return/device/component/result
     */
    @ApiOperation("获取设备总成升级状态")
    @PostMapping("/device/component/result")
    public R<List<ComponentResultDto>> getDeviceComponentResult(@RequestBody DeviceJobResultDto deviceJobResultDto) {
        return R.ok(remoteOtaJobService.getDeviceComponentResult(deviceJobResultDto));

    }


    @ApiOperation("app端确认设备升级")
    @PostMapping("/device/upgrade/confirm")
    public void confirmDeviceUpgrade(@RequestBody DeviceJobResultDto deviceJobResultDto) {
        if(deviceJobResultDto.getUserId()==null || deviceJobResultDto.getUserId()==0L){
            deviceJobResultDto.setUserId(StpUtil.getLoginIdAsLong());
        }
        if (deviceJobResultDto.getUserId()==null || deviceJobResultDto.getUserId()==0L){
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_USER_IS_NOT_LOGIN);
        }
        if(CollectionUtils.isEmpty(deviceJobResultDto.getJobId())
                || StringUtils.isEmpty(deviceJobResultDto.getDeviceId())){
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_PARAM_NOT_PROVIDED);
        }
        if(Objects.isNull(deviceJobResultDto.getBusinessType())){
            deviceJobResultDto.setBusinessType(BusinessTypeEnum.EGO_CONNECT.getType());
        }
        remoteOtaJobService.confirmDeviceUpgrade(deviceJobResultDto);
    }
}
