package com.chervon.iot.app.controller;

import com.chervon.operation.api.RemoteAppVersionService;
import com.chervon.operation.api.vo.AppVersionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2022/11/21 17:25
 */
@Api(tags = "app 安卓版本接口")
@RestController
@Slf4j
@RequestMapping("/version/android")
public class AndroidVersionController {

    @DubboReference
    private RemoteAppVersionService remoteAppVersionService;

    @ApiOperation("查询最新版本")
    @PostMapping("latest")
    public AppVersionVo latest(HttpServletRequest request) {
        if (!StringUtils.equalsIgnoreCase(request.getHeader("appType"), "android")) {
            return new AppVersionVo();
        }
        return remoteAppVersionService.latest(1);
    }
}
