package com.chervon.iot.app.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * AppUserRn对象
 *
 * <AUTHOR>
 * @since 2022-09-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("app_user_rn")
@ApiModel(value = "AppUserRn对象", description = "")
public class AppUserRn extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备Id")
    private String deviceId;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("RN版本")
    private String rnVersion;

    public static final String ID = "id";

    public static final String DEVICE_ID = "device_id";

    public static final String USER_ID = "user_id";

    public static final String RN_VERSION = "rn_version";

    public static final String IS_DELETED = "is_deleted";

    public static final String CREATE_BY = "create_by";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_BY = "update_by";

    public static final String UPDATE_TIME = "update_time";

}
