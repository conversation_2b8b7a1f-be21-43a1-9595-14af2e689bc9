package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.R;
import com.chervon.iot.app.domain.dto.device.DeviceWarrantyQuestionnaireAddDto;
import com.chervon.iot.app.service.DeviceWarrantyQuestionnaireService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 设备维保调查问卷接口
 *
 * <AUTHOR>
 * @since 2022-08-30 17:21
 **/
@Api(tags = "设备维保调查问卷接口")
@RestController
@RequestMapping("/device/warranty/questionnaire")
public class DeviceWarrantyQuestionnaireController {
    @Resource
    private DeviceWarrantyQuestionnaireService deviceWarrantyQuestionnaireService;

    /**
     * 新增维保问卷结果
     *
     * @param dto 新增维保问卷结果dto
     */
    @PostMapping("/add")
    @ApiOperation("新增维保问卷填写结果")
    public R<?> add(@RequestBody @Validated DeviceWarrantyQuestionnaireAddDto dto) {
        deviceWarrantyQuestionnaireService.add(dto);
        return R.ok();
    }
}
