package com.chervon.iot.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedissonLockExecutor;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.common.CommonConstant;
import com.chervon.iot.app.config.ExceptionMessageUtil;
import com.chervon.iot.app.domain.dataobject.AppHelpFaq;
import com.chervon.iot.app.domain.dto.help.AppHelpFaqPageDto;
import com.chervon.iot.app.mapper.AppHelpFaqMapper;
import com.chervon.iot.app.service.AppHelpFaqService;
import com.chervon.operation.api.RemoteHelpFaqService;
import com.chervon.operation.api.vo.HelpFaqAppVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/29 11:23
 */
@Service
@Slf4j
public class AppHelpFaqServiceImpl extends ServiceImpl<AppHelpFaqMapper, AppHelpFaq> implements AppHelpFaqService {

    @DubboReference
    private RemoteHelpFaqService remoteHelpFaqService;

    @Autowired
    private LockTemplate lockTemplate;

    @Override
    public PageResult<HelpFaqAppVo> listPage(AppHelpFaqPageDto appHelpFaqPageDto) {
        PageResult<HelpFaqAppVo> res = remoteHelpFaqService.listPage(appHelpFaqPageDto.getSearchContent(), appHelpFaqPageDto.getPageNum(), appHelpFaqPageDto.getPageSize());
        if (!CollectionUtils.isEmpty(res.getList())) {
            handle(res.getList());
        }
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HelpFaqAppVo detail(Long helpFaqId) {
        if (helpFaqId == null) {
            log.warn("helpFaqId is null");
            return new HelpFaqAppVo();
        }
        HelpFaqAppVo detail = remoteHelpFaqService.detail(helpFaqId);
        if (detail.getHelpFaqId() == null) {
            log.warn("not found helpFaq id is {}", helpFaqId);
            return detail;
        }
        AppHelpFaq appHelpFaq = getOne(Wrappers.<AppHelpFaq>lambdaQuery()
                .eq(AppHelpFaq::getHelpFaqId, detail.getHelpFaqId())
                .eq(AppHelpFaq::getUserId, StpUtil.getLoginIdAsLong())
        );

        if (Objects.isNull(appHelpFaq)) {
            detail.setPraised(false);
            appHelpFaq = new AppHelpFaq();
            appHelpFaq.setHelpFaqId(detail.getHelpFaqId());
            appHelpFaq.setReadCount(1);
            appHelpFaq.setUserId(StpUtil.getLoginIdAsLong());
            this.save(appHelpFaq);
        } else {
            detail.setPraised(Objects.equals(appHelpFaq.getPraise(), CommonConstant.HELP_FAQ_PRAISE_STATUS_1));
            // 更新阅读量
            AppHelpFaq a = new AppHelpFaq();
            a.setId(appHelpFaq.getId());
            a.setReadCount(Objects.equals(appHelpFaq.getReadCount(), 0) ? 1 : appHelpFaq.getReadCount() + 1);
            updateById(a);
        }
        remoteHelpFaqService.increaseReadCount(helpFaqId);
        return detail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void praise(Long helpFaqId) {
        StopWatch stopWatch = new StopWatch();
        if (helpFaqId == null) {
            log.warn("helpFaqId is null");
            return;
        }
        stopWatch.start("lock");
        String lockKey =  "updateHelpFaqPraiseCount-" + helpFaqId;
        final LockInfo lockInfo = lockTemplate.lock(lockKey, 30000L, 5000L, RedissonLockExecutor.class);
        if (null == lockInfo) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_HELP_FAQ_PRAISING);
        }
        stopWatch.stop();
        // 获取锁成功，处理业务
        boolean praised = true;
        try {
            stopWatch.start("getOne");
            AppHelpFaq one = getOne(Wrappers.<AppHelpFaq>lambdaQuery()
                    .eq(AppHelpFaq::getHelpFaqId, helpFaqId)
                    .eq(AppHelpFaq::getUserId, StpUtil.getLoginIdAsLong())
            );
            stopWatch.stop();
            AppHelpFaq appHelpFaq = new AppHelpFaq();
            if (one == null) {
                appHelpFaq.setHelpFaqId(helpFaqId);
                appHelpFaq.setPraise(CommonConstant.HELP_FAQ_PRAISE_STATUS_1);
                appHelpFaq.setUserId(StpUtil.getLoginIdAsLong());
            } else {
                appHelpFaq.setId(one.getId());
                appHelpFaq.setPraise(Objects.equals(one.getPraise(), CommonConstant.HELP_FAQ_PRAISE_STATUS_1) ?
                        CommonConstant.HELP_FAQ_PRAISE_STATUS_0 : CommonConstant.HELP_FAQ_PRAISE_STATUS_1);
                praised = !Objects.equals(one.getPraise(), CommonConstant.HELP_FAQ_PRAISE_STATUS_1);
            }
            stopWatch.start("saveOrUpdate");
            saveOrUpdate(appHelpFaq);
            //更新点赞量
            remoteHelpFaqService.updateHelpFaqPraiseCount(helpFaqId,praised);
            stopWatch.stop();
        } finally {
            //释放锁
            stopWatch.start("releaseLock");
            lockTemplate.releaseLock(lockInfo);
            stopWatch.stop();
        }
        log.info("stop watch:{}", stopWatch.prettyPrint());
    }

    @Override
    public List<HelpFaqAppVo> recommend(Long helpFaqId) {
        List<HelpFaqAppVo> res = remoteHelpFaqService.recommend(helpFaqId);
        if (CollectionUtils.isEmpty(res)) {
            return new ArrayList<>();
        }
        handle(res);
        return res;
    }

    private void handle(List<HelpFaqAppVo> res) {
        List<AppHelpFaq> list = list(Wrappers.<AppHelpFaq>lambdaQuery()
                .in(AppHelpFaq::getHelpFaqId, res.stream().map(HelpFaqAppVo::getHelpFaqId).collect(Collectors.toList()))
        );
        Map<Long, List<AppHelpFaq>> collect = list.stream().collect(Collectors.groupingBy(AppHelpFaq::getHelpFaqId));
        res.forEach(e -> {
            List<AppHelpFaq> appHelpFaqs = collect.get(e.getHelpFaqId());
            if (!CollectionUtils.isEmpty(appHelpFaqs)) {
                e.setPraised(appHelpFaqs.stream().anyMatch(i -> i.getPraise() != null && i.getPraise() == 1 && i.getUserId() != null && i.getUserId().equals(StpUtil.getLoginIdAsLong())));
            }
        });
    }
}
