package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.iot.app.config.ExceptionMessageUtil;
import com.chervon.iot.app.config.IotAppCommonConstant;
import com.chervon.iot.app.service.ProductService;
import com.chervon.technology.api.dto.ProductDto;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.vo.ProductRpcVo;
import com.chervon.technology.api.vo.thingmodel.ProductModelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 产品相关
 *
 * <AUTHOR>
 * @date 2022-07-22
 */
@Api(tags = "产品相关接口")
@RestController
@RequestMapping("/product")
@Slf4j
public class ProductController {

    @Autowired
    private ProductService productService;

    /**
     * 分页获取产品信息
     *
     * @param productDto
     * @return
     */
    @ApiOperation("分页获取产品信息")
    @PostMapping("/list")
    public R<PageResult<ProductRpcVo>> list(@Validated @RequestBody ProductDto productDto) {
        PageResult<ProductRpcVo> list = productService.list(productDto);
        return R.ok(list);
    }

    /**
     * 根据产品信息获取产品详情
     *
     * @param productSnCode
     * @return
     */
    @ApiOperation("根据产品信息获取产品详情-参数产品snCode")
    @PostMapping("/detail")
    public R<ProductRpcVo> getProductDetail(@Validated @RequestBody SingleInfoReq<String> productSnCode) {
        return R.ok(productService.getProductDetail(productSnCode.getReq()));
    }

    /**
     * 根据PID获取产品详情
     *
     * @param pId
     * @return
     */
    @ApiOperation("根据产品信息获取产品详情-参数产品id")
    @PostMapping("/detail/pid")
    public R<ProductRpcVo> getProductDetailByPId(@Validated @RequestBody SingleInfoReq<Long> pId) {
        return R.ok(productService.getProductDetailByPId(pId.getReq()));
    }

    /**
     * 根据产品pid获取产品物模型
     *
     * @param pid
     * @return
     */
    @PostMapping("/get/model")
    @ApiOperation("根据产品pid获取产品物模型")
    public R<ProductModelVo> getProductModel(@Validated @RequestBody SingleInfoReq<Long> pid) {
        return R.ok(productService.getProductModel(pid.getReq()));
    }
}
