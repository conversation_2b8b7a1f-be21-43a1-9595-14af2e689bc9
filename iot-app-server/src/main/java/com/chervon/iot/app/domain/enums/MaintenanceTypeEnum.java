package com.chervon.iot.app.domain.enums;

import com.chervon.iot.app.config.ExceptionMessageUtil;
import com.chervon.technology.api.exception.TechnologyErrorCode;

/**
 * 维保类型枚举 1 截止日期 2 使用工时
 * <AUTHOR>
 * @date 2023-11-13
 */
public enum MaintenanceTypeEnum {
    /**
     * 维保类型枚举 1 截止日期 2 使用工时
     */
    NONE(0, "无维保"),
    DEADLINE_DATE(1, "截止日期维保"),
    WORKING_HOURS_USED(2, "使用工时维保");

    private int value;

    private String label;

    MaintenanceTypeEnum(int value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public int getValue() {
        return value;
    }

    /**
     * 根据匹配value的值获取Label
     *
     * @param value 枚举类型
     * @return 审批状态
     */
    public static String getLabelByValue(int value) {
        for (MaintenanceTypeEnum s : MaintenanceTypeEnum.values()) {
            if (value==(s.getValue())) {
                return s.getLabel();
            }
        }
        return "";
    }

    /**
     * 获取StatusEnum
     *
     * @param value 枚举类型
     * @return 审批状态
     */
    public static MaintenanceTypeEnum getStatusEnum(String value) {
        for (MaintenanceTypeEnum s : MaintenanceTypeEnum.values()) {
            if (value.equals(s.getValue())) {
                return s;
            }
        }
        throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_DEVICE_TYPE_NOT_EXIST);
    }
}
