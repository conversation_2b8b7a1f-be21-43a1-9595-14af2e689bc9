package com.chervon.iot.app.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024-06-24
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("device_info_sync_fail")
public class DeviceInfoSyncFail extends BaseDo {
    private static final long serialVersionUID = 1806474566585196618L;
    /**
     * 设备SN码
     */
    @ApiModelProperty("设备SN码")
    private String sn;

    /**
     * 用户SaleForceId
     */
    @ApiModelProperty("用户SaleForceId")
    private String sfUserId;

    /**
     * 异常信息
     */
    @ApiModelProperty("异常信息")
    private String msg;

}
