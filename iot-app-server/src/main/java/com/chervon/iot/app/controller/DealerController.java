package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.iot.app.domain.dto.dealer.DealerListDto;
import com.chervon.iot.app.service.UserDealerService;
import com.chervon.operation.api.vo.AppDealerVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/19 16:14
 */
@Api(tags = "供应商相关接口")
@RestController
@Slf4j
@RequestMapping("/dealer")
public class DealerController {

    @Autowired
    private UserDealerService userDealerService;

    @ApiOperation("获取经销商列表，传经纬度，查询距离中心300英里内，最近的5个点；不传经纬度，查询已关注的经销商")
    @PostMapping("list")
    public List<AppDealerVo> list(@RequestBody DealerListDto req) {
        return userDealerService.list(req);
    }

    @ApiOperation("获取经销商列表，传经纬度点、距离范围distance，查询距离经纬度点distance英里内的经销商")
    @PostMapping("list/v2")
    public List<AppDealerVo> list2(@RequestBody DealerListDto req) {
        return userDealerService.list2(req);
    }

    @ApiOperation("添加收藏")
    @PostMapping("collection")
    public void collection(@RequestBody SingleInfoReq<Long> req) {
        userDealerService.collection(req.getReq());
    }

    @ApiOperation("取消收藏")
    @PostMapping("cancelCollection")
    public void cancelCollection(@RequestBody SingleInfoReq<Long> req) {
        userDealerService.cancelCollection(req.getReq());
    }
}
