package com.chervon.iot.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.iot.app.domain.dataobject.DeviceInfo;
import com.chervon.iot.app.domain.dto.device.DeviceInfoAddDto;
import com.chervon.iot.app.domain.dto.device.DeviceInfoDetailDto;
import com.chervon.iot.app.domain.vo.device.PartsInfoVo;
import com.chervon.iot.app.domain.dto.device.UploadReceiptDto;
import com.chervon.iot.app.domain.vo.PreSignedUrlVo;
import com.chervon.iot.app.domain.vo.device.DeviceInfoVo;
import com.chervon.usercenter.api.vo.sf.SfWarrantyRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-31 20:19
 **/
public interface DeviceInfoService extends IService<DeviceInfo> {
    /**
     * 注册设备信息
     *
     * @param dto 注册设备信息接口
     */
    PartsInfoVo add(DeviceInfoAddDto dto);

    /**
     * 根据设备ID获取设备注册信息,不存在返回空
     *
     * @param deviceId 设备ID
     * @return 设备注册信息
     */
    DeviceInfo getByDeviceId(String deviceId);

    /**
     * 获取设备注册信息
     *
     * @param dto 设备Id
     * @return 设备注册信息
     */
    DeviceInfoVo detail(DeviceInfoDetailDto dto);

    /**
     * 设备是否已经注册过
     *
     * @param deviceId 设备Id
     * @return 设备是否已注册
     */
    Boolean ifDeviceAlreadyRegistered(String deviceId);

    /**
     * 上传收据信息
     *
     * @param dto 收据信息
     * @return 文件上传URL
     */
    PreSignedUrlVo uploadReceipt(UploadReceiptDto dto);

    /**
     * 根据deviceId删除设备信息
     *
     * @param deviceInfoDetailDto 设备ID
     */
    void deleteByDeviceId(DeviceInfoDetailDto deviceInfoDetailDto);

    /**
     * 判断设备是否注册过
     *
     * @param deviceId 设备id
     * @return 是否注册过
     */
    boolean deviceIsRegistered(String deviceId);

    /**
     * 根据sn删除质保
     * @param sn
     */
    void removeDeviceInfoBySn(String sn);

    /**
     * 根据deviceId删除质保
     * @param deviceId
     */
    void removeByDeviceId(String deviceId);

    /**
     * 校验SN的合法性以及是否已注册
     * @param sn 设备SN
     */
    void validateSn(String sn);

    /**
     * 将SF查询到的质保数据同步到IoT平台
     * @param sfWarrantyRecords SF查询到的质保数据
     */
    void syncDeviceFromCrm(List<SfWarrantyRecord> sfWarrantyRecords);
}
