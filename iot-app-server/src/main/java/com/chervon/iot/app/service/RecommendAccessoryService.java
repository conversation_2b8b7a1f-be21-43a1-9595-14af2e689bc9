package com.chervon.iot.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.iot.app.domain.dataobject.promotion.RecommendedAccessory;


/**
 * <AUTHOR>
 */
public interface RecommendAccessoryService extends IService<RecommendedAccessory> {

    /**
     * 根据SN Code查询推荐配件信息
     * @param snCode SN Code
     * @return 推荐配件信息
     */
    RecommendedAccessory selectAccessoryBySnCode(String snCode);
}
