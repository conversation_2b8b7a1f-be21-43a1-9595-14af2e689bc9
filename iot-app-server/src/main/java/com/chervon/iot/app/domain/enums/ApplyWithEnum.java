package com.chervon.iot.app.domain.enums;

import com.chervon.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 设备信息:用途
 *
 * <AUTHOR>
 * @since 2023-04-06 17:48
 **/
@AllArgsConstructor
@Getter
@Slf4j
public enum ApplyWithEnum {
    /**
     * Residential
     * industrial/Professional/Commercial
     */
    RESIDENTIAL("0", "Residential"),
    INDUSTRIAL_PROFESSIONAL_COMMERCIAL("1", "Industrial/Professional/Commercial");

    private String value;
    private String label;

    public static String getLabelByValue(String value) {
        if (StringUtils.isEmpty(value)) {
            log.error("ApplyWithEnum#getLabelByValue -> value is empty.");
            return null;
        }
        for (ApplyWithEnum applyWithEnum : ApplyWithEnum.values()) {
            if (applyWithEnum.getValue().equals(value)) {
                return applyWithEnum.getLabel();
            }
        }
        log.error("ApplyWithEnum#getLabelByValue -> no label match.");
        return null;
    }
}
