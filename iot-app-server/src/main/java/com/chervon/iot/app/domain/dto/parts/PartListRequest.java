package com.chervon.iot.app.domain.dto.parts;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 配件列表请求参数
 * <AUTHOR>
 * @date 2022/11/23 21:05
 */
@Data
@ApiModel(description = "配件列表请求参数")
public class PartListRequest {

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "产品Id")
    private Long productId;
}
