package com.chervon.iot.app.service;

import com.chervon.iot.app.domain.dto.RobotCheckEmailCodeDto;
import com.chervon.iot.app.domain.vo.RobotEmailCertificateVo;

/**
 * <AUTHOR>
 * @date 2023/7/27 19:54
 */
public interface RobotService {

    /**
     * 获取邮箱
     *
     * @param deviceId 设备id
     * @return 邮箱
     */
    RobotEmailCertificateVo getEmailByDeviceId(String deviceId);

    /**
     * 校验邮箱验证码
     *
     * @param req 请求参数
     */
    void checkEmailCode(RobotCheckEmailCodeDto req);
}
