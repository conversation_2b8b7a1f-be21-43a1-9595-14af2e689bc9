package com.chervon.iot.app.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.domain.SingleInfoResp;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.iot.app.config.AppProperties;
import com.chervon.iot.app.config.IotAppCommonConstant;
import com.chervon.iot.app.domain.dto.device.DeviceAlarmDto;
import com.chervon.iot.app.domain.dto.message.MessageSettingDto;
import com.chervon.iot.app.domain.vo.AppLangVo;
import com.chervon.iot.app.domain.vo.message.MessageSettingVo;
import com.chervon.iot.app.service.DeviceAlarmService;
import com.chervon.iot.app.service.UserSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户设置
 *
 * <AUTHOR>
 * @date 2022-07-22
 */
@Api(tags = "用户设置相关")
@RestController
@RequestMapping("/setting")
public class UserSettingController {

    @Autowired
    private UserSettingService userSettingService;
    @Autowired
    private DeviceAlarmService deviceAlarmService;
    @Autowired
    private AppProperties appLangProperties;

    /**
     * 设置智能开关状态
     *
     * @param
     * @return
     */
    @ApiOperation("设置智能开关状态")
    @PostMapping("/set/smart/switch")
    public R<Boolean> setSmartSwitch(@Validated @RequestBody SingleInfoReq<Boolean> req) {
        return R.ok(userSettingService.setSmartSwitch(req.getReq()));
    }

    /**
     * 获取智能开关的状态
     * @return 智能开关的状态
     */
    @ApiOperation("获取智能开关的状态")
    @PostMapping("/get/smart/switch")
    public R<SingleInfoResp<Boolean>> getSmartSwitch() {
        return R.ok(userSettingService.getSmartSwitch());
    }

    @ApiOperation("获取语言设置")
    @PostMapping("/language")
    public R<List<AppLangVo>> language() {
        List<AppLangVo> res = new ArrayList<>();
        appLangProperties.getLang().forEach(e -> {
            AppLangVo vo = new AppLangVo();
            vo.setType(e.getType());
            vo.setContent(e.getContent());
            res.add(vo);
        });
        return R.ok(res);
    }

    /**
     * 设置消息开关状态
     * @param
     * @return
     */
    @ApiOperation("设置消息开关状态")
    @PostMapping("/set/message/switch")
    public R<Boolean> setMessageSwitch(@Validated @RequestBody MessageSettingDto messageSetting) {
        return R.ok(userSettingService.setMessageSwitch(messageSetting));
    }

    /**
     * 获取消息开关状态
     * @return 消息开关状态
     */
    @ApiOperation("获取消息开关状态")
    @PostMapping("/get/message/switch")
    public R<MessageSettingVo> getMessageSwitch() {
        return R.ok(userSettingService.getMessageSwitch());
    }

    /**
     * 设置用户推送token
     *
     * @param token 用户token
     */
    @ApiOperation("设置用户推送token")
    @PostMapping("/set/push/token")
    public void setPushToken(@Validated @RequestBody SingleInfoReq<String> token) {
        userSettingService.setPushToken(token.getReq());
    }

    /**
     * APP用户修改语言设置
     * @param lang 多语言请求头
     */
    @ApiOperation("APP用户修改语言设置")
    @PostMapping("/set/language")
    public void setLanguage(@RequestHeader(IotAppCommonConstant.LANG) String lang) {
        userSettingService.setUserLanguage(StpUtil.getLoginIdAsLong(), lang);
    }

//***********************************翻车紧急联系人设置*************************************
    /**
     * 紧急联系人详情
     * @param
     * @return 紧急联系人详情
     */
    @ApiOperation("紧急联系人详情")
    @PostMapping("/rolloverEmergency/userDetail")
    public DeviceAlarmDto detail(@RequestParam("deviceId") String deviceId){
        Assert.hasText(deviceId, ErrorCode.PARAMETER_NOT_PROVIDED,"deviceId");
        final long userId = StpUtil.getLoginIdAsLong();
        Assert.isId(userId, ErrorCode.PARAMETER_ERROR,"userId");
        return deviceAlarmService.detail(deviceId,userId);
    }

    /**
     * 创建紧急联系人信息
     * @param
     * @return 结果
     */
    @ApiOperation("创建紧急联系人信息")
    @PostMapping("/rolloverEmergency/create")
    public Boolean create(@RequestBody DeviceAlarmDto requestDto){
        Assert.notNull(requestDto, ErrorCode.PARAMETER_NOT_PROVIDED, "requestDto");
        final long userId = StpUtil.getLoginIdAsLong();
        Assert.isId(userId, ErrorCode.PARAMETER_ERROR,"userId");
        requestDto.setUserId(userId);
        String language= LocaleContextHolder.getLocale().getLanguage();
        requestDto.setLanguage(language);
        return deviceAlarmService.create(requestDto);
    }

    /**
     * 编辑紧急联系人信息
     * @param
     * @return 结果
     */
    @ApiOperation("编辑紧急联系人信息")
    @PostMapping("/rolloverEmergency/edit")
    public Boolean edit(@RequestBody DeviceAlarmDto requestDto){
        Assert.notNull(requestDto, ErrorCode.PARAMETER_NOT_PROVIDED, "requestDto");
        final long userId = StpUtil.getLoginIdAsLong();
        Assert.isId(userId, ErrorCode.PARAMETER_ERROR,"userId");
        Assert.isId(requestDto.getId(), ErrorCode.PARAMETER_NOT_PROVIDED,"id");
        requestDto.setUserId(userId);
        String language= LocaleContextHolder.getLocale().getLanguage();
        requestDto.setLanguage(language);
        return deviceAlarmService.edit(requestDto);
    }
}
