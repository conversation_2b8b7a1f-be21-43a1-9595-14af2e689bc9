package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.ListInfoReq;
import com.chervon.common.core.domain.PageRequest;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.feedback.api.dto.AppFeedbackCommitDto;
import com.chervon.feedback.api.dto.AppFeedbackEditDto;
import com.chervon.feedback.api.dto.AppFeedbackReplyDto;
import com.chervon.feedback.api.dto.AppFeedbackReqDto;
import com.chervon.feedback.api.service.RemoteFeedbackService;
import com.chervon.feedback.api.vo.AppFeedbackDetailVo;
import com.chervon.feedback.api.vo.AppFeedbackPageVo;
import com.chervon.feedback.api.vo.AppFeedbackStatusVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/3/17 21:34
 */
@RestController
@RequestMapping("/feedback")
@Api(tags = "用户反馈相关接口")
public class FeedbackController {

    @DubboReference
    private RemoteFeedbackService remoteFeedbackService;

    @ApiOperation("用户反馈页面右上角是否显示小红点")
    @PostMapping("showRed")
    public boolean showRed() {
        return remoteFeedbackService.showRed();
    }

    @ApiOperation("提交用户反馈")
    @PostMapping("commit")
    public void commit(@RequestBody AppFeedbackCommitDto req) {
        remoteFeedbackService.commit(req);
    }

    @ApiOperation("反馈记录分页查询")
    @PostMapping("page")
    public PageResult<AppFeedbackPageVo> page(@RequestBody PageRequest req) {
        return remoteFeedbackService.page(req.getPageNum(), req.getPageSize());
    }

    @ApiOperation("批量删除")
    @PostMapping("delete")
    public void delete(@RequestBody ListInfoReq<Long> req) {
        remoteFeedbackService.delete(req.getInfo());
    }

    @ApiOperation("详情")
    @PostMapping("detail")
    public AppFeedbackDetailVo detail(@RequestBody SingleInfoReq<Long> req) {
        return remoteFeedbackService.detail(req.getReq());
    }

    @ApiOperation("提交回复")
    @PostMapping("reply")
    public void reply(@RequestBody AppFeedbackReplyDto req) {
        remoteFeedbackService.reply(req);
    }

    @ApiOperation("编辑")
    @PostMapping("edit")
    public void edit(@RequestBody AppFeedbackEditDto req) {
        Assert.notNull(req.getType(), ErrorCode.PARAMETER_NOT_PROVIDED, "type");
        Assert.notNull(req.getId(), ErrorCode.PARAMETER_NOT_PROVIDED, "feedbackId");
        remoteFeedbackService.edit(req);
    }

    @ApiOperation("判断是否有反馈")
    @PostMapping("hasFeedback")
    public AppFeedbackStatusVo hasFeedback(@RequestBody AppFeedbackReqDto req) {
        Assert.notNull(req.getType(), ErrorCode.PARAMETER_NOT_PROVIDED, "type");
        return remoteFeedbackService.hasFeedback(req);
    }

    @ApiOperation("关闭反馈")
    @PostMapping("close")
    public void closeFeedback(@RequestBody SingleInfoReq<Long> req) {
        Assert.notNull(req.getReq(), ErrorCode.PARAMETER_NOT_PROVIDED, "feedbackId");
        remoteFeedbackService.closeFeedback(req.getReq());
    }

}
