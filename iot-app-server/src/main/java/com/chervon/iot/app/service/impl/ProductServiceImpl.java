package com.chervon.iot.app.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.app.config.MultiLanguageUtil;
import com.chervon.iot.app.service.ProductService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.enums.AppShowEnum;
import com.chervon.operation.api.vo.cache.BrandCache;
import com.chervon.operation.api.vo.cache.CategoryCache;
import com.chervon.operation.api.vo.cache.ProductCache;
import com.chervon.technology.api.RemoteTechProductOperationService;
import com.chervon.technology.api.RemoteProductService;
import com.chervon.technology.api.dto.ProductDto;
import com.chervon.technology.api.vo.ProductRpcVo;
import com.chervon.technology.api.vo.thingmodel.ProductModelVo;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-07-22
 */
@Service
public class ProductServiceImpl implements ProductService {

    @DubboReference
    private RemoteProductService productService;

    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;

    @DubboReference
    private RemoteTechProductOperationService remoteTechProductOperationService;

    @Override
    public PageResult<ProductRpcVo> list(ProductDto productDto) {
        PageResult<ProductRpcVo> res = new PageResult<>(productDto.getPageNum(), productDto.getPageSize());
        //获取显示的品类-app类表顺序
        List<Long> appListOrderCategory = remoteOperationCacheService.getAppListOrderCategory();
        if (CollectionUtils.isEmpty(appListOrderCategory)) {
            return res;
        }
        //获取语言环境
        String language = LocaleContextHolder.getLocale().getLanguage();
        boolean isEn = StringUtils.equals(language, RemoteOperationCacheService.DEFAULT_LANGUAGE);
        List<ProductRpcVo> list = new ArrayList<>();
        //获取显示产品ID-app列表顺序
        List<Long> appListOrderProduct = remoteTechProductOperationService.listEGOReleasedProductIdsOrderByAppShowOrder(AppShowEnum.SHOW.getValue());;
        //获取缓存中 产品列表
        List<ProductCache> productCacheList = remoteOperationCacheService.listProducts(appListOrderProduct);
        //产品列表按照品类Id分组
        Map<Long, List<ProductCache>> collect = productCacheList.stream().collect(Collectors.groupingBy(ProductCache::getCategoryId));
        //查询需要显示的品类的详情列表
        List<CategoryCache> categoryCacheList = remoteOperationCacheService.listCategories(appListOrderCategory);
        Map<Long, CategoryCache> cMap = categoryCacheList.stream().collect(Collectors.toMap(CategoryCache::getId, Function.identity()));

        //组装返回参数
        appListOrderCategory.forEach(e -> {
            CategoryCache categoryCache = cMap.get(e);
            List<ProductCache> productCaches = collect.get(e);
            if (!CollectionUtils.isEmpty(productCaches)) {
                Map<Long, ProductCache> pMap = productCaches.stream().collect(Collectors.toMap(ProductCache::getId, Function.identity()));
                appListOrderProduct.forEach(i -> {
                    ProductCache productCache = pMap.get(i);
                    if (productCache != null) {
                        ProductRpcVo vo = new ProductRpcVo();
                        vo.setCategoryId(categoryCache.getId());
                        if (isEn) {
                            vo.setCategoryName(categoryCache.getDefaultName());
                        } else {
                            vo.setCategoryName(MultiLanguageUtil.getByLangCode(categoryCache.getNameLangCode(), language));
                        }
                        vo.setId(productCache.getId());
                        if (isEn) {
                            vo.setProductName(productCache.getDefaultName());
                        } else {
                            vo.setProductName(MultiLanguageUtil.getByLangCode(productCache.getNameLangCode(), language));
                        }
                        vo.setModel(productCache.getModel());
                        vo.setCommodityModel(productCache.getCommodityModel());
                        vo.setProductIcon(productCache.getUrl());
                        vo.setBrandId(productCache.getBrandId());
                        BrandCache brand = remoteOperationCacheService.getBrand(productCache.getBrandId());
                        if (isEn) {
                            vo.setBrandName(brand.getDefaultName());
                        } else {
                            vo.setBrandName(MultiLanguageUtil.getByLangCode(brand.getNameLangCode(), language));
                        }
                        vo.setProductType(productCache.getType());
                        vo.setProductSnCode(productCache.getSnCode());
                        vo.setNetworkModes(productCache.getNetworkModes());
                        vo.setDescription(productCache.getDescription());
                        vo.setQuestionTemplate(productCache.getQuestionTemplate());
                        vo.setIconType(productCache.getIconType());
                        vo.setBusinessType(productCache.getBusinessType());
                        list.add(vo);
                    }
                });
            }
        });
        res.setTotal(list.size());
        if (list.size() <= res.getPageSize()) {
            res.setList(list);
        } else {
            res.setList(ListUtil.page((int) res.getPageNum() - 1, (int) res.getPageSize(), list));
        }
        return res;
    }

    @Override
    public ProductRpcVo getProductDetail(String productSnCode) {
        ProductRpcVo rpc = productService.getProductIdBySnCode(productSnCode);
        if (rpc == null) {
            return new ProductRpcVo();
        }
        return getProductDetailByPId(rpc.getId());
    }

    @Override
    public ProductRpcVo getProductDetailByPId(Long pId) {
        String language = LocaleContextHolder.getLocale().getLanguage();
        boolean isEn = StringUtils.equals(language, RemoteOperationCacheService.DEFAULT_LANGUAGE);

        ProductCache productCache = remoteOperationCacheService.getProduct(pId);
        ProductRpcVo vo = new ProductRpcVo();
        vo.setCategoryId(productCache.getCategoryId());
        CategoryCache categoryCache = remoteOperationCacheService.getCategory(productCache.getCategoryId());
        if (isEn) {
            vo.setCategoryName(categoryCache.getDefaultName());
        } else {
            vo.setCategoryName(MultiLanguageUtil.getByLangCode(categoryCache.getNameLangCode(), language));
        }
        vo.setId(productCache.getId());
        vo.setProductName(MultiLanguageUtil.getByLangCode(productCache.getNameLangCode(), language));
        vo.setModel(productCache.getModel());
        vo.setCommodityModel(productCache.getCommodityModel());
        vo.setProductIcon(productCache.getUrl());
        vo.setBrandId(productCache.getBrandId());
        BrandCache brand = remoteOperationCacheService.getBrand(productCache.getBrandId());
        if (isEn) {
            vo.setBrandName(brand.getDefaultName());
        } else {
            vo.setBrandName(MultiLanguageUtil.getByLangCode(brand.getNameLangCode(), language));
        }
        vo.setProductType(productCache.getType());
        vo.setProductSnCode(productCache.getSnCode());
        vo.setNetworkModes(productCache.getNetworkModes());
        vo.setDescription(productCache.getDescription());
        vo.setQuestionTemplate(productCache.getQuestionTemplate());
        vo.setIconType(productCache.getIconType());
        vo.setBusinessType(productCache.getBusinessType());
        return vo;
    }

    @Override
    public ProductModelVo getProductModel(Long pid) {
        return productService.getProductModel(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), pid));
    }
}
