package com.chervon.iot.app.domain.vo.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 绑定成功之后的一些额外判断
 *
 * <AUTHOR>
 * @since 2022-09-01 15:53
 **/
@Data
public class AddUserDeviceBindVo implements Serializable {
    /**
     * 设备信息是否已经注册
     * 如果还没有注册设备信息，APP需要跳转到设备注册页面
     */
    @ApiModelProperty("设备信息是否已经注册")
    private Boolean isDeviceInfoRegistered;

    @ApiModelProperty("设备Id")
    private String deviceId;
}
