package com.chervon.iot.app.service;

import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.dto.ProductDto;
import com.chervon.technology.api.vo.ProductRpcVo;
import com.chervon.technology.api.vo.thingmodel.ProductModelVo;

/**
 * <AUTHOR>
 * @date 2022/6/26 12:57
 */
public interface ProductService {

    /**
     * 分页获取产品信息
     * @param productDto 产品
     * @return 产品分页信息
     */
    PageResult<ProductRpcVo> list(ProductDto productDto);

    /**
     * 获取产品详情
     * @param productSnCode 产品snCode
     * @return 产品详情
     */
    ProductRpcVo getProductDetail(String productSnCode);


    /**
     * 根据产品Pid获取产品信息
     * @param pId 产品Pid
     * @return 产品详情
     */
    ProductRpcVo getProductDetailByPId(Long pId);

    /**
     * 根据产品Id获取产品物模型
     * @param pid 产品id
     * @return 产品物模型
     */
    ProductModelVo getProductModel(Long pid);
}
