package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.R;
import com.chervon.iot.app.domain.dto.shortcutFunction.ShortcutFunctionDto;
import com.chervon.technology.api.RemoteShortcutFunctionService;
import com.chervon.technology.api.vo.ShortcutFunctionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2022/11/11 9:48
 */
@Api(tags = "快捷功能配置")
@RestController
@RequestMapping("/shortcut/function")
public class ShortcutFunctionController {
    @DubboReference(timeout = 120000, retries = 0)
    private RemoteShortcutFunctionService remoteShortcutFunctionService;
    @ApiOperation("快捷功能配置列表")
    @PostMapping("/list")
    public R<List<ShortcutFunctionVo>> list(@RequestBody ShortcutFunctionDto shortcutFunctionDto) {
        return R.ok(remoteShortcutFunctionService.list(shortcutFunctionDto.getDeviceId()));
    }
}
