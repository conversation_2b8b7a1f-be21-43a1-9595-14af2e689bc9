package com.chervon.iot.app.service;


import com.chervon.iot.app.domain.vo.dict.DictVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/3 14:51
 * @desc 描述
 */
public interface DictService {

    /**
     * 描述：查询字典值
     * @date 2024/7/3 15:18
     * @param lang 语种
     * @param dictNameList 字典名称列表
     * @return java.util.List<com.chervon.configuration.api.core.DictVo>
     **/
    List<DictVo> listByDictName(String lang, List<String> dictNameList);

}
