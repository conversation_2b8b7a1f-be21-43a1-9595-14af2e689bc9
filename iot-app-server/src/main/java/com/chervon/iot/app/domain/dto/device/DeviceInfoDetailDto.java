package com.chervon.iot.app.domain.dto.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-08-31 11:21
 **/
@Data
public class DeviceInfoDetailDto implements Serializable {
    /**
     * 设备Id(非主键)
     */
    @ApiModelProperty("设备Id(非主键)")
    @NotEmpty
    private String deviceId;
}
