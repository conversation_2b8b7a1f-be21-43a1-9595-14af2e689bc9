package com.chervon.iot.app.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/15 20:39
 */
@Data
@ApiModel(description = "地图数据请求入参")
public class DeviceMapDto implements Serializable {

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty("要查询的轨迹id集合")
    private List<String> pathIds;
}
