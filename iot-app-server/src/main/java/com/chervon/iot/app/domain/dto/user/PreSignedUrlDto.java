package com.chervon.iot.app.domain.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022-06-18
 */
@Data
public class PreSignedUrlDto {

    /**
     * 存储桶
     */
    @ApiModelProperty("后续没用了，只是为了支持前一个版本")
    String bucketName;

    /**
     * 存储key
     */
    @NotEmpty
    String key;

    /**
     * 过期时间，时间戳
     */
    Long expiration;

    /**
     * 是否上传文件预签名，true 上传， false 下载
     */
    @NotNull
    Boolean isPut;
}
