package com.chervon.iot.app.domain.dto.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-09-01 11:10
 **/
@Data
public class UploadReceiptDto implements Serializable {
    /**
     * 文件名称(注意需要加.后缀)
     */
    @ApiModelProperty("文件名称(注意需要加.后缀)")
    @NotEmpty(message = "fileName不能为空")
    String fileName;
    /**
     * 设备Id(非设备表主键)
     */
    @ApiModelProperty("设备Id(非设备表主键)")
    private String deviceId;
}
