package com.chervon.iot.app.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.iot.app.api.RemoteUserSettingService;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.api.vo.UserSettingBo;
import com.chervon.iot.app.domain.dataobject.UserSetting;
import com.chervon.iot.app.service.AppUserService;
import com.chervon.iot.app.service.UserSettingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-09-16 09:57
 **/
@Slf4j
@DubboService
public class RemoteUserSettingServiceImpl implements RemoteUserSettingService {
    @Resource
    private UserSettingService userSettingService;


    @Override
    public UserSettingBo get(Long userId) {
        UserSetting userSetting = userSettingService.getOne(
                new LambdaQueryWrapper<UserSetting>().eq(UserSetting::getUserId, userId));
        if (null == userSetting) {
            log.warn(String.format(AppErrorCode.APP_USER_SETTING_IS_NULL.getErrorMessage(), userId));
            return null;
        }
        return ConvertUtil.convert(userSetting, UserSettingBo.class);
    }

    @Override
    public Map<Long, UserSettingBo> listUserSettingBoMap(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new HashMap<>();
        }
        List<UserSetting> userSettings = userSettingService.list(new LambdaQueryWrapper<UserSetting>()
                .in(UserSetting::getUserId, userIds));
        if (CollectionUtils.isEmpty(userSettings)) {
            return new HashMap<>();
        }
        List<UserSettingBo> userSettingBos = ConvertUtil.convertList(userSettings, UserSettingBo.class);
        return userSettingBos.stream().collect(Collectors.toMap(UserSettingBo::getUserId, Function.identity()));
    }

}
