package com.chervon.iot.app.domain.dto.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-31 10:55
 **/
@Data
public class DeviceInfoAddDto implements Serializable {

    /**
     * 用户手动输入sn
     */
    @ApiModelProperty("sn")
    private String sn;

    /**
     * 电池包或充电器sn
     */
    @ApiModelProperty("电池包或充电器sn")
    private List<String> kitSnList;

    /**
     * 设备Id(非主键)
     */
    @ApiModelProperty("设备Id(非主键)")
    @NotEmpty
    private String deviceId;
    /**
     * 收据信息: 0 I have my receipt, 1 I lost my receipt, 2 product was a gift
     */
    @ApiModelProperty("收据信息: 0 I have my receipt, 1 I lost my receipt, 2 product was a gift")
    // 欧洲版本不传，北美传
    private String receiptInformation;
    /**
     * 收据文件地址Key(收据文件上传接口返回值)
     */
    @ApiModelProperty("收据文件地址Key(收据文件上传接口返回值)")
    private List<String> receiptFileKey;
    /**
     * 购买地点: 0 Lower''s, 1 Ace Hardware, 2 Amazon LLC, 3 Home Depot, Other other
     */
    @ApiModelProperty("购买地点: 0 Lower''s, 1 Ace Hardware, 2 Amazon LLC, 3 Home Depot, Other other")
    @NotEmpty
    private String purchasePlace;
    /**
     * 其他购买地点
     */
    @ApiModelProperty("其他购买地点")
    private String purchasePlaceOther;
    /**
     * 用途: 0 Residential, 1 industrial/Professional/Commercial, 2 Residential or Commercial
     */
    @ApiModelProperty("用途: 0 Residential, 1 industrial/Professional/Commercial, 2 Residential or Commercial")
    @NotEmpty
    private String applyWith;
    /**
     * 购买时间戳
     * APP需要将时间显示为MM-DD-YYYY形式
     */
    @ApiModelProperty("购买时间戳")
    @NotNull
    private Long purchaseTime;
    /**
     * 是否填写维保问卷: 0否 1是
     */
    @ApiModelProperty("是否填写维保问卷: 0否 1是")
    private Integer ifCheckedWarranty;
}
