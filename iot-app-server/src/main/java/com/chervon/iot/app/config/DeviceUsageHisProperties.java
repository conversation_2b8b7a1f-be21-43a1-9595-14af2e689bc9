package com.chervon.iot.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2024/9/19 14:10
 * @desc 描述
 */
@Component
@ConfigurationProperties(prefix = "device-usage-history")
@Data
@RefreshScope
public class DeviceUsageHisProperties {

    private List<AttributesIndex> product;

    @Data
    public static class AttributesIndex {
        private String snCode;
        private Map<String, String> workState;
        private Map<String, String> pointStatus;
        private Map<String, String> usageHistory;
        private Map<String, String> tracksHistory;
    }

}
