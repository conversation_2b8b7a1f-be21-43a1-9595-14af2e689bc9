package com.chervon.iot.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.iot.app.domain.dataobject.AppUserDevice;
import com.chervon.iot.app.domain.dataobject.DeviceShare;
import com.chervon.iot.app.domain.dto.share.AppDeviceAcceptDto;
import com.chervon.iot.app.domain.dto.share.AppDeviceShareDto;
import com.chervon.iot.app.domain.dto.share.DeviceShareAddDto;
import com.chervon.iot.app.domain.dto.share.DeviceShareSubDto;

import java.util.List;

/**
 * 设备分享服务
 * <AUTHOR>
 * @date 2024/8/1
 **/
public interface DeviceShareService extends IService<DeviceShare> {

    /**
     * 主用户分享设备
     * @param shareAddDto 设备ID+子账户邮箱
     */
    void share(DeviceShareAddDto shareAddDto);

    /**
     * 主用户分享设备 并且 邀请子用户注册
     * @param shareAddDto 设备ID+子账户邮箱
     */
    void shareAndInvite(DeviceShareAddDto shareAddDto);

    /**
     * 子用户确认接受设备分享
     * @param shareId 分享记录ID
     */
    void accept(Long shareId);

    /**
     * 主用户能分享的设备列表
     * @return list
     */
    List<AppDeviceShareDto> shareDeviceList();

    /**
     * 子用户收到的设备分享列表
     * @return list
     */
    List<AppDeviceAcceptDto> acceptDeviceList();

    /**
     * 主用户设备分享出去的子用户列表
     * @param deviceId 设备ID
     * @return list
     */
    List<DeviceShareSubDto> subList(String deviceId);

    /**
     * 主用户删除设备分享
     * @param shareId 分享记录ID
     */
    void masterRemoveShare(Long shareId);

    /**
     * 子用户删除设备分享
     * @param shareId 分享记录ID
     */
    void subRemoveShare(Long shareId);

    /**
     * 用户设备解绑后清空分享信息
     * @param userDevice 用户设备信息
     */
    void clearShare(AppUserDevice userDevice);

    /**
     * 用户注销后清空分享信息
     * @param userId 用户ID
     */
    void clearShareOfUser(Long userId);

    /**
     * 子用户待接受的设备分享个数
     * @return count
     */
    long pendingCount();

    /**
     * 设置用户设备绑定关系的分享类型
     * @param appUserDevice 用户和设备绑定关系
     * @param paired 是否配对
     * @param isSharingSupported 是否为主子账户
     */
    void setUserDeviceShareType(AppUserDevice appUserDevice,boolean paired,Boolean isSharingSupported);

    /**
     * 更新过期的分享记录状态
     */
    void expire();

    /**
     * 子用户注册成功后更新之前的设备分享
     * @param email  子用户邮箱
     * @param userId 子用户ID
     */
    void updateAfterRegister(String email, Long userId);
}

