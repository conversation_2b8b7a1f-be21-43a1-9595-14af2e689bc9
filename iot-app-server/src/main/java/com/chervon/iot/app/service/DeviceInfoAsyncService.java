package com.chervon.iot.app.service;

import com.chervon.iot.app.domain.dataobject.DeviceInfo;
import com.chervon.iot.app.domain.dto.device.DeviceInfoAddDto;

/**
 * <AUTHOR>
 * @since 2023-04-06 10:15
 **/
public interface DeviceInfoAsyncService {
    /**
     * 注册设备质保到SaleForce
     *
     * @param deviceInfo 设备信息Do
     * @param sn   设备sn
     */
    void registerToSaleForce(DeviceInfo deviceInfo, DeviceInfoAddDto deviceInfoAddDto, String sn, Long userId);

    /**
     * 同步用户质保
     *
     * @param userId 用户ID
     */
    void syncUserWarranty(Long userId);
}
