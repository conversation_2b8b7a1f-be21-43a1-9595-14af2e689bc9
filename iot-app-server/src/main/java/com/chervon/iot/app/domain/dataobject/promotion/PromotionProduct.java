package com.chervon.iot.app.domain.dataobject.promotion;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 促销产品实体
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_promotion_product")
public class PromotionProduct extends BaseDo implements Serializable {


    private static final long serialVersionUID = 5166272336906013134L;

    /**
     * 产品品类
     */
    @ApiModelProperty("产品品类")
    private String productCategory;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 产品型号
     */
    @ApiModelProperty("商品型号")
    private String commodityModel;

    /**
     * 产品图片编号
     */
    @ApiModelProperty("产品图片编号")
    private String imageNo;

    /**
     * 购买链接
     */
    @ApiModelProperty("购买链接")
    private String shopUrl;

    /**
     * 产品图片链接
     */
    @ApiModelProperty("产品图片链接")
    private String imageUrl;
    
}
