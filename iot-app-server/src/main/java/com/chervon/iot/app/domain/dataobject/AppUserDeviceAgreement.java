package com.chervon.iot.app.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022-08-30 11:41
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("app_user_device_agreement")
public class AppUserDeviceAgreement extends BaseDo {
    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 产品Id
     */
    private Long productId;
    /**
     * 协议Id(协议表主键Id)
     */
    private Long agreementId;
}
