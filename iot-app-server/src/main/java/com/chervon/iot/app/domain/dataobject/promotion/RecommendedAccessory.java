package com.chervon.iot.app.domain.dataobject.promotion;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 推荐产品实体
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_recommend_accessory")
public class RecommendedAccessory extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品类别
     */
    @ApiModelProperty("品类")
    private String category;

    /**
     * 商品型号
     */
    @ApiModelProperty("产品名称")
    private String commodityModel;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String model;

    /**
     * SN Code
     */
    @ApiModelProperty("SN Code")
    private String productSnCode;

    /**
     * 推荐配件商品型号
     */
    @ApiModelProperty("推荐配件商品型号")
    private String recommendedAccessory;

   
}
