package com.chervon.iot.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.iot.app.domain.dataobject.AppUserDeviceAgreement;
import com.chervon.iot.app.domain.dto.device.AppUserDeviceAgreementAddDto;
import com.chervon.iot.app.domain.dto.device.AppUserDeviceAgreementRemoveDto;
import com.chervon.iot.app.domain.dto.device.AppUserDeviceUnbindDto;
import com.chervon.iot.app.mapper.AppUserDeviceAgreementMapper;
import com.chervon.iot.app.service.AppDeviceAgreementService;
import com.chervon.iot.app.service.AppDeviceService;
import com.chervon.operation.api.RemoteDeviceAgreementService;
import com.chervon.operation.api.dto.AppDeviceAgreementListDto;
import com.chervon.operation.api.vo.AppDeviceAgreementListVo;
import com.chervon.operation.api.vo.AppDeviceAgreementVo;
import com.chervon.technology.api.vo.DeviceRpcVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.SetUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-08-30 19:50
 **/
@Service
@Slf4j
public class AppDeviceAgreementServiceImpl extends ServiceImpl<AppUserDeviceAgreementMapper, AppUserDeviceAgreement>
    implements AppDeviceAgreementService {
    @Resource
    private AppDeviceService appDeviceService;

    @DubboReference
    private RemoteDeviceAgreementService remoteDeviceAgreementService;

    @Override
    public void add(AppUserDeviceAgreementAddDto dto) {
        Long userId = StpUtil.getLoginIdAsLong();
        //先取消老版本的同意
        LambdaQueryWrapper<AppUserDeviceAgreement> wrapper = new LambdaQueryWrapper<AppUserDeviceAgreement>()
            .eq(AppUserDeviceAgreement::getUserId, userId)
            .eq(AppUserDeviceAgreement::getProductId, dto.getProductId());
        this.remove(wrapper);
        //绑定新同意关系
        List<AppUserDeviceAgreement> target = new ArrayList<>();
        for (Long agreementId : dto.getId()) {
            AppUserDeviceAgreement appUserDeviceAgreement = new AppUserDeviceAgreement();
            appUserDeviceAgreement.setAgreementId(agreementId);
            appUserDeviceAgreement.setUserId(userId);
            appUserDeviceAgreement.setProductId(dto.getProductId());
            target.add(appUserDeviceAgreement);
        }
        this.saveBatch(target);
    }

    @Override
    public void remove(AppUserDeviceAgreementRemoveDto dto) {
        // 删除当前用户与设备协议的同意数据
        Long userId = StpUtil.getLoginIdAsLong();
        LambdaQueryWrapper<AppUserDeviceAgreement> wrapper = new LambdaQueryWrapper<AppUserDeviceAgreement>()
            .eq(AppUserDeviceAgreement::getUserId, userId)
            .eq(AppUserDeviceAgreement::getProductId, dto.getProductId());
        this.remove(wrapper);
        // 解绑当前用户与所有dto.productId设备的绑定关系
        List<DeviceRpcVo> deviceRpcVos = appDeviceService.listBoundDevices();
        for (DeviceRpcVo deviceRpcVo : deviceRpcVos) {
            if (deviceRpcVo.getProductId().equals(dto.getProductId())) {
                AppUserDeviceUnbindDto appUserDeviceUnbindDto = new AppUserDeviceUnbindDto();
                appUserDeviceUnbindDto.setDeviceId(deviceRpcVo.getDeviceId());
                appDeviceService.unbind(appUserDeviceUnbindDto);
            }
        }
    }

    @Override
    public List<AppDeviceAgreementListVo> list(String lang, AppDeviceAgreementListDto dto) {
        List<AppDeviceAgreementVo> list = remoteDeviceAgreementService.listNewestAgreementByProductId(lang, dto);
        return ConvertUtil.convertList(list, AppDeviceAgreementListVo.class);
    }

    @Override
    public List<AppDeviceAgreementListVo> listNewNames(String lang, AppDeviceAgreementListDto dto) {
        List<AppDeviceAgreementVo> list = remoteDeviceAgreementService.listNewestAgreementByProductId(lang, dto);
        Long userId = StpUtil.getLoginIdAsLong();
        LambdaQueryWrapper<AppUserDeviceAgreement> wrapper = new LambdaQueryWrapper<AppUserDeviceAgreement>()
            .eq(AppUserDeviceAgreement::getProductId, dto.getProductId())
            .eq(AppUserDeviceAgreement::getUserId, userId)
            .select(AppUserDeviceAgreement::getAgreementId);
        List<AppUserDeviceAgreement> appUserDeviceAgreements = this.list(wrapper);
        // dbIds中使用的是AgreementId，是协议主键ID
        Set<Long> dbIds = appUserDeviceAgreements.stream().map(AppUserDeviceAgreement::getAgreementId)
            .collect(Collectors.toSet());
        // newIds中使用的是id，也是协议主键ID
        Set<Long> newIds = list.stream().map(AppDeviceAgreementVo::getId).collect(Collectors.toSet());
        // 如果新旧一致
        if (dbIds.size() == newIds.size() && SetUtils.isEqualSet(dbIds, newIds)) {
            return null;
        } else {
            return ConvertUtil.convertList(list, AppDeviceAgreementListVo.class);
        }
    }
}
