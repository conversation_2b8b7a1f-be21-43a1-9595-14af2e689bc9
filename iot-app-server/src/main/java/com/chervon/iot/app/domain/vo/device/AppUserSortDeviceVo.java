package com.chervon.iot.app.domain.vo.device;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date Created in 2022/9/23 15:49
 */
@Data
public class AppUserSortDeviceVo extends BaseDo implements Serializable {
    @ApiModelProperty("设备id")
    private String deviceId;
    @ApiModelProperty("排序号")
    private Integer sort;
    /**
     * 是否新添加1.是，null.否
     */
    private Integer ifNewAdd;
    /**
     * 用户Id
     */
    @ApiModelProperty("用户Id")
    private Long userId;

    /**
     * MAC地址
     */
    @ApiModelProperty("MAC地址")
    private String mac;

    /**
     * 是否注册:0没有注册 1已经注册
     */
    @ApiModelProperty("是否注册:0没有注册 1已经注册")
    private Integer status;
    @ApiModelProperty("用途,1:Residential, 2:industrial/Professional/Commercial")
    private String applyWith;

    /**
     * 购买地点, 1:Lower''s, 2:Ace Hardware, 3:Amazon LLC, 4:Home Depot, 5:other
     */
    @ApiModelProperty("购买地点, 1:Lower''s, 2:Ace Hardware, 3:Amazon LLC, 4:Home Depot, 5:other")
    private String purchasePlace;

    /**
     * 购买时间
     */
    @ApiModelProperty("购买时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime purchaseTime;

    /**
     * 收据信息,1:I have My Receipt,2:I Lost My Receipt, 3:Product Was a Gift
     */
    @ApiModelProperty("收据信息,1:I have My Receipt,2:I Lost My Receipt, 3:Product Was a Gift")
    private String receiptInformation;

    /**
     * 收据文件的key
     */
    @ApiModelProperty("收据文件的key")
    private String receiptFileKey;
    @ApiModelProperty("设备昵称")
    private String deviceNickName;
}
