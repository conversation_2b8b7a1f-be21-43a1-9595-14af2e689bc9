package com.chervon.iot.app.rpc;

import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.iot.app.api.RemoteDeviceInfoService;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.api.vo.DeviceInfoRpcVo;
import com.chervon.iot.app.domain.dataobject.DeviceInfo;
import com.chervon.iot.app.service.DeviceInfoService;
import com.chervon.technology.api.RemoteDeviceCodeService;
import com.chervon.technology.api.vo.DeviceCodeRpcVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022-11-15 18:19
 **/
@Slf4j
@DubboService
public class RemoteDeviceInfoServiceImpl implements RemoteDeviceInfoService {
    @Resource
    private DeviceInfoService deviceInfoService;

    @Override
    public DeviceInfoRpcVo getDeviceInfo(String deviceId) {
        DeviceInfo deviceInfo = deviceInfoService.getByDeviceId(deviceId);
        if (deviceInfo != null) {
            return ConvertUtil.convert(deviceInfo, DeviceInfoRpcVo.class);
        } else {
            log.warn(String.format(AppErrorCode.APP_DEVICE_INFO_NOT_REGISTERED.getErrorMessage(), deviceId));
            return null;
        }
    }

    @Override
    public void removeDeviceInfoBySn(String sn) {
        deviceInfoService.removeDeviceInfoBySn(sn);
    }
}
