package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.R;
import com.chervon.technology.api.dto.DeviceIdDto;
import com.chervon.technology.api.dto.DeviceRegisterDto;
import com.chervon.technology.api.todevice.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 提供给设备的接口
 *
 * <AUTHOR>
 * @date 2023/4/23 13:59
 */
@Api(tags = "设备相关接口，给设备提供接口")
@RestController
@RequestMapping("/device")
public class ToDeviceController {

    @DubboReference
    private RemoteToDeviceService remoteToDeviceService;

    /**
     * 设备验签
     *
     * @param dto 设备验签Dto
     * @return token
     */
    @ApiOperation(value = "设备验签")
    @PostMapping("/verify")
    public R<IotDeviceVerifyVo> verify(@Validated @RequestBody IotDeviceVerifyDto dto) {
        return R.ok(remoteToDeviceService.deviceVerify(dto));
    }

    /**
     * 设备注册,返回证书信息
     *
     * @param deviceRegisterDto 设备
     */
    @PostMapping("/register")
    @ApiOperation("设备注册,返回证书信息")
    public R<IotDeviceCertVo> register(@Validated @RequestBody DeviceRegisterDto deviceRegisterDto) {
        return R.ok(remoteToDeviceService.deviceRegister(deviceRegisterDto));
    }

    /**
     * 检查设备是否已注册
     *
     * @param deviceIdDto 设备Id
     */
    @PostMapping("/check/registered")
    @ApiOperation("检查设备是否已注册")
    public R<Boolean> checkRegistered(@Validated @RequestBody DeviceIdDto deviceIdDto) {
        return R.ok(remoteToDeviceService.checkRegistered(deviceIdDto));
    }

    /**
     * 时间同步
     *
     * @param deviceTime 设备时间
     * @return 设备时间Vo
     */
    @PostMapping("/timeservice")
    @ApiOperation("时间同步")
    public R<DeviceTimeVo> timeService(@Validated @RequestBody DeviceTimeDto deviceTime) {
        return R.ok(remoteToDeviceService.timeService(deviceTime));
    }

    /**
     * 获取流量情况
     *
     * @param deviceFlowDto 设备流量DTO
     * @return 设备列量Vo
     */
    @PostMapping("/flow")
    @ApiOperation("获取流量情况")
    public R<DeviceFlowVo> flow(@Validated @RequestBody DeviceFlowDto deviceFlowDto) {
        return R.ok(remoteToDeviceService.flow(deviceFlowDto));
    }

    /**
     * R项目上传地图文件
     *
     * @param file     上传的地图文件
     * @param deviceId 设备id
     * @param complete 是否是整图
     * @param md5      摘要
     * @return 地图信息Vo
     */
    @ApiOperation("R项目上传地图文件")
    @PostMapping("/map/upload")
    public R<Void> uploadMap(@RequestParam(value = "file") MultipartFile file
            , @RequestParam(value = "deviceId") String deviceId
            , @RequestParam(value = "complete") Boolean complete
            , @RequestParam(value = "md5") String md5
            , @RequestHeader(value = "token",required = false) String token) throws Exception {
        if(token == null){
            token="app";
        }
        remoteToDeviceService.uploadMap(new DeviceMapUploadDto(deviceId, complete, md5,token), file);
        return R.ok();
    }
}
