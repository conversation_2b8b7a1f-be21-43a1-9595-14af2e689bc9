package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.R;
import com.chervon.iot.app.service.DeviceInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 给压测提供使用，没有DB操作的空接口
 *
 * <AUTHOR>
 * @since 2022-10-20 10:09
 **/
@RestController
@RequestMapping("/test")
@Validated
@Api(tags = "给压测提供使用，没有DB操作的空接口")
public class StressTestController {

    @Resource
    private DeviceInfoService deviceInfoService;

    /**
     * 获取当前时间戳
     *
     * @return 当前时间戳字符串
     */
    @PostMapping("/timestamp")
    @ApiOperation("获取当前时间戳")
    public R<String> timestamp() {
        return R.ok(String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 获取字符串
     *
     * @return 获取字符串
     */
    @PostMapping("/ping")
    @ApiOperation("获取字符串")
    public R<String> hello() {
        return R.ok("pong");
    }

}
