package com.chervon.iot.app.domain.dto.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import java.io.Serializable;

/**
 * 设备短信息，设备中不会变动的信息
 * <AUTHOR>
 * @date 20230215
 */
@Data
public class DeviceShortInfo implements Serializable {

    private static final long serialVersionUID = -4610522484066143542L;

    @ApiModelProperty("设备Id")
    private String deviceId;

    @ApiModelProperty("设备sn")
    private String sn;

    @ApiModelProperty("设备所属产品Id")
    private Long productId;

    @ApiModelProperty("设备MAC")
    private String mac;

    @ApiModelProperty("设备类型")
    private String productType;

    @ApiModelProperty("设备排序")
    private Integer sort;

    @ApiModelProperty("设备昵称")
    private String nickName;

    @ApiModelProperty("是否可被分享，0:老设备不可分享，1:主用户可分享，2:子用户不可再分享")
    private Integer shareType;

    @ApiModelProperty("商品型号Model#")
    private String commodityModel;

    @ApiModelProperty("产品型号")
    private String model;

    @ApiModelProperty("所属品类ID")
    private String categoryId;

    @ApiModelProperty("品类名称")
    private String categoryName;
}
