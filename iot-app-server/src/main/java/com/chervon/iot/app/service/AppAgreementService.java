package com.chervon.iot.app.service;

import com.chervon.iot.app.domain.vo.agreement.AppAgreedAgreementVo;
import com.chervon.operation.api.vo.AppAgreementVo;

/**
 * <AUTHOR>
 * @date 2022/9/2 16:48
 */
public interface AppAgreementService {

    /**
     * 查看同意过的最新版本版本
     *
     * @param type 协议类型
     * @return 最新协议内容
     */
    AppAgreedAgreementVo agreed(String type);

    /**
     * 取消app协议授权
     */
    void withdraw();
    /**
     * 取消app协议授权-by userId
     */
    void withdraw(Long userId);

    /**
     * 查询最新版本的app协议
     *
     * @param type 协议类型
     * @return 最新协议
     */
    AppAgreementVo latest(String type);

    /**
     * 根据用户邮箱判断是否同意最新app协议
     *
     * @param email 用户邮箱
     * @return Boolean
     */
    boolean checkAgree(String email);

    /**
     * 同意协议
     *
     * @param userId    用户id
     * @param userEmail 用户邮箱
     */
    void agreeLatest(Long userId, String userEmail);
}
