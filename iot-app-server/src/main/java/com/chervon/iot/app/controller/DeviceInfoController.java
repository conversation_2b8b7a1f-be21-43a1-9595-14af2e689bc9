package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.iot.app.domain.dto.device.DeviceInfoAddDto;
import com.chervon.iot.app.domain.dto.device.DeviceInfoDetailDto;
import com.chervon.iot.app.domain.dto.device.UploadReceiptDto;
import com.chervon.iot.app.domain.vo.PreSignedUrlVo;
import com.chervon.iot.app.domain.vo.device.DeviceInfoVo;
import com.chervon.iot.app.domain.vo.device.PartsInfoVo;
import com.chervon.iot.app.service.DeviceInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 设备信息注册相关接口
 *
 * <AUTHOR>
 * @since 2022-08-31 10:53
 **/
@Api(tags = "设备信息注册相关接口")
@RestController
@RequestMapping("/device/info")
public class DeviceInfoController {
    @Resource
    private DeviceInfoService deviceInfoService;

    /**
     * 设备注册信息
     *
     * @param dto 设备信息dto
     * @return 注册结果
     */
    @ApiOperation("设备注册信息")
    @PostMapping("/add")
    public R<PartsInfoVo> add(@RequestBody @Validated DeviceInfoAddDto dto) {
        PartsInfoVo partsInfoVo = deviceInfoService.add(dto);
        return R.ok(partsInfoVo);
    }

    /**
     * 获取设备注册信息
     *
     * @param dto 设备Id
     * @return 注册信息
     */
    @ApiOperation("获取设备注册信息")
    @PostMapping("/detail")
    public R<DeviceInfoVo> detail(@RequestBody @Validated DeviceInfoDetailDto dto) {
        return R.ok(deviceInfoService.detail(dto));
    }

    /**
     * 上传文件到私有存储桶
     *
     * @param uploadReceiptDto 收据文件信息
     * @return 文件key+预签名地址
     */
    @ApiOperation("上传文件到私有存储桶")
    @PostMapping("/upload")
    public R<PreSignedUrlVo> uploadPhoto(@Validated @RequestBody UploadReceiptDto uploadReceiptDto) {
        return R.ok(deviceInfoService.uploadReceipt(uploadReceiptDto));
    }

    /**
     * 根据设备ID删除注册信息
     * @param deviceInfoDetailDto 设备ID
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperation("根据设备ID删除注册信息")
    public R<?> delete(@RequestBody @Validated DeviceInfoDetailDto deviceInfoDetailDto) {
        deviceInfoService.deleteByDeviceId(deviceInfoDetailDto);
        return R.ok();
    }

    /**
     * 校验SN的合法性以及是否已注册
     * @param sn 设备SN
     */
    @ApiOperation("校验SN的合法性以及是否已注册")
    @PostMapping("/validateSn")
    public void validateSn(@Validated @RequestBody SingleInfoReq<String> sn) {
        deviceInfoService.validateSn(sn.getReq());
    }
}
