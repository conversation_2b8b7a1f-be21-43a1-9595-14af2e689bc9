<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.chervon</groupId>
    <artifactId>chervon-common-bom</artifactId>
    <version>1.1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>chervon-common-bom</name>

    <description>
        chervon-common-bom common依赖项
    </description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <spring.profiles.active>dev</spring.profiles.active>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-security</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-satoken</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-log</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-excel</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-redis</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- web服务 -->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-web</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 数据库服务 -->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-mybatis</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-job</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-dubbo</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-elasticsearch</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-seata</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-oss</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-idempotent</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-mail</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-i18n</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-swagger</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-mongodb</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-sso</artifactId>
                <version>${project.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>User Porject Release</name>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>User Porject Snapshot</name>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-snapshots/</url>
            <uniqueVersion>true</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>
</project>
