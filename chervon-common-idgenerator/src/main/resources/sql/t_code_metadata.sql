SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `t_code_meta`;
CREATE TABLE `t_code_meta` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `segment_len` bigint(20) NOT NULL COMMENT '数据段长度',
  `issued` bigint(20) NOT NULL COMMENT '已发放的',
  `key` varchar(100) COLLATE utf8_bin NOT NULL COMMENT '键',
  `is_reset` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否重置',
  `expired_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '过期时间(重置时使用)',
  `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_key` (`key`) USING BTREE COMMENT '键索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='序列生成器元数据';

SET FOREIGN_KEY_CHECKS = 1;
