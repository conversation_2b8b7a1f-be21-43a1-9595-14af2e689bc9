package com.chervon.idgenerator.config;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

/**
 * 配置文件 减少用户配置，使用@EnableLeafCode直接启用
 * @date 2022/9/11
 */
//@Configuration
@ComponentScan(basePackages = "com.chervon.idgenerator")
@EnableConfigurationProperties({LeafCodeProperties.class})
public class LeafCodeConfiguration {

}
