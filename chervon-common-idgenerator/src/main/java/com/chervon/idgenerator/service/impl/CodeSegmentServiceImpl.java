package com.chervon.idgenerator.service.impl;

import com.chervon.idgenerator.dao.CodeMetadataDao;
import com.chervon.idgenerator.entity.CodeMetadata;
import com.chervon.idgenerator.exception.GenerateCodeException;
import com.chervon.idgenerator.service.CodeSegmentService;
import com.chervon.idgenerator.entity.CodeSegment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;

/**
 *
 * @date 2019/9/3 16:49
 */
@Service
public class CodeSegmentServiceImpl implements CodeSegmentService {

    @Autowired
    private CodeMetadataDao codeMetadataDao;

    /**
     * 请求Code资源块
     */
    @Transactional(rollbackFor = Throwable.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
    public CodeSegment applySegment(String key) {
        CodeMetadata codeMetadata = applySegmentPreProcess(key);
        if (codeMetadata != null) {
            return createSegment(codeMetadata);
        }
        return applySegment(key);
    }

    /**
     * 请求新的编码块
     *
     * @param key
     * @param segment
     * @return
     */
    @Transactional(rollbackFor = Throwable.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
    public CodeSegment applySegment(String key, CodeSegment segment) {
        CodeMetadata codeMetadata = applySegmentPreProcess(key);
        if (codeMetadata != null) {
            return resetSegment(segment, codeMetadata);
        }
        return applySegment(key, segment);
    }

    /**
     * 请求编码块前置处理
     *
     * @param key
     * @return
     */
    private CodeMetadata applySegmentPreProcess(String key) {
        CodeMetadata codeMetadata = codeMetadataDao.getForUpdate(key);
        if (codeMetadata == null) {
            throw new GenerateCodeException("CodeMetadata is not set! key:" + key);
        }
        if (codeMetadata.getIsReset() && codeMetadata.getExpiredTime() < Instant.now().toEpochMilli()) {
            CodeMetadata issuedToZeroMetadata = new CodeMetadata()
                    .setVersion(codeMetadata.getVersion())
                    .setId(codeMetadata.getId());
            codeMetadataDao.issuedToZero(issuedToZeroMetadata);
            codeMetadata = codeMetadataDao.getForUpdate(key);
            codeMetadata.setExpiredTime(getExpiredTime());
        }
        int effectRow = applySegment(codeMetadata);
        if (effectRow > 0) {
            return new CodeMetadata()
                    .setId(codeMetadata.getId())
                    .setIssued(codeMetadata.getIssued())
                    .setExpiredTime(codeMetadata.getExpiredTime())
                    .setSegmentLen(codeMetadata.getSegmentLen())
                    .setIsReset(codeMetadata.getIsReset())
                    .setKey(codeMetadata.getKey());
        }
        return null;
    }

    /**
     * 请求新的编码块
     *
     * @param codeMetadata
     * @return
     */
    private int applySegment(CodeMetadata codeMetadata) {
        return codeMetadataDao.applySegment(codeMetadata);
    }

    /**
     * 获取过期时间
     *
     * @return
     */
    private long getExpiredTime() {
        //当前日期+1天的时间戳
        return LocalDate.now().plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 重置编码块
     *
     * @param segment
     * @param codeMetadata
     * @return
     */
    private CodeSegment resetSegment(CodeSegment segment, CodeMetadata codeMetadata) {
        segment.reset(codeMetadata);
        return segment;
    }

    /**
     * 创建新的数据段
     *
     * @param codeMetadata 上下文
     * @return
     */
    private CodeSegment createSegment(CodeMetadata codeMetadata) {
        return new CodeSegment(codeMetadata);
    }
}
