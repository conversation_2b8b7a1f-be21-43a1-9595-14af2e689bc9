package com.chervon.idgenerator.dao;

import com.chervon.common.core.utils.JdbcTemplateHolder;
import com.chervon.idgenerator.entity.CodeMetadata;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 *
 * @date 2022/09/11
 */
@Slf4j
@Repository
public class CodeMetadataDao {
    /**
     * 获取编码元数据
     *
     * @param key 键名称
     * @return
     */
    public CodeMetadata getForUpdate(String key) {
        String sql = "SELECT `id`, `segment_len` as segmentLen, `issued`, `key`, `is_reset` as isReset, `expired_time` as expiredTime, `version` FROM `t_code_meta` where `key` = ? FOR UPDATE";
        CodeMetadata codeMetadata = JdbcTemplateHolder.getInstance().queryForObject(sql, (resultSet, i) -> new CodeMetadata()
                .setId(resultSet.getLong("id"))
                .setSegmentLen(resultSet.getLong("segmentLen"))
                .setIssued(resultSet.getLong("issued"))
                .setKey(resultSet.getString("key"))
                .setIsReset(resultSet.getBoolean("isReset"))
                .setExpiredTime(resultSet.getLong("expiredTime"))
                .setVersion(resultSet.getLong("version")), key);
        return codeMetadata;
    }

    /**
     * 发放序列归零
     *
     * @param codeMetadata 元数据
     * @return
     */
    public int issuedToZero(CodeMetadata codeMetadata) {
        String sql = "UPDATE `t_code_meta` SET `issued` = 0 ,`version` = `version`+1 WHERE `id` = ? and `version` = ?";
        return JdbcTemplateHolder.getInstance().update(sql, codeMetadata.getId(), codeMetadata.getVersion());
    }

    /**
     * 申请一段数据
     *
     * @param metadata 元数据
     * @return
     */
    public int applySegment(CodeMetadata metadata) {
        String sql = "UPDATE `t_code_meta` SET `issued` = `issued`+`segment_len`,`expired_time` = ?,`version` = `version`+1 WHERE `id` = ? and `version` = ?";
        return JdbcTemplateHolder.getInstance().update(sql, metadata.getExpiredTime(), metadata.getId(), metadata.getVersion());
    }

    /**
     * 查询是否存在表
     *
     * @return
     */
    public int hasTable() {
        String sql = "select count(1) as count from information_schema.TABLES where TABLE_SCHEMA=(select database()) and `table_name` = 't_code_meta'";
        return JdbcTemplateHolder.getInstance().queryForObject(sql, (resultSet, i) -> resultSet.getInt("count"));
    }

    /**
     * 键的源数据个数
     *
     * @param key 键
     * @return 个数
     */
    public int count(String key) {
        String sql = "SELECT count(*) as count from `t_code_meta` WHERE `key` = ? ";
        return JdbcTemplateHolder.getInstance().queryForObject(sql, (resultSet, i) -> resultSet.getInt("count"), key);
    }

    /**
     * 创建编码源数据表
     */
    public void createTable() {
        String sql = "CREATE TABLE `t_code_meta`  (\n" +
                "  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',\n" +
                "  `segment_len` bigint(20) NOT NULL COMMENT '数据段长度',\n" +
                "  `issued` bigint(20) NOT NULL COMMENT '已发放的',\n" +
                "  `key` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '键',\n" +
                "  `is_reset` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否重置',\n" +
                "  `expired_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '过期时间(重置时使用)',\n" +
                "  `version` bigint(20) NOT NULL DEFAULT 0 COMMENT '版本号',\n" +
                "  PRIMARY KEY (`id`) USING BTREE,\n" +
                "  UNIQUE INDEX `idx_key`(`key`) USING BTREE COMMENT '键索引'\n" +
                ") ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '序列生成器元数据' ROW_FORMAT = DYNAMIC;";
        JdbcTemplateHolder.getInstance().execute(sql);
    }

    /**
     * 创建源数据
     *
     * @param codeMetadata
     * @return
     */
    public int createMetadata(CodeMetadata codeMetadata) {
        String sql = "INSERT INTO `t_code_meta`(`segment_len`, `issued`, `key`, `is_reset`) VALUES (?, 0, ?, ?)";
        return JdbcTemplateHolder.getInstance().update(sql, codeMetadata.getSegmentLen(), codeMetadata.getKey(), codeMetadata.getIsReset());
    }

}
