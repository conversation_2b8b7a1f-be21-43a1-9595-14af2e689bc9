package com.chervon.idgenerator.util;

import com.chervon.idgenerator.annotation.GenerateId;
import com.chervon.idgenerator.config.GenerateProperties;
import com.chervon.idgenerator.config.LeafCodeProperties;
import com.chervon.idgenerator.entity.CodeMetadata;
import com.chervon.idgenerator.enums.IdStrategy;
import com.chervon.idgenerator.generator.impl.SegmentIdGenerator;
import com.chervon.idgenerator.generator.impl.SnowFlakeIdGenerator;
import com.chervon.idgenerator.service.CodeMetadataService;
import org.springframework.util.StringUtils;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 
 * @date 2022/09/11
 */
public class IdUtils {
    private static class Inner {
        private static SegmentIdGenerator segmentIdGenerator;
        private static SnowFlakeIdGenerator snowFlakeIdGenerator;
        private static CodeMetadataService codeMetadataService;
        private static LeafCodeProperties leafCodeProperties;

        static {
            segmentIdGenerator = (SegmentIdGenerator) CodeGeneratorApplicationContextHolder.getCtx().getBean("segmentIdGenerator");
            snowFlakeIdGenerator = CodeGeneratorApplicationContextHolder.getCtx().getBean(SnowFlakeIdGenerator.class);
            codeMetadataService = CodeGeneratorApplicationContextHolder.getCtx().getBean(CodeMetadataService.class);
            leafCodeProperties = CodeGeneratorApplicationContextHolder.getCtx().getBean(LeafCodeProperties.class);
        }
    }

    private final static Map<String, String> KEY_CACHE = new ConcurrentHashMap<>();
    private final static Map<Class<?>, IdStrategy> ID_STRATEGY_MAP = new ConcurrentHashMap<>();
    private final static Map<Class<?>, String> ID_GENERATOR_KEY_MAP = new ConcurrentHashMap<>();

    /**
     * 生成ID
     * <p>
     * 只支持分段取ID
     *
     * @param generatorKey 序列唯一KEY
     * @return new ID
     */
    private static Long segmentNext(String generatorKey) {
        String key = KEY_CACHE.get(generatorKey);
        if (key == null) {
            KEY_CACHE.put(generatorKey, String.format("key:{%s}", generatorKey));
            key = KEY_CACHE.get(generatorKey);
        }
        return Inner.segmentIdGenerator.generator(key);
    }

    /**
     * 生成ID
     *
     * @return new ID
     */
    public static Long snowFlakeNext() {
        return Inner.snowFlakeIdGenerator.generator();
    }

    /**
     * 获取ID
     *
     * @param clazz 与数据库对应的实体
     * @return new ID
     */
    public static Long next(Class<?> clazz) {
        IdStrategy strategy = ID_STRATEGY_MAP.get(clazz);
        //如果有缓存，直接处理
        if (null != strategy) {
            if (strategy == IdStrategy.SEGMENT) {
                return segmentNext(ID_GENERATOR_KEY_MAP.get(clazz));
            } else {
                return snowFlakeNext();
            }
        }

        synchronized (IdUtils.class) {
            strategy = ID_STRATEGY_MAP.get(clazz);
            if (null != strategy) {
                return next(clazz);
            }

            GenerateProperties properties = buildGenerateProperties(clazz);

            generatorSelect(clazz, properties);
        }
        return next(clazz);
    }

    /**
     * 生成器选择
     *
     * @param clazz      目标类型
     * @param properties 生成器属性
     */
    private static void generatorSelect(Class<?> clazz, GenerateProperties properties) {
        switch (properties.getStrategy()) {
            case SEGMENT:
                verifySegmentMetaData(properties);
                ID_STRATEGY_MAP.put(clazz, IdStrategy.SEGMENT);
                ID_GENERATOR_KEY_MAP.put(clazz, properties.getKey());
                break;
            case SNOW_FLAKE:
                ID_STRATEGY_MAP.put(clazz, IdStrategy.SNOW_FLAKE);
                break;
            default:
        }
    }

    /**
     * 构建生成属性
     *
     * @param clazz 目标类型
     * @return 生成属性
     */
    private static GenerateProperties buildGenerateProperties(Class<?> clazz) {
        GenerateProperties properties = new GenerateProperties();
        properties.setStrategy(Inner.leafCodeProperties.getGlobalIdConfig().getStrategy());
        properties.setLen(Inner.leafCodeProperties.getGlobalIdConfig().getLen());
        String key = clazz.getSimpleName();
        if (Inner.leafCodeProperties.getGlobalIdConfig().isKeyCamelToUnderscore()) {
            key = com.chervon.common.core.utils.StringUtils.camelToUnderscore(clazz.getSimpleName());
            if (StringUtils.hasText(Inner.leafCodeProperties.getGlobalIdConfig().getKeyPrefix())) {
                key = Inner.leafCodeProperties.getGlobalIdConfig().getKeyPrefix() + key;
            }
        }
        properties.setKey(key);

        GenerateId generateId = clazz.getAnnotation(GenerateId.class);
        if (generateId != null) {
            properties.setStrategy(generateId.strategy());
            if (StringUtils.hasText(generateId.key())) {
                properties.setKey(generateId.key());
            }
            if (generateId.len() > 0) {
                properties.setLen(generateId.len());
            }
        }
        return properties;
    }


    /**
     * 校验号段的元数据
     *
     * @param properties ID生成参数
     */
    private static void verifySegmentMetaData(GenerateProperties properties) {
        CodeMetadataService codeMetadataService = Inner.codeMetadataService;
        CodeMetadata metadata = new CodeMetadata()
                .setIsReset(false)
                .setKey(properties.getKey())
                .setSegmentLen(properties.getLen());
        codeMetadataService.verifyHasKeyMetadata(metadata);
    }
}
