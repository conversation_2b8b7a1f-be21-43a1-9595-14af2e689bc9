package com.chervon.idgenerator.provider.impl;

import com.chervon.idgenerator.generator.CodeGenerator;
import com.chervon.idgenerator.entity.CodeBlock;
import com.chervon.idgenerator.entity.CodeBlockStyle;
import com.chervon.idgenerator.entity.CodeGeneratorContext;
import com.chervon.idgenerator.exception.GenerateCodeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;

/**
 * 序列提供者
 *
 *
 * @date 2022/09/11
 */
@Component
@Slf4j
public class SequenceProvider extends AbsCodeBlockValueProvider<String> {
    /**
     * 样式正则
     */
    private static final String STYLE_REGEX = "^(?<style1>%[^\\?\\{}\\s]+)?(\\{(?<value1>[^\\?\\{}\\s]+)})?$|^(?<style2>%[^\\?\\{}\\s]+)?(\\{\\?(?<value2>[^\\?\\{}\\s]+)})?$";
    private static final String DEFAULT_STYLE = "%d";
    /**
     * 样式缓存
     */
    private static final Map<String, String> STYLE_CACHES = new ConcurrentHashMap<>();
    /**
     * KEY模版缓存
     */
    private static final Map<String, String> KEY_TEMPLATE_CACHES = new ConcurrentHashMap<>();

    @Autowired
    @Qualifier("sequenceCodeGenerator")
    private CodeGenerator<Long> sequenceCodeGenerator;

    /**
     * 获取提供者类型
     *
     * @return
     */
    @Override
    public String getName() {
        return "seq";
    }

    /**
     * 计算值
     *
     * @param codeBlock
     * @return
     */
    @Override
    protected String computeValue(CodeBlock codeBlock) {
        String codeKey = (String) codeBlock.getCodeBlockStyle().getValue();
        if (codeKey == null) {
            codeKey = CodeGeneratorContext.getInstance().get(CodeGeneratorContext.CODE_METADATA_KEY);
            if (codeKey == null) {
                throw new GenerateCodeException("[seq]:No key provided.");
            }
        }
        String finalCodeKey = codeKey;
        String template = KEY_TEMPLATE_CACHES.computeIfAbsent(codeKey, k -> "key:" + finalCodeKey);
        Long seq = sequenceCodeGenerator.generator(template);
        return String.format(codeBlock.getCodeBlockStyle().getStyle(), seq);
    }

    /**
     * 解析模板样式信息
     * <p>
     * PS: support format
     * key:{key}&seq
     * key:{key}&seq:format
     * seq:{key}
     * seq:{?key}
     * seq:style{key}
     * seq:style{?key}
     *
     * @param style 样式
     * @return
     */
    @Override
    public CodeBlockStyle parse(String style) {
        //默认情况使用 KeyProvider提供的key
        if (!StringUtils.hasText(style)) {
            String key = CodeGeneratorContext.getInstance().get(CodeGeneratorContext.CODE_METADATA_KEY);
            if (key == null) {
                throw new GenerateCodeException("[seq]:Parsing codeBlockStyle failed, no key provided.");
            }
            CodeBlockStyle codeBlockStyle = new CodeBlockStyle();
            codeBlockStyle.setValue(key).setStyle(DEFAULT_STYLE);
            return codeBlockStyle;
        }
        Matcher matcher = regexMatcher(style, STYLE_REGEX);
        if (matcher.find()) {
            String stl = matcher.group(REGEX_STYLE_1);
            if (stl == null) {
                stl = matcher.group(REGEX_STYLE_2);
            }
            String codeKey = matcher.group(REGEX_VALUE_1);
            if (codeKey == null) {
                codeKey = matcher.group(REGEX_VALUE_2);
                codeKey = CodeGeneratorContext.getInstance().get(codeKey);
            }
            if (stl == null) {
                stl = DEFAULT_STYLE;
            }
            return new CodeBlockStyle().setStyle(stl).setValue(codeKey);
        }
        throw new GenerateCodeException("[seq]:Parsing codeBlockStyle failed, codeBlockStyle format does not match.");
    }
}
