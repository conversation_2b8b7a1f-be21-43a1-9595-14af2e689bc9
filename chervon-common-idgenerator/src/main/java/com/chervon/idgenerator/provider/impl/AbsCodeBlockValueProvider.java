package com.chervon.idgenerator.provider.impl;

import com.chervon.idgenerator.provider.CodeBlockValueProvider;
import com.chervon.idgenerator.exception.GenerateCodeException;
import com.chervon.idgenerator.entity.CodeBlock;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 提供者抽象实现
 *
 *
 * @date 2022/09/11
 */
public abstract class AbsCodeBlockValueProvider<T> implements CodeBlockValueProvider<T> {
    /**
     * 空串
     */
    protected static final String EMPTY_STR = "";
    /**
     * 样式正则名称1
     */
    protected static final String REGEX_STYLE_1 = "style1";
    /**
     * 样式正则名称2
     */
    protected static final String REGEX_STYLE_2 = "style2";
    /**
     * 样式正则值1
     */
    protected static final String REGEX_VALUE_1 = "value1";
    /**
     * 样式正则值2
     */
    protected static final String REGEX_VALUE_2 = "value2";

    /**
     * 获取值
     *
     * @return
     */
    @Override
    public T getValue(CodeBlock codeBlock) {
        if (null == codeBlock.getCodeBlockStyle()) {
            throw new GenerateCodeException("Generate code exception. no format provided.");
        }
        return computeValue(codeBlock);
    }

    /**
     * 计算值
     *
     * @param codeBlock
     * @return
     */
    protected abstract T computeValue(CodeBlock codeBlock);

    /**
     * 匹配样式信息
     *
     * @param style 样式
     * @param regex 正则
     * @return
     */
    protected Matcher regexMatcher(String style, String regex) {
        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(style);
    }
}
