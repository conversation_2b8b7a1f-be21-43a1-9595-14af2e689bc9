package com.chervon.idgenerator.provider.impl;

import com.chervon.idgenerator.entity.CodeBlockStyle;
import com.chervon.idgenerator.exception.GenerateCodeException;
import com.chervon.idgenerator.entity.CodeBlock;
import com.chervon.idgenerator.entity.CodeGeneratorContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;

/**
 * 时间格式提供者
 *
 *
 * @date 2022/09/11
 */
@Component
public class DateTimeProvider extends AbsCodeBlockValueProvider<String> {
    /**
     * 样式正则
     */
    private static final String STYLE_REGEX = "^(?<style1>[yMdHhmsS]+)$|^(?<style2>[yMdHhmsS]+)?(\\{\\?(?<value2>[^\\?\\{}\\s]+)})?$";
    private static final String DEFAULT_FORMAT = "yyyyMMdd";

    /**
     * 获取提供者名称
     *
     * @return
     */
    @Override
    public String getName() {
        return "date";
    }

    /**
     * 计算值
     *
     * @param codeBlock
     * @return
     */
    @Override
    protected String computeValue(CodeBlock codeBlock) {
        if (codeBlock == null || StringUtils.isEmpty(codeBlock.getCodeBlockStyle())) {
            return EMPTY_STR;
        }
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(codeBlock.getCodeBlockStyle().getStyle());
            Date date;
            if (codeBlock.getCodeBlockStyle().getValue() != null) {
                date = (Date) codeBlock.getCodeBlockStyle().getValue();
            } else {
                date = new Date();
            }
            return dateFormat.format(date);
        } catch (Exception e) {
            throw new GenerateCodeException("[date]:Generate code exception. date format(" + codeBlock.getCodeBlockStyle() + ") error.", e);
        }
    }

    /**
     * 解析模板样式信息
     * <p>
     * PS: support format
     * date
     * date:style
     * date:style{?value}
     * date:{?value}
     *
     * @param style 样式
     * @return
     */
    @Override
    public CodeBlockStyle parse(String style) {
        //全部使用默认情况
        if (!StringUtils.hasText(style)) {
            return new CodeBlockStyle().setStyle(DEFAULT_FORMAT);
        }
        Matcher matcher = regexMatcher(style, STYLE_REGEX);
        if (matcher.find()) {
            String stl = matcher.group(REGEX_STYLE_1);
            //存在格式和引用名
            if (stl == null) {
                stl = matcher.group(REGEX_STYLE_2);
                //只存在引用名
                if (stl == null) {
                    stl = DEFAULT_FORMAT;
                }
            }
            String val = matcher.group(REGEX_VALUE_2);
            Date valDate = null;
            if (val != null) {
                valDate = CodeGeneratorContext.getInstance().get(val);
            }
            return new CodeBlockStyle().setStyle(stl).setValue(valDate);
        }
        throw new GenerateCodeException("[date]:Parsing codeBlockStyle failed, codeBlockStyle format does not match.");
    }
}
