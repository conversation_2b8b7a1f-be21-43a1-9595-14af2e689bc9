package com.chervon.idgenerator.provider.impl;

import com.chervon.idgenerator.exception.GenerateCodeException;
import com.chervon.idgenerator.entity.CodeBlock;
import com.chervon.idgenerator.entity.CodeBlockStyle;
import com.chervon.idgenerator.entity.CodeGeneratorContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.regex.Matcher;

/**
 *
 * @date 2019/9/2 19:30
 */
@Component
public class KeyProvider extends AbsCodeBlockValueProvider<String> {
    /**
     * 样式正则
     */
    private static final String STYLE_REGEX = "^\\{(?<value1>[^\\?\\{}\\s]+)}$|^\\{\\?(?<value2>[^\\?\\{}\\s]+)}$";

    /**
     * 计算值
     *
     * @param codeBlock
     * @return
     */
    @Override
    protected String computeValue(CodeBlock codeBlock) {
        if (!StringUtils.hasText((String) codeBlock.getCodeBlockStyle().getValue())) {
            throw new GenerateCodeException("[key]:Must provide the value of the key.");
        }
        //设置需要使用的序列KEY
        CodeGeneratorContext.getInstance().put(CodeGeneratorContext.CODE_METADATA_KEY, codeBlock.getCodeBlockStyle().getValue());
        return EMPTY_STR;
    }

    /**
     * 获取提供者名称
     *
     * @return
     */
    @Override
    public String getName() {
        return "key";
    }

    /**
     * 解析模板样式信息
     * <p>
     * PS: support format
     * key:{key}
     * key:{?key}
     *
     * @param style 样式
     * @return
     */
    @SuppressWarnings("Duplicates")
    @Override
    public CodeBlockStyle parse(String style) {
        //默认情况  后续计算时会抛出异常
        if (!StringUtils.hasText(style)) {
            return new CodeBlockStyle().setValue(EMPTY_STR);
        }
        Matcher matcher = regexMatcher(style, STYLE_REGEX);
        if (matcher.find()) {
            String val = matcher.group(REGEX_VALUE_1);
            if (null == val) {
                String key = matcher.group(REGEX_VALUE_2);
                val = CodeGeneratorContext.getInstance().get(key);
            }
            return new CodeBlockStyle().setValue(val);
        }
        throw new GenerateCodeException("[key]:Parsing codeBlockStyle failed, codeBlockStyle format does not match.");
    }
}
