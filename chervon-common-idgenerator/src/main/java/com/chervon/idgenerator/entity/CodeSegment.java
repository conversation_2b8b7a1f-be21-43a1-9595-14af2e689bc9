package com.chervon.idgenerator.entity;

import com.chervon.common.core.utils.SpringUtils;
import com.chervon.idgenerator.generator.impl.SegmentIdGenerator;
import java.time.Instant;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据段
 *
 *
 * @date 2022/09/11
 */
public class CodeSegment {
    /**
     * 高位已经发放的数量
     */
    private long issued;
    /**
     * 低位
     */
    private volatile AtomicInteger lo;
    /**
     * Code池大小
     */
    private volatile AtomicLong size;
    /**
     * 有效日期
     */
    private volatile Long expiredTime;
    /**
     * 是否需要重置
     */
    private final Boolean isReset;

    public CodeSegment(CodeMetadata codeMetadata) {
        this.issued = codeMetadata.getIssued();
        this.size = new AtomicLong(codeMetadata.getSegmentLen());
        this.expiredTime = codeMetadata.getExpiredTime();
        this.isReset = codeMetadata.getIsReset();
        this.lo = new AtomicInteger(1);
    }

    /**
     * 获取下一个数据
     *
     * @return
     */
    public synchronized Long next(String key) {
        return nextSequence(key);
    }

    /**
     * 下一个序列
     *
     * @return
     */
    private Long nextSequence(String key) {
        if (size.get() <= 0) {
            return ((SegmentIdGenerator) SpringUtils.getBean("segmentIdGenerator")).next(key);
        }
        size.getAndDecrement();
        Long sequence = issued + lo.get();
        lo.getAndIncrement();
        return sequence;
    }

    /**
     * 获取剩余数量
     *
     * @return 数量
     */
    public long getSize() {
        return this.size.get();
    }

    /**
     * 重置数据
     *
     * @param codeMetadata
     */
    public void reset(CodeMetadata codeMetadata) {
        this.issued = codeMetadata.getIssued();
        this.size.set(codeMetadata.getSegmentLen());
        this.lo.set(1);
        this.expiredTime = codeMetadata.getExpiredTime();
    }

    /**
     * 是否过期
     *
     * @return
     */
    public Boolean isExpired() {
        if (!this.isReset) {
            return false;
        }
        return this.expiredTime < Instant.now().toEpochMilli();
    }
}
