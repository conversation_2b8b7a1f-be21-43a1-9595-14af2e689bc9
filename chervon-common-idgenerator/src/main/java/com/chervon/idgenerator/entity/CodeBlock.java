package com.chervon.idgenerator.entity;


import com.chervon.idgenerator.provider.CodeBlockValueProvider;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 编号代码块
 *
 *
 * @date 2022/09/11
 */
@Data
@Accessors(chain = true)
public class CodeBlock implements Serializable {
    private static final long serialVersionUID = -9071421827222471092L;
    /**
     * 块模版
     */
    private String template;
    /**
     * 是否是键
     */
    private Boolean isKey;
    /**
     * 编码的格式信息
     */
    private CodeBlockStyle codeBlockStyle;
    /**
     * 提供者
     */
    private CodeBlockValueProvider<?> provider;
}
