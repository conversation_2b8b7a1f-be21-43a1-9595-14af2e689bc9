package com.chervon.idgenerator.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 * @date 2022/09/11
 */
@Data
@Accessors(chain = true)
public class CodeMetadata implements Serializable {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 已经发放
     */
    private Long issued;
    /**
     * 数据段
     */
    private Long segmentLen;
    /**
     * 键名称
     */
    private String key;
    /**
     * 是否需要重置
     */
    private Boolean isReset;
    /**
     * 过期时间
     */
    private Long expiredTime;
    /**
     * 版本号
     */
    private Long version;
}
