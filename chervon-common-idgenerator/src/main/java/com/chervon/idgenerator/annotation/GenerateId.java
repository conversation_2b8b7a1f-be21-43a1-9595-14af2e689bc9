package com.chervon.idgenerator.annotation;

import com.chervon.idgenerator.enums.IdStrategy;

import java.lang.annotation.*;

/**
 * ID生成描述
 *
 *
 * @date 2022/09/11
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface GenerateId {
    /**
     * ID生成策略
     */
    IdStrategy strategy();

    /**
     * 步长 使用IdStrategy.Segment时生效
     */
    long len() default -1;

    /**
     * key 使用IdStrategy.Segment时生效
     */
    String key() default "";
}
