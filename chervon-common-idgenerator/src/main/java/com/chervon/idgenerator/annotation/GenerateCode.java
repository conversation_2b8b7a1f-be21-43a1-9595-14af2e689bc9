package com.chervon.idgenerator.annotation;

import java.lang.annotation.*;

/**
 * Code生成描述
 *
 *
 * @date 2022/09/11
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface GenerateCode {
    /**
     * 步长 默认为1，如果能够容忍单号的不连续，可以适当增加步长
     */
    long len() default 1;

    /**
     * key
     */
    String key();

    /**
     * 是否每日重置
     */
    boolean isReset() default false;

    /**
     * 模版
     */
    String template();
}
