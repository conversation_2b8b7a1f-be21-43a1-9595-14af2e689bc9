package com.chervon.usercenter.api.dto.enums;

/**
 * 调查问卷问题3 答案枚举
 * <p>
 * What other products are you considering to buy in the next 12 months? Please select all that apply. Allow multiple selections. No limit.
 *
 * @Author：flynn.wang
 * @Date：2024/2/2 15:03
 */
public enum SfSurveyQuestionThreeAnswerEnum implements SfSurveyMultiAnswerQuestionEnum{


    ANSWER_0(0, "a3A0h00000303ffEAA", SfSurveyQuestionManualAnswerEnum.N, "Riding Mower"),
    ANSWER_1(1, "a3A0h00000303fjEAA", SfSurveyQuestionManualAnswerEnum.N, "Lawn Mower"),
    ANSWER_2(2, "a3A0h00000303fkEAA", SfSurveyQuestionManualAnswerEnum.N, "Leaf Blower"),
    ANSWER_3(3, "a3A0h00000303fdEAA", SfSurveyQuestionManualAnswerEnum.N, "String Trimmer"),
    ANSWER_4(4, "a3A0h00000303feEAA", SfSurveyQuestionManualAnswerEnum.N, "<PERSON> Blower"),
    ANSWER_5(5, "a3A0h00000303fZEAQ", SfSurveyQuestionManualAnswerEnum.N, "Chainsaw"),
    ANSWER_6(6, "a3A0h00000303faEAA", SfSurveyQuestionManualAnswerEnum.N, "Hedge Trimmer"),
    ANSWER_7(7, "a3A0h00000303fbEAA", SfSurveyQuestionManualAnswerEnum.N, "Power Station (Inverter)"),
    ANSWER_8(8, "a3A0h00000303fgEAA", SfSurveyQuestionManualAnswerEnum.N, "Pressure washer"),
    ANSWER_9(9, "a3A0h00000303fhEAA", SfSurveyQuestionManualAnswerEnum.N, "Wet/Dry Vac"),
    ANSWER_10(10, "a3A0h00000303fiEAA", SfSurveyQuestionManualAnswerEnum.N, "Ice/Earth Auger"),
    ANSWER_11(11, "a3A0h00000303fmEAA", SfSurveyQuestionManualAnswerEnum.N, "Portable Cooler"),
    ANSWER_12(12, "a3A0h00000303fVEAQ", SfSurveyQuestionManualAnswerEnum.N, "Portable Speaker"),
    ANSWER_13(13, "a3A0h00000303fWEAQ", SfSurveyQuestionManualAnswerEnum.N, "Portable Light"),
    ANSWER_14(14, "a3A0h00000303fXEAQ", SfSurveyQuestionManualAnswerEnum.N, "Portable Fan"),
    ANSWER_15(15, "a3A0h00000303fYEAQ", SfSurveyQuestionManualAnswerEnum.N, "UTV"),
    ANSWER_16(16, "a3A0h00000303fUEAQ", SfSurveyQuestionManualAnswerEnum.N, "E-bike/Scooter"),
    ANSWER_17(17, "a3A0h00000303fcEAA", SfSurveyQuestionManualAnswerEnum.Y, "Other (specify)"),
    ANSWER_18(18, "a3A0h00000303flEAA", SfSurveyQuestionManualAnswerEnum.N, "Do Not Know"),
    ;

    /**
     * 对应app顺序
     */
    private int order;

    /**
     * sf问题id
     */
    private String sfAnswerId;

    private SfSurveyQuestionManualAnswerEnum sfSurveyQuestionManualAnswerEnum;
    /**
     * 答案描述
     */
    private String desc;

    SfSurveyQuestionThreeAnswerEnum(int order, String sfAnswerId, SfSurveyQuestionManualAnswerEnum sfSurveyQuestionManualAnswerEnum, String desc) {
        this.order = order;
        this.sfAnswerId = sfAnswerId;
        this.sfSurveyQuestionManualAnswerEnum = sfSurveyQuestionManualAnswerEnum;
        this.desc = desc;
    }

    public int getOrder() {
        return order;
    }

    public String getSfAnswerId() {
        return sfAnswerId;
    }

    @Override
    public SfSurveyQuestionManualAnswerEnum getSfSurveyQuestionManualAnswerEnum() {
        return sfSurveyQuestionManualAnswerEnum;
    }

    @Override
    public SfSurveyMultiAnswerQuestionEnum getSfSurveyQuestionAnswerEnumByOrder(int order) {
        for (SfSurveyQuestionThreeAnswerEnum sfSurveyQuestionThreeAnswerEnum : SfSurveyQuestionThreeAnswerEnum.values()) {
            if (sfSurveyQuestionThreeAnswerEnum.getOrder() == order) {
                return sfSurveyQuestionThreeAnswerEnum;
            }
        }
        return null;
    }


}
