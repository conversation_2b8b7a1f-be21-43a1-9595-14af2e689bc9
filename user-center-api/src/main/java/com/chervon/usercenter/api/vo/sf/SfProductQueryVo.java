package com.chervon.usercenter.api.vo.sf;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-03-20 11:17
 **/
@Data
public class SfProductQueryVo implements Serializable {
    private Attributes attributes;
    @JsonProperty("Id")
    private String Id;
    @JsonProperty("Name")
    private String Name;
    @JsonProperty("ProductCode")
    private String ProductCode;
    @JsonProperty("CreatedDate")
    private String CreatedDate;
    @JsonProperty("Country_of_Origin__c")
    private String Country_of_Origin__c;

    @Data
    public static class Attributes implements Serializable {
        private String type;
        private String url;
    }
}
