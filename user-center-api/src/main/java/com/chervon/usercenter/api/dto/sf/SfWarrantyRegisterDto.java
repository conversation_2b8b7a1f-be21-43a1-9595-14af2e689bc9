package com.chervon.usercenter.api.dto.sf;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-20 10:42
 **/
@Data
public class SfWarrantyRegisterDto implements Serializable {
    private List<WRecord> records;

    @Data
    public static class WRecord implements Serializable {
        /**
         * 请查看本文档中的‘WIAttributes’
         * 部分信息
         */
        private WAttributes attributes;
        /**
         * Salesforce中客户记录的Id
         */
        @JsonProperty("AccountCustomer__c")
        private String AccountCustomer__c;
        /**
         * 字符串格式传入：yyyy-mm-dd
         */
        @JsonProperty("Purchase_Date__c")
        private String Purchase_Date__c;
        /**
         * Salesforce中产品记录的Id
         */
        @JsonProperty("Master_Product__c")
        private String Master_Product__c;
        /**
         * 选填：
         * EGO
         * Skil
         * SkilSaw
         * Hammerhead
         * Hypertough
         * FLEX
         * Kobalt
         */
        @JsonProperty("Brand_Name__c")
        private String Brand_Name__c;
        /**
         * 选填：
         * Industrial/Professional/Commercial
         * Residential
         * Rental
         */
        @JsonProperty("Product_Use_Type2__c")
        private String Product_Use_Type2__c;
        /**
         * 购买地址
         */
        @JsonProperty("Place_of_Purchase__c")
        private String Place_of_Purchase__c;
        /**
         * 发票状态。三选一
         */
        @JsonProperty("Lost_Receipt__c")
        private Boolean Lost_Receipt__c;
        /**
         * 发票状态。三选一
         */
        @JsonProperty("Pending__c")
        private Boolean Pending__c;
        /**
         * 请查看本文档中的‘WarrantyItems’
         * 部分信息
         */
        @JsonProperty("Gift__c")
        private Boolean Gift__c;


        @JsonProperty("One_Time_Exception__c")
        private Boolean One_Time_Exception__c;

        /**
         * 收据
         */
        @JsonProperty("Image_of_Receipt__c")
        private String Image_of_Receipt__c;

        /**
         * 要注册质保的物品(设备)列表
         */
        @JsonProperty("Warranty_Items__r")
        private WarrantyItems Warranty_Items__r;

        /**
         * 要注册质保来源，为提升CRM注册维保接口效率添加字段
         */
        @JsonProperty("Source__c")
        private String Source__c;
    }

    @Data
    public static class WAttributes implements Serializable {
        /**
         * 固定值:Warranty__c
         */
        private String type;
        /**
         * 用于确认注册完成后返回的Salesforce Id与对应记录的关系
         */
        private String referenceId;
    }

    @Data
    public static class WarrantyItems implements Serializable {
        private List<WIRecord> records;
    }

    @Data
    public static class WIRecord implements Serializable {
        private WAttributes attributes;
        /**
         * 序列号
         */
        private String Serial_Number__c;
        /**
         * 产品名称
         */
        private String Product_Name__c;
        /**
         * model
         */
        private String Product_Model__c;
        /**
         * model
         */
        private String Product_Code__c;
        /**
         * 选填：Product, Charger, Battery
         */
        private String Product_Type__c;
        /**
         * Salesforce中产品记录的Id
         */
        private String Product__c;
    }
}
