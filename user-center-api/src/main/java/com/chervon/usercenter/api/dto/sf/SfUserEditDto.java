package com.chervon.usercenter.api.dto.sf;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-03-23 14:52
 **/
@Data
public class SfUserEditDto implements Serializable {

    /**
     * 名
     */
    @JsonProperty("FirstName")
    private String FirstName;
    /**
     * 姓
     * 对应字段
     */
    @JsonProperty("LastName")
    private String LastName;
    /**
     * 个人邮箱,与EGO_username__c保持一直
     */
    @JsonProperty("PersonEmail")
    private String PersonEmail;
    /**
     * SF中的用户名
     * 对应字段email
     */
    @JsonProperty("EGO_username__c")
    private String EGO_username__c;
    /**
     * 用户密码
     * 对应字段password
     */
    @JsonProperty("EGO_password__c")
    private String EGO_password__c;
    /**
     * 国家-英文
     */
    @JsonProperty("Site_Origin__pc")
    private String Site_Origin__pc;
    /**
     * 非必填
     */
    @JsonProperty("Trade__c")
    private String Trade__c;
    /**
     * 非必填
     */
    @JsonProperty("Trade_Other__c")
    private String Trade_Other__c;
    /**
     * 个人电话
     * 非必填
     */
    @JsonProperty("PersonMobilePhone")
    private String PersonMobilePhone;
    /**
     * 电话
     * 非必填
     */
    @JsonProperty("Phone")
    private String Phone;
    /**
     * 地址-街
     * 非必填
     */
    @JsonProperty("ShippingStreet")
    private String ShippingStreet;
    /**
     * 地址-城市
     * 非必填
     */
    @JsonProperty("ShippingCity")
    private String ShippingCity;
    /**
     * 地址-区
     * 非必填
     */
    @JsonProperty("ShippingState")
    private String ShippingState;
    /**
     * 邮编
     * 非必填
     */
    @JsonProperty("ShippingPostalCode")
    private String ShippingPostalCode;
    /**
     * 地址-国家
     * 非必填
     */
    @JsonProperty("ShippingCountry")
    private String ShippingCountry;
    /**
     * 公司
     * 非必填
     */
    @JsonProperty("Company__pc")
    private String Company__pc;
}
