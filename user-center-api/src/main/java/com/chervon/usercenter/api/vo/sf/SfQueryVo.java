package com.chervon.usercenter.api.vo.sf;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-09 11:54
 **/
@Data
public class SfQueryVo<T> implements Serializable {
    /**
     * 成功时返回。查询到的记录总数
     */
    private Integer totalSize;
    /**
     * 成功时返回。
     * （1）若查询到的记录总数小于等于2000，为true。
     * （2）若查询到的记录总数大于2000，Salesforce会拆分查询到的记录，并且此处为false，直到没有nextRecordsUrl时则为true。
     * 类似于分页，是否为最后一页。
     */
    private Boolean done;
    /**
     * 成功时返回。若查询到的记录总数大于2000，则此处有值，通过连接可访问到剩余数据。
     * 类似于分页，提供后面页面的链接地址。
     */
    private String nextRecordsUrl;
    /**
     * 知识库记录列表
     */
    private List<T> records;
    /**
     * 失败时返回
     */
    private String message;
    /**
     * 失败时返回
     */
    private String errorCode;
}
