package com.chervon.usercenter.api.vo.sf;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-20 10:01
 **/
@Data
public class SfWarrantyInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty("done")
    private Boolean done;

    @JsonProperty("totalSize")
    private Integer totalSize;

    @JsonProperty("records")
    private List<WarrantyItem> records;

    @Data
    public static class WarrantyItem implements Serializable {
        private static final long serialVersionUID = 1L;
        private SfAttribute attributes;
        @JsonProperty("Id")
        private String id;
        @JsonProperty("Lost_Receipt__c")
        private Boolean lostReceipt;
        @JsonProperty("Gift__c")
        private Boolean gift;
        @JsonProperty("Place_of_Purchase__c")
        private String purchasePlace;
        @JsonProperty("Product_Use_Type2__c")
        private String applyWith;
        @JsonProperty("Purchase_Date__c")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate purchaseTime;
        @JsonProperty("Expiration_Date__c")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate expireTime;
    }

    @Data
    public static class SfAttribute implements Serializable {
        private static final long serialVersionUID = 1L;
        @JsonProperty("type")
        private String type;
        @JsonProperty("url")
        private String url;
    }
}
