package com.chervon.usercenter.api.dto.sf;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 调查问卷添加dto
 */
@Data
public class SfSurveySubmitDto implements Serializable {

    /**
     * sf 用户id
     */
    @JsonProperty("customerId")
    private String sfUserId;

    /**
     * 设备sn
     */
    @JsonProperty("serialNumber")
    private String sn;

    /**
     * 问题和答案
     */
    @JsonProperty("responseList")
    private List<SurveyContent> surveyContentList;

    /**
     * 问卷调查实体
     */
    @Data
    public static class SurveyContent implements Serializable {
        /**
         * sf 问题id
         */
        private String questionId;

        /**
         * 答案
         */
        @JsonProperty("result")
        private Answer answer;

    }

    /**
     * 答案
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Answer implements Serializable {
        private String manualText;
        private Boolean haveManualText;
        private String type;
        private String answer;
    }
}
