package com.chervon.usercenter.api.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-06-29 13:51
 **/
@Data
public class SysUserEditStatusCommand implements Serializable {
    /**
     * 用户Id
     */
    @NotNull
    private Long id;
    /**
     * 用户状态(0停用 1启用)
     */
    @Min(0)
    @Max(1)
    @NotNull
    private Integer status;
}