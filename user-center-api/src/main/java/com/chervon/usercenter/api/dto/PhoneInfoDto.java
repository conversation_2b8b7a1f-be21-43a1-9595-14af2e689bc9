package com.chervon.usercenter.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 手机信息
 * <AUTHOR>
 * @date 10:52 2022/8/30
 **/
@Data
@ApiModel("手机信息")
public class PhoneInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机系统版本
     **/
    @ApiModelProperty("手机系统版本")
    private String phoneOsVersion;

    /**
     * 手机型号
     **/
    @ApiModelProperty("手机型号")
    private String phoneModel;

    /**
     * APP宿主包版本
     **/
    @ApiModelProperty("APP宿主包版本")
    private String appVersion;

    /**
     * RN包版本map, key为设备id，value为RN版本号
     **/
    @ApiModelProperty("RN包版本map, key为设备id，value为RN版本号")
    private Map<String, String> rnMap;

    /**
     * ip地址
     **/
    @ApiModelProperty("ip地址")
    private String ip;

    /**
     * app类型
     **/
    @ApiModelProperty("app类型, android或ios")
    private String appTypeCode;
}
