package com.chervon.usercenter.api.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/20 14:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "app用户账户分页查询条件")
public class AppUserPageDto extends PageRequest implements Serializable {

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "在线状态code")
    private String appPresenceCode;

    @ApiModelProperty(value = "手机型号")
    private String phoneModel;

    @ApiModelProperty(value = "app类型code")
    private String appTypeCode;

    @ApiModelProperty(value = "用户类型code")
    private String userTypeCode;

    @ApiModelProperty(value = "用户来源code")
    private String userSourceCode;

    @ApiModelProperty(value = "app版本号")
    private String appVersion;
}
