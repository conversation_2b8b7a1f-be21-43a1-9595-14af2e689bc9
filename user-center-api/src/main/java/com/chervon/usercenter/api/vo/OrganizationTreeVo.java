package com.chervon.usercenter.api.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 组织架构
 * <AUTHOR>
 *
 * @date 2022-06-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrganizationTreeVo implements Serializable {

    /**
     * 部门下面的子部门
     */
    List<OrganizationDetailVo> organizations;

    /**
     * 部门下面的用户
     */
    List<SysUserVo> users;
}
