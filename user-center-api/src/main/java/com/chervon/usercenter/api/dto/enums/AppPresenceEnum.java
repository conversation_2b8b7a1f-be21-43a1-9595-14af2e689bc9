package com.chervon.usercenter.api.dto.enums;

import java.util.Arrays;

/**
 * app用户在线状态：
 *  1  "online", 2  "offline"
 * <AUTHOR>
 */
public enum AppPresenceEnum {
    ONLINE(1, "online"),
    OFFLINE(2,"offline")
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    AppPresenceEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取类型
     */
    public int getType() {
        return type;
    }

    /**
     * 获取描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取描述
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(AppPresenceEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举
     */
    public static AppPresenceEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
