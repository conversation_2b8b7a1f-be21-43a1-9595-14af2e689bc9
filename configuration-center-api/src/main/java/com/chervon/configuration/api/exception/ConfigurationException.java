package com.chervon.configuration.api.exception;

import com.chervon.common.core.exception.base.BaseException;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/6/26 21:54
 */
public class ConfigurationException extends BaseException implements Serializable {

    private static final long serialVersionUID = 1L;

    private String tempMessage;

    public void setTempMessage(String tempMessage) {
        this.tempMessage = tempMessage;
    }

    public ConfigurationException(String code, Object... args) {
        super("configurationCenter", code, args);
    }

    public ConfigurationException(ConfigurationErrorCode code, Object... args) {
        super("configurationCenter", code.getCode(), code.getDefaultMessage(), code.getErrorMessage(), args);
    }

    @Override
    public String getMessage() {
        if (tempMessage != null) {
            return this.tempMessage;
        }
        return this.getDefaultMessage() == null ? this.getCode() : String.format(this.getDefaultMessage(), this.getArgs());
    }
}
