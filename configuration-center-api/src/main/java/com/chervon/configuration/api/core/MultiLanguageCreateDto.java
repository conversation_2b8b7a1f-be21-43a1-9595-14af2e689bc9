package com.chervon.configuration.api.core;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/10 20:40
 */
@Data
@ApiModel(description = "多语言创建对象")
public class MultiLanguageCreateDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "多语言code")
    private String langCode;

    @ApiModelProperty(value = "多语言系统code")
    private String sysCode;

    @ApiModelProperty(value = "多语言页面名称")
    private String page;

    @ApiModelProperty(value = "多语言功能名称")
    private String func;

    @ApiModelProperty(value = "多语言元素类型code")
    private String elementCode;

    @ApiModelProperty(value = "多语言文本属性code")
    private String textCode;

    @ApiModelProperty(value = "多语言备注")
    private String remark;

    @ApiModelProperty(value = "多语言项集合")
    private List<MultiLanguageNode> nodes;

}
