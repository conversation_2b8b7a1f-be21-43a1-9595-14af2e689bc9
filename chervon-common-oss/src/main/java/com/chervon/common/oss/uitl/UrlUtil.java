package com.chervon.common.oss.uitl;

import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @date 2023/3/23 15:16
 */
@Slf4j
public class UrlUtil {

    private UrlUtil() {
    }

    public static String completeUrl(String s3Pre, String path) {
        if (path.startsWith("http://") || path.startsWith("https://")) {
            return path;
        }
        if (s3Pre.endsWith("/")) {
            if (path.startsWith("/")) {
                return s3Pre + encodeUrl(path.substring(1));
            }
            return s3Pre + encodeUrl(path);
        }
        if (path.startsWith("/")) {
            return s3Pre + encodeUrl(path);
        }
        return s3Pre + "/" + encodeUrl(path);
    }

    public static String encodeUrl(String url) {
        try {
            return URLEncoder.encode(url, "UTF-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            log.warn(e.getMessage(), e);
            return null;
        }
    }

}
