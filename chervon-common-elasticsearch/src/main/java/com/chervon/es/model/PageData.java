package com.chervon.es.model;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 分页对象
 *
 * <AUTHOR>
 * @date 16:05 2022/2/10
 **/
@Data
public class PageData<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    public PageData() {
    }

    /**
     * 有参构造.
     */
    public PageData(Integer pageNum, Integer pageSize) {
        super();
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

    /**
     * 结果集.
     */
    private List<T> list;
    /**
     * .当前页.
     */
    private int pageNum;
    /**
     * 每页的数量.
     */
    private int pageSize;
    /**
     * 当前页的数量.
     */
    private int size;
    /**
     * 总页数.
     */
    private int pages;
    /**
     * 总记录数.
     */
    private int total;

}
