package com.chervon.configuration.sdk.language.config;

import cn.hutool.extra.spring.SpringUtil;
import com.chervon.configuration.api.exception.ConfigurationErrorCode;
import com.chervon.configuration.api.exception.ConfigurationException;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;

/**
 * <AUTHOR>
 * @date 2023/4/17 11:18
 */
public class ExceptionMessageUtil {

    private static final RemoteMultiLanguageService remoteMultiLanguageService = SpringUtil.getBean("remoteMultiLanguageService");

    public static ConfigurationException getException(ConfigurationErrorCode errorCode, Object... args) {
        ConfigurationException exception = new ConfigurationException(errorCode, args);
        String code = errorCode.getCode();
        if (code != null) {
            String language = remoteMultiLanguageService.simpleFindMultiLanguageByCode(code);
            if (language != null) {
                exception.setTempMessage(language);
            }
        }
        return exception;
    }

    public static ConfigurationException getException(String message) {
        return new ConfigurationException(message);
    }

}
