package com.chervon.configuration.sdk.language.config;

import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.chervon.configuration.api.exception.ConfigurationErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.IOException;
import java.util.Locale;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date 2022/05/31 17:07
 */
@Slf4j
//@Component
public class NacosConfig {

    @Autowired
    private NacosConfigProperties nacosConfigProperties;

    @Autowired
    private MessageConfig messageConfig;

    private final static ConcurrentHashMap<String, Listener> NACOS_LISTENER_MAP = new ConcurrentHashMap<>();

    @Autowired
    public void init() {
//        if (messageConfig.getBasenameArray().length > 0) {
//            for (String basename : messageConfig.getBasenameArray()) {
//                initTip(basename, null);
//                initTip(basename, Locale.CHINESE);
//                initTip(basename, Locale.ENGLISH);
//                initTip(basename, Locale.FRENCH);
//                initTip(basename, Locale.JAPANESE);
//            }
//        }
    }

    private ConfigService getConfigService() {
        try {
            Properties properties = new Properties();
            properties.put(PropertyKeyConst.SERVER_ADDR, Objects.toString(nacosConfigProperties.getServerAddr(), ""));
            properties.put(PropertyKeyConst.NAMESPACE, Objects.toString(nacosConfigProperties.getNamespace(), ""));
            return NacosFactory.createConfigService(properties);
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_INIT_CONFIG_SERVICE_ERROR);
        }
    }

    private void initTip(String applicationName, Locale locale) {
        String content;
        String dataId;
        try {
            if (locale == null) {
                dataId = applicationName + "-message.properties";
            } else {
                dataId = applicationName + "-message_" + locale.getLanguage() + ".properties";
            }
            content = getConfigService().getConfig(dataId, nacosConfigProperties.getGroup(), 5000);
            if (StringUtils.isEmpty(content)) {
                log.warn("配置内容为空!dataId:{}", dataId);
                content = "";
            }
            saveAsFileWriter(dataId, content);
            setListener(applicationName, dataId, locale);
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_INIT_ERROR);
        }
    }

    private void setListener(String applicationName, String dataId, Locale locale)
            throws com.alibaba.nacos.api.exception.NacosException {
        // 删除旧监听
        String key = String.join(NacosConfigProperties.COMMAS, String.valueOf(dataId),
                String.valueOf(nacosConfigProperties.getGroup()));
        Listener remove = NACOS_LISTENER_MAP.remove(key);
        if (remove != null) {
            getConfigService().removeListener(dataId, nacosConfigProperties.getGroup(), remove);
        }
        // 创建新监听
        Listener listener = new Listener() {
            @Override
            public void receiveConfigInfo(String configInfo) {
                try {
                    initTip(applicationName, locale);
                } catch (Exception e) {
                    throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_INIT_ERROR);
                }
            }

            @Override
            public Executor getExecutor() {
                return null;
            }
        };
        getConfigService().addListener(dataId, nacosConfigProperties.getGroup(), listener);
        NACOS_LISTENER_MAP.put(key, listener);
    }

    private void saveAsFileWriter(String fileName, String content) {
        String path = System.getProperty("user.dir") + File.separator + messageConfig.getBaseFolder();
        try {
            fileName = path + File.separator + fileName;
            File file = new File(fileName);
            FileUtils.writeStringToFile(file, content, "UTF-8");
            log.debug("国际化配置已更新!本地文件路径:{}", fileName);
        } catch (IOException e) {
            throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_INIT_ERROR);
        }
    }


}
