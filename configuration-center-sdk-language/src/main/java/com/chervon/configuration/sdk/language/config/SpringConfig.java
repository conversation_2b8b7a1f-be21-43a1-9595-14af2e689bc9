package com.chervon.configuration.sdk.language.config;

import com.chervon.configuration.sdk.language.resolver.DefaultLocaleResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.util.ResourceUtils;
import org.springframework.web.servlet.LocaleResolver;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2022/05/31 17:06
 */

@Slf4j
@Configuration()
public class SpringConfig {

//    @Autowired
    private MessageConfig messageConfig;

    @Primary
    @Bean(name = {"localeResolver"})
    public LocaleResolver localeResolver() {
        return new DefaultLocaleResolver();
    }

//    @Primary
//    @Bean(name = "messageSource")
    public ReloadableResourceBundleMessageSource messageSource() {
        log.debug("国际化配置内容:{}", messageConfig);
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        if (messageConfig.getBasenameArray().length > 0) {
            String[] basenameArray = new String[messageConfig.getBasenameArray().length];
            String[] array = messageConfig.getBasenameArray();
            for (int i = 0; i < array.length; i++) {
                String path = ResourceUtils.FILE_URL_PREFIX + System.getProperty("user.dir") + File.separator
                        + messageConfig.getBaseFolder() + File.separator + array[i]+"-message";
                log.debug("国际化配置路径:{}", path);
                basenameArray[i] = path;
            }
            messageSource.setBasenames(basenameArray);
        }
        messageSource.setDefaultEncoding(messageConfig.getEncoding());
        messageSource.setCacheMillis(messageConfig.getCacheMillis());
        messageSource.setFallbackToSystemLocale(messageConfig.isFallbackToSystemLocale());
        return messageSource;
    }
}
