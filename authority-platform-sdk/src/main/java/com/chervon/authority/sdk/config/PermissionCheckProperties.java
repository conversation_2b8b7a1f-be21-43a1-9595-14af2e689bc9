package com.chervon.authority.sdk.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/20 17:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PermissionCheckProperties implements Serializable {

    /**
     * 获取权限集合地址
     */
    private String permissionCheckUrl;

    /**
     * 是否开启权限校验
     */
    private boolean enable = true;

    /**
     * 需要排除的地址
     */
    private List<String> excludeUrls;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 权限key有效时间
     */
    private Duration permissionKeyTtl = Duration.ofMinutes(5);

    public PermissionCheckProperties(String permissionCheckUrl, List<String> excludeUrls, String appId) {
        this.permissionCheckUrl = permissionCheckUrl;
        this.excludeUrls = excludeUrls;
        this.appId = appId;
    }

}
