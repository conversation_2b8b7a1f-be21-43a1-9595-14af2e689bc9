package com.chervon.message.api.enums;

import java.util.Arrays;

/**
 * APP消息展示枚举：1展示  0不展示
 */
public enum AppShowEnum {
    /**
     * 1：展示
     */
    YES(1, "展示"),
    /**
     * 0:不展示
     */
    NO(0, "不展示"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    /**
     * 业务类型构造函数
     * @param type 类型id
     * @param desc 描述
     */
    AppShowEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public Integer getType() {
        return type;
    }
    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    public String getStrType(){
        return String.valueOf(type);
    }

    /**
     * 获取枚举类型值
     * @param value 类型值
     * @return 返回值
     */
    public static String getDesc(int value) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == value)
                .map(AppShowEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static AppShowEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
