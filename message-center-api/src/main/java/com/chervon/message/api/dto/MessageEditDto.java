package com.chervon.message.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 消息更新请求对象
 * <AUTHOR> 2024/8/28
 */
@Accessors(chain = true)
@Data
public class MessageEditDto {
    /**
     * 消息id
     */
    private Long id;
    /**
     * 消息UUID
     */
    private String uuid;
    /**
     * 批量UUID
     */
    private List<String> listUuid;
    /**
     * 告警消息id
     */
    private String systemMessageId;
    /**
     * userId
     */
    private Long userId;
    /**
     * 设备id
     */
    private String deviceId;
}
