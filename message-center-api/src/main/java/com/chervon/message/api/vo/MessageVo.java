package com.chervon.message.api.vo;

import com.chervon.message.api.enums.OsType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022-08-26
 */
@Data
public class MessageVo implements Serializable {

    @ApiModelProperty("消息标题")
    private String title;

    @ApiModelProperty("消息内容")
    private String content;

    @ApiModelProperty("推送附加消息")
    private Map<String, String> payloadData;

    @ApiModelProperty("推送用户userId")
    private Long userId;

    @ApiModelProperty("推送用户账号")
    private String email;

    @ApiModelProperty("推送用户token")
    private String token;

    @ApiModelProperty("设备类型")
    private OsType deviceType;

    @ApiModelProperty("系统消息或者营销系统消息Id")
    private String systemMessageId;

    @ApiModelProperty("消息的唯一标识")
    private String uuid;

    @ApiModelProperty("产品Pid")
    private String productId;

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("消息类型，0系统消息，1营销消息, 2设备消息")
    private Integer messageType;

    @ApiModelProperty("是否已读：1已读，0未读")
    private Integer ifRead;

    @ApiModelProperty("本条消息推送方式：0墓碑消息，1APP弹窗，2APP banner, 3消息管理, 4短信, 5邮件")
    private String pushType;


    @ApiModelProperty("推送结果，true:成功， false:失败")
    private Boolean pushResult;
}
