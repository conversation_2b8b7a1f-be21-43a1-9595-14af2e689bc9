package com.chervon.message.api.dto;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.message.api.enums.OsType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-08-26
 */
@Accessors(chain = true)
@Data
public class MessageDto implements Serializable {
    @ApiModelProperty("消息类型，0系统消息，1营销消息, 2设备消息, 3反馈消息")
    @NotNull
    private Integer messageType;
    /**
     * uuid
     */
    private String uuid;

    @ApiModelProperty(value = "推送用户userId", required = true)
    @NotEmpty(message = "用户ID不可为空")
    private String userId;

    @ApiModelProperty(value = "推送用户token", required = true)
    private String token;

    @ApiModelProperty(value = "消息标题", required = true)
    @NotEmpty(message = "消息标题不可为空")
    private String title;

    @ApiModelProperty("消息内容")
    private String content;

    @ApiModelProperty("推送附加消息")
    private Map<String, String> payloadData;

    @ApiModelProperty("设备类型")
    @NotNull
    private OsType deviceType;

    @ApiModelProperty("系统消息或者营销系统或反馈消息 消息Id")
    private String systemMessageId;
    //********************如为设备消息不为空**********************
    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("设备昵称")
    private String deviceNickName;

    @ApiModelProperty("产品Pid")
    private String productId;
    //********************如为设备消息不为空**********************

    @ApiModelProperty("消息推送方式：0墓碑消息，1APP弹窗，2APP banner, 3消息管理, 4短信, 5邮件")
    @NotNull
    private List<Integer> pushTypes;

    @ApiModelProperty("用户手机号,仅推送方式包含短信时需要")
    private String phone;

    @ApiModelProperty("用户邮箱")
    private String email;

    @ApiModelProperty("消息开关：1开启，0不开启")
    private Integer pushSwitch = CommonConstant.ONE;

    @ApiModelProperty("短信开关：1开启，0不开启")
    private Integer snsSwitch = CommonConstant.ZERO;

    @ApiModelProperty("语音开关：1开启，0不开启")
    private Integer phoneSwitch = CommonConstant.ZERO;
    /**
     * app是否显示: 0不展示 1展示
     */
    private Integer appShow;
}
