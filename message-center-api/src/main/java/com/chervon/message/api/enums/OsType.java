package com.chervon.message.api.enums;

import com.chervon.common.core.utils.StringUtils;

/**
 * 系统类型枚举类
 *
 * <AUTHOR>
 * @date 16:18 2022/2/21
 **/
public enum OsType {
    /**
     * ANDROID
     */
    ANDROID("Android"),
    IOS("iOS"),
    ALL("all");

    private final String name;

    OsType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static OsType getByName(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        for (OsType pushMethodEnum : OsType.values()) {
            if (pushMethodEnum.getName().equals(value)) {
                return pushMethodEnum;
            }
        }
        return null;
    }
}
