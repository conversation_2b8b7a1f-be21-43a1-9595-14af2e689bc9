<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.chervon</groupId>
    <artifactId>chervon-common-parent</artifactId>
    <version>1.1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>chervon-common</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <!-- J<PERSON><PERSON><PERSON><PERSON><PERSON>版本 -->
        <jacoco.version>0.8.8</jacoco.version>
        <!-- 测试框架版本 -->
        <junit-jupiter.version>5.8.2</junit-jupiter.version>
        <mockito.version>4.6.1</mockito.version>

        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.6.8</spring-boot.version>
        <spring-cloud.version>2021.0.3</spring-cloud.version>
        <spring-boot-admin.version>2.6.7</spring-boot-admin.version>
        <spring-boot.mybatis>2.2.2</spring-boot.mybatis>
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <druid.version>1.2.9</druid.version>
        <dynamic-ds.version>3.5.1</dynamic-ds.version>
        <fastjson.version>1.2.83</fastjson.version>
        <poi.version>5.0.0</poi.version>
        <easyexcel.version>3.1.0</easyexcel.version>
        <hutool.version>5.8.2</hutool.version>
        <redisson.version>3.17.2</redisson.version>
        <lock4j.version>2.2.2</lock4j.version>
        <xxl-job.version>2.3.1</xxl-job.version>
        <satoken.version>1.34.0</satoken.version>
        <mongo-java.version>3.9.1</mongo-java.version>
<!--        <com.google.guava.version>27.1-jre</com.google.guava.version>-->
        <eu.bitwalker.version>1.20</eu.bitwalker.version>

        <!-- 统一 guava 版本 解决隐式漏洞问题 -->
        <guava.version>30.0-jre</guava.version>

        <!-- OSS 配置 -->
        <aws-java-sdk-s3.version>1.12.215</aws-java-sdk-s3.version>
        <okhttp.version>4.9.3</okhttp.version>

        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <chervon.alibaba.version>1.0.0-SNAPSHOT</chervon.alibaba.version>
        <chervon.cola.version>1.0.0-SNAPSHOT</chervon.cola.version>
        <javax.mail.version>1.4.7</javax.mail.version>
        <org.freemarker.version>2.3.28</org.freemarker.version>

        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <commons-io.version>2.2</commons-io.version>
        <bouncycastle.version>1.68</bouncycastle.version>
        <knife4j-version>3.0.3</knife4j-version>
        <swagger.core.version>1.5.22</swagger.core.version>
        <spring-webmvc-version>5.3.20</spring-webmvc-version>
        <redis-clients-jedis-version>4.0.0</redis-clients-jedis-version>
        <spring.profiles.active>dev</spring.profiles.active>

    </properties>

    <modules>
        <module>chervon-common-bom</module>
        <module>chervon-common-alibaba-bom</module>
        <module>chervon-common-cola-bom</module>
        <module>chervon-common-log</module>
        <module>chervon-common-excel</module>
        <module>chervon-common-core</module>
        <module>chervon-common-redis</module>
        <module>chervon-common-security</module>
        <module>chervon-common-satoken</module>
        <module>chervon-common-web</module>
        <module>chervon-common-mybatis</module>
        <module>chervon-common-job</module>
        <module>chervon-common-dubbo</module>
        <module>chervon-common-seata</module>
        <module>chervon-common-oss</module>
        <module>chervon-common-idempotent</module>
        <module>chervon-common-idgenerator</module>
        <module>chervon-common-mail</module>
        <module>chervon-common-elasticsearch</module>
        <module>chervon-common-swagger</module>
        <module>chervon-common-mongodb</module>
        <module>chervon-common-sso</module>
        <module>chervon-common-i18n</module>
    </modules>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <!-- https://mvnrepository.com/artifact/org.springframework/spring-webmvc -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring-webmvc-version}</version>
            </dependency>
            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-alibaba-bom</artifactId>
                <version>${chervon.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Cola -->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-cola-bom</artifactId>
                <version>${chervon.cola.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- common 的依赖配置-->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-bom</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-core</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- Mybatis 依赖配置 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${spring-boot.mybatis}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- Swagger 依赖配置 -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!-- sql性能分析插件 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
            </dependency>

            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${aliyun.sms.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencent.sms.version}</version>
            </dependency>

            <!-- 统一 guava 版本 解决隐式漏洞问题 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- 统一 fastjson 版本 解决alibaba组件序列化漏洞问题 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>${javax.mail.version}</version>
            </dependency>

            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${org.freemarker.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <!-- swagger -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j-version}</version>
            </dependency>

            <dependency>
              <groupId>org.mongodb</groupId>
              <artifactId>mongo-java-driver</artifactId>
              <version>${mongo-java.version}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.google.guava</groupId>-->
<!--                <artifactId>guava</artifactId>-->
<!--                <version>${guava.version}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${eu.bitwalker.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${redis-clients-jedis-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 测试框架依赖 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit-jupiter.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <!-- 构建配置 -->
    <build>
        <plugins>
            <!-- JaCoCo插件（代码覆盖率） -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <configuration>
                    <excludes>
                        <!-- 启动类 -->
                        <exclude>**/*Application.class</exclude>
                        <!-- 配置类 -->
                        <exclude>**/*Config.class</exclude>
                        <exclude>**/*Configuration.class</exclude>
                        <!-- 常量类 -->
                        <exclude>**/*Constant*.class</exclude>
                        <exclude>**/constant/**</exclude>
                        <!-- 异常类 -->
                        <exclude>**/*Exception.class</exclude>
                        <exclude>**/exception/**</exclude>
                        <!-- 数据传输对象 -->
                        <exclude>**/*DTO.class</exclude>
                        <exclude>**/*VO.class</exclude>
                        <exclude>**/*Entity.class</exclude>
                        <exclude>**/domain/**</exclude>
                        <!-- 枚举类 -->
                        <exclude>**/enums/**</exclude>
                        <!-- 属性配置类 -->
                        <exclude>**/*Properties.class</exclude>
                        <exclude>**/prop/**</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report-aggregate</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Surefire插件（单元测试运行器） -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M7</version>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                    <argLine>@{argLine} -Dfile.encoding=UTF-8</argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>User Porject Release</name>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>User Porject Snapshot</name>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-snapshots/</url>
            <uniqueVersion>true</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>
</project>
