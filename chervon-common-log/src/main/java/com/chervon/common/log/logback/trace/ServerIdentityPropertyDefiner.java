package com.chervon.common.log.logback.trace;

import ch.qos.logback.core.PropertyDefinerBase;

import java.io.File;
import java.io.IOException;
import java.lang.management.ManagementFactory;

/**
 * 服务进程ID
 */
public class ServerIdentityPropertyDefiner extends PropertyDefinerBase {
    @Override
    public String getPropertyValue() {
        try {
            return ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
        } catch (Exception ex) {
            try {
                return (new File("/proc/self")).getCanonicalFile().getName();
            } catch (IOException ioException) {
                return "-";
            }
        }
    }
}