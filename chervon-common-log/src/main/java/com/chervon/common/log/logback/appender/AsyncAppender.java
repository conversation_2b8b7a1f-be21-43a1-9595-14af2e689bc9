package com.chervon.common.log.logback.appender;

import ch.qos.logback.classic.spi.ILoggingEvent;
import com.chervon.common.log.logback.conversion.ClassicConverterContext;
import com.chervon.common.log.logback.conversion.ClassicConverterContextHolder;
import com.chervon.common.log.logback.event.LoggingEvent;

/**
 *
 * @date 2023/5/16 15:35
 */
public class AsyncAppender extends ch.qos.logback.classic.AsyncAppender {

    @Override
    protected void append(ILoggingEvent eventObject) {
        ClassicConverterContext context = ClassicConverterContextHolder.get();
        if (context != null) {
            eventObject = new LoggingEvent(eventObject, context);
        }
        super.append(eventObject);
    }
}
