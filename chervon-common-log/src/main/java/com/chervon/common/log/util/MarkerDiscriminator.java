package com.chervon.common.log.util;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.sift.AbstractDiscriminator;
import com.chervon.common.log.config.LogConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.slf4j.Marker;

/**
 * <AUTHOR>
 * @date 2024/3/20 17:30
 * @desc 基于Marker的日志分离器
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class MarkerDiscriminator extends AbstractDiscriminator<ILoggingEvent> {

    private String key;
    private String defaultValue;

    @Override
    public String getDiscriminatingValue(ILoggingEvent iLoggingEvent) {
        Marker marker = iLoggingEvent.getMarker();
        if (marker != null) {
            return marker.getName();
        }
        return defaultValue;
    }

}
