/**
 * Logback: the reliable, generic, fast and flexible logging framework.
 * Copyright (C) 1999-2015, QOS.ch. All rights reserved.
 * <p>
 * This program and the accompanying materials are dual-licensed under
 * either the terms of the Eclipse Public License v1.0 as published by
 * the Eclipse Foundation
 * <p>
 * or (per the licensee's choosing)
 * <p>
 * under the terms of the GNU Lesser General Public License version 2.1
 * as published by the Free Software Foundation.
 */
package com.chervon.common.log.logback.conversion;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.CallerData;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.chervon.common.log.logback.event.ILoggingEventAppend;

public class LineOfCallerConverter extends ClassicConverter {
    public String convert(ILoggingEvent le) {
        ClassicConverterContext context = ClassicConverterContextHolder.get();
        if (context != null) {
            return CallerData.NA;
        }

        if(le instanceof ILoggingEventAppend){
            return CallerData.NA;
        }

        StackTraceElement[] cda = le.getCallerData();
        if (cda != null && cda.length > 0) {
            return Integer.toString(cda[0].getLineNumber());
        } else {
            return CallerData.NA;
        }
    }
}
