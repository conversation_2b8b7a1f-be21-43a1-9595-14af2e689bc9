package com.chervon.common.log.util;

import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Header代理
 * @date 2023/5/11 18:59
 */
public class HeaderProxy {
    /**
     * WebFlux request.
     */
    private ServerHttpRequestHeaderOperate serverRequest;
    /**
     * Servlet request header operator.
     */
    private ServletRequestHeaderOperate servletRequest;
    /**
     * Is servlet.
     */
    private boolean isServlet;

    public HeaderProxy(HttpServletRequest servletRequest) {
        this.servletRequest = new ServletRequestHeaderOperate(servletRequest);
        isServlet = true;
    }

    public HeaderProxy(ServerHttpRequest serverRequest) {
        this.serverRequest = new ServerHttpRequestHeaderOperate(serverRequest);
        isServlet = false;
    }

    /**
     * Get header
     *
     * @param name The header name.
     * @return Match the value of header name.
     */
    public String get(String name) {
        return isServlet ? servletRequest.get(name) : serverRequest.get(name);
    }

    /**
     * Get all header.
     *
     * @return All header info.
     */
    public Map<String, String> all() {
        return isServlet ? servletRequest.all() : serverRequest.all();
    }

    /**
     * Returns the Internet Protocol (IP) address of the client or last proxy that sent the request.
     *
     * @return Internet Protocol (IP) address
     */
    public String getRemoteAddr() {
        return isServlet ? servletRequest.getRemoteAddr() : serverRequest.getRemoteAddr();
    }

    /**
     * Servlet 请求头操作工具
     */
    private static class ServletRequestHeaderOperate implements HeaderOperate {
        private final HttpServletRequest request;

        public ServletRequestHeaderOperate(HttpServletRequest request) {
            this.request = request;
        }

        /**
         * Get header
         *
         * @param name The header name.
         * @return Match the value of header name.
         */
        @Override
        public String get(String name) {
            return request.getHeader(name);
        }

        /**
         * Get all header.
         *
         * @return All header info.
         */
        @Override
        public Map<String, String> all() {
            Enumeration<String> names = request.getHeaderNames();
            Map<String, String> headers = new HashMap<>();
            while (names.hasMoreElements()) {
                String name = names.nextElement();
                headers.put(name, request.getHeader(name));
            }
            return headers;
        }

        /**
         * Returns the Internet Protocol (IP) address of the client or last proxy that sent the request.
         *
         * @return Internet Protocol (IP) address
         */
        @Override
        public String getRemoteAddr() {
            return request.getRemoteAddr();
        }
    }

    /**
     * ServerHttpRequest 请求头操作工具
     */
    private static class ServerHttpRequestHeaderOperate implements HeaderOperate {
        private final ServerHttpRequest request;

        public ServerHttpRequestHeaderOperate(ServerHttpRequest request) {
            this.request = request;
        }

        /**
         * Get header
         *
         * @param name The header name.
         * @return Match the value of header name.
         */
        @Override
        public String get(String name) {
            List<String> headers = request.getHeaders().get(name);
            if (CollectionUtils.isEmpty(headers)) {
                return null;
            }
            return String.join(";", headers);
        }

        /**
         * Get all header.
         *
         * @return All header info.
         */
        @Override
        public Map<String, String> all() {
            Map<String, String> headers = new HashMap<>(request.getHeaders().size());
            request.getHeaders().keySet().forEach(x -> {
                headers.put(x, get(x));
            });
            return headers;
        }

        /**
         * Returns the Internet Protocol (IP) address of the client or last proxy that sent the request.
         *
         * @return Internet Protocol (IP) address
         */
        @Override
        public String getRemoteAddr() {
            if (request.getRemoteAddress() == null) {
                return null;
            }
            return request.getRemoteAddress().getAddress().getHostAddress();
        }
    }

    /**
     * Header Operate
     */
    private interface HeaderOperate {

        /**
         * Get header
         *
         * @param name The header name.
         * @return Match the value of header name.
         */
        String get(String name);

        /**
         * Get all header.
         *
         * @return All header info.
         */
        Map<String, String> all();

        /**
         * Returns the Internet Protocol (IP) address of the client or last proxy that sent the request.
         *
         * @return Internet Protocol (IP) address
         */
        String getRemoteAddr();
    }
}
