<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.operation.mapper.DealerNaMapper">
    <select id="selectDealerNaPage" resultType="com.chervon.operation.domain.dataobject.DealerNa">
        select * from dealer_na where is_deleted = 0
        <if test="search.dealerId != null and search.dealerId != ''">
            and id like concat('%', #{search.dealerId}, '%')
        </if>
        <if test="search.telephone != null and search.telephone != ''">
            and telephone like concat('%', #{search.telephone}, '%')
        </if>
        <if test="search.name != null and search.name != ''">
            and name like concat('%', #{search.name}, '%')
        </if>
        <if test="search.address != null and search.address != ''">
            and address like concat('%', #{search.address}, '%')
        </if>
        <if test="search.city != null and search.city != ''">
            and city like concat('%', #{search.city}, '%')
        </if>
        <if test="search.state != null and search.state != ''">
            and state like concat('%', #{search.state}, '%')
        </if>
        <if test="search.country != null and search.country != ''">
            and country like concat('%', #{search.country}, '%')
        </if>
        <if test="search.email != null and search.email != ''">
            and email like concat('%', #{search.email}, '%')
        </if>
        <if test="search.lat != null and search.lat != ''">
            and lat like concat('%', #{search.lat}, '%')
        </if>
        <if test="search.lng != null and search.lng != ''">
            and lng like concat('%', #{search.lng}, '%')
        </if>
        <if test="categoryArrayStr != null and categoryArrayStr != ''">
            and concat(',', category, ',') REGEXP REPLACE(#{categoryArrayStr},',','|')
        </if>
        <if test="search.createStartTime != null and search.createStartTime != ''">
            and create_time &gt;= #{search.createStartTime}
        </if>
        <if test="search.createEndTime != null and search.createEndTime != ''">
            and create_time &lt;= #{search.createEndTime}
        </if>
        <if test="search.updateStartTime != null and search.updateStartTime != ''">
            and update_time &gt;= #{search.updateStartTime}
        </if>
        <if test="search.updateEndTime != null and search.updateEndTime != ''">
            and update_time &lt;= #{search.updateEndTime}
        </if>
        order by create_time desc
    </select>

    <select id="list" resultType="com.chervon.operation.api.vo.AppDealerVo">
        select id as dealerId,name,address,city,state,country,zipcode,telephone,email,website,lat,lng,category as ca,
        ROUND(
        3959 * 2 * ASIN(
        SQRT(
        POW( SIN(( #{lat} * PI()/ 180-lat * PI()/ 180 )/ 2 ), 2 )+ COS( #{lat} * PI()/ 180 )* COS( lat * PI()/ 180 )*
        POW( SIN(( #{lng} * PI()/ 180-lng * PI()/ 180 )/ 2 ), 2 )))
        ,4) AS distance
        from dealer_na where is_deleted = 0
        <if test="category != null and category != ''">
            and category REGEXP #{category}
        </if>
        and lat <![CDATA[<]]> #{maxLat} and lat > #{minLat} and lng <![CDATA[<]]> #{maxLng} and lng > #{minLng}
        ORDER BY ROUND(
        3959 * 2 * ASIN(
        SQRT(
        POW( SIN(( #{lat} * PI()/ 180-lat * PI()/ 180 )/ 2 ), 2 )+ COS( #{lat} * PI()/ 180 )* COS( lat * PI()/ 180 )*
        POW( SIN(( #{lng} * PI()/ 180-lng * PI()/ 180 )/ 2 ), 2 )))
        ,4) ASC
        <if test="limit">
            LIMIT 5
        </if>
    </select>
    <select id="countByIds" resultType="java.lang.Integer">
        select count(0) from dealer_na where is_deleted = 0 and id in
            <foreach collection="dealerIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>
    <select id="listWithDistanceByIds" resultType="com.chervon.operation.api.vo.AppDealerVo">
        select id as dealerId,name,address,city,state,country,zipcode,telephone,email,website,lat,lng,category as ca
        <if test="lat != null and lng != null and lat!=0 and lng!=0">
            ,ROUND(3959 * 2 * ASIN(
            SQRT(
            POW( SIN(( #{lat} * PI()/ 180-lat * PI()/ 180 )/ 2 ), 2 )+ COS( #{lat} * PI()/ 180 )* COS( lat * PI()/ 180
            )*
            POW( SIN(( #{lng} * PI()/ 180-lng * PI()/ 180 )/ 2 ), 2 )))
            ,4) AS distance
        </if>
        from dealer_na where is_deleted = 0
        <if test="dealerIds != null and dealerIds.size() > 0">
            and id in
            <foreach collection="dealerIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            order by field(id,
            <foreach collection="dealerIds" item="id" open="" separator="," close="">
                #{id}
            </foreach>
            )
        </if>
    </select>


</mapper>
