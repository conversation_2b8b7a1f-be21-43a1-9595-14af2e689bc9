<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.operation.mapper.CommonOperationGuidanceMapper">
    <select id="selectManagePage" resultType="com.chervon.operation.domain.dataobject.CommonOperationGuidance">
        select t1.* from common_operation_guidance as t1 left join operation_guidance as t2 on t1.operation_guidance_id = t2.id where
        t1.is_deleted = 0 and
        t2.is_deleted = 0
        <if test="search.createBy != null and search.createBy != ''">
            and t1.create_by like concat('%', #{search.createBy}, '%')
        </if>
        <if test="search.updateBy != null and search.updateBy != ''">
            and t1.update_by like concat('%', #{search.updateBy}, '%')
        </if>
        <if test="search.instanceId != null and search.instanceId != ''">
            and t2.instance_id like concat('%', #{search.instanceId}, '%')
        </if>

        <if test="nameLangIds != null and nameLangIds.size() > 0">
            and t1.name_lang_id in
            <foreach collection="nameLangIds" item="nameLangId" open="(" separator="," close=")">
                #{nameLangId}
            </foreach>
        </if>
        <if test="search.createStartTime != null and search.createStartTime != ''">
            and t1.create_time &gt;= #{search.createStartTime}
        </if>
        <if test="search.createEndTime != null and search.createEndTime != ''">
            and t1.create_time &lt;= #{search.createEndTime}
        </if>
        <if test="search.updateStartTime != null and search.updateStartTime != ''">
            and t1.update_time &gt;= #{search.updateStartTime}
        </if>
        <if test="search.updateEndTime != null and search.updateEndTime != ''">
            and t1.update_time &lt;= #{search.updateEndTime}
        </if>
        order by t1.create_time desc
    </select>

    <select id="selectManageList" resultType="com.chervon.operation.domain.dataobject.CommonOperationGuidance">
        select t1.* from common_operation_guidance as t1 left join operation_guidance as t2 on t1.operation_guidance_id = t2.id where
        t1.is_deleted = 0 and
        t2.is_deleted = 0
        <if test="search.createBy != null and search.createBy != ''">
            and t1.create_by like concat('%', #{search.createBy}, '%')
        </if>
        <if test="search.updateBy != null and search.updateBy != ''">
            and t1.update_by like concat('%', #{search.updateBy}, '%')
        </if>
        <if test="search.instanceId != null and search.instanceId != ''">
            and t2.instance_id like concat('%', #{search.instanceId}, '%')
        </if>
        <if test="nameLangIds != null and nameLangIds.size() > 0">
            and t1.name_lang_id in
            <foreach collection="nameLangIds" item="nameLangId" open="(" separator="," close=")">
                #{nameLangId}
            </foreach>
        </if>
        <if test="search.createStartTime != null and search.createStartTime != ''">
            and t1.create_time &gt;= #{search.createStartTime}
        </if>
        <if test="search.createEndTime != null and search.createEndTime != ''">
            and t1.create_time &lt;= #{search.createEndTime}
        </if>
        <if test="search.updateStartTime != null and search.updateStartTime != ''">
            and t1.update_time &gt;= #{search.updateStartTime}
        </if>
        <if test="search.updateEndTime != null and search.updateEndTime != ''">
            and t1.update_time &lt;= #{search.updateEndTime}
        </if>
        order by t1.create_time desc
    </select>

    <select id="selectReleasePage" resultType="com.chervon.operation.domain.dataobject.CommonOperationGuidance">
        select t1.* from common_operation_guidance as t1 left join operation_guidance as t2 on t1.operation_guidance_id = t2.id where
        t1.is_deleted = 0 and
        t2.is_deleted = 0 and t1.status_code != 'will_release'
        <if test="search.applyBy != null and search.applyBy != ''">
            and t1.apply_by like concat('%', #{search.applyBy}, '%')
        </if>
        <if test="search.approveBy != null and search.approveBy != ''">
            and t1.approved_by like concat('%', #{search.approveBy}, '%')
        </if>
        <if test="search.instanceId != null and search.instanceId != ''">
            and t2.instance_id like concat('%', #{search.instanceId}, '%')
        </if>
        <if test="search.statusCode != null and search.statusCode != ''">
            and t1.status_code = #{search.statusCode}
        </if>
        <if test="nameLangIds != null and nameLangIds.size() > 0">
            and t1.name_lang_id in
            <foreach collection="nameLangIds" item="nameLangId" open="(" separator="," close=")">
                #{nameLangId}
            </foreach>
        </if>
        <if test="search.applyStartTime != null and search.applyStartTime != ''">
            and t1.apply_time &gt;= #{search.applyStartTime}
        </if>
        <if test="search.applyEndTime != null and search.applyEndTime != ''">
            and t1.apply_time &lt;= #{search.applyEndTime}
        </if>
        <if test="search.approveStartTime != null and search.approveStartTime != ''">
            and t1.approved_time &gt;= #{search.approveStartTime}
        </if>
        <if test="search.approveEndTime != null and search.approveEndTime != ''">
            and t1.approved_time &lt;= #{search.approveEndTime}
        </if>
        order by t1.create_time desc
    </select>

    <select id="selectListIdByGroupName" resultType="long">
        SELECT id
        FROM common_operation_guidance
        WHERE is_deleted = 0
          AND CONCAT(',', test_group, ',') REGEXP CONCAT(',', #{groupName}
            , ',')
    </select>

</mapper>