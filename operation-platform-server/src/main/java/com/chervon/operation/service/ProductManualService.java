package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.operation.domain.dataobject.ProductManual;
import com.chervon.operation.domain.dto.manual.product.ProductManualDto;
import com.chervon.operation.domain.dto.postsale.PostSaleBaseDto;
import com.chervon.operation.domain.vo.manual.product.ProductManualVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/22 10:55
 */
public interface ProductManualService extends IService<ProductManual> {

    /**
     * 查询产品下的用户手册
     *
     * @param productId 产品id
     * @return 用户手册列表
     */
    List<ProductManualVo> listByProductId(Long productId);

    /**
     * 编辑
     *
     * @param req 修改对象
     */
    void edit(ProductManualDto req);

    /**
     * 删除
     *
     * @param req 删除条件
     */
    void delete(PostSaleBaseDto req);

    /**
     * 获取下载地址
     *
     * @param productManualId 产品下用户手册id
     * @return url
     */
    String getUrl(Long productManualId);

    /**
     * 新增
     *
     * @param req 新增对象
     */
    void add(ProductManualDto req);

    /**
     * 详情
     *
     * @param productManualId 产品下用户手册id
     * @return 详情
     */
    ProductManualVo detail(Long productManualId);

}
