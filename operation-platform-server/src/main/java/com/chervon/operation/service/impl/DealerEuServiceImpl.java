package com.chervon.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.fleet.web.api.service.RemoteDealerFavoritesService;
import com.chervon.operation.api.vo.AppDealerEuVo;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.dataobject.DealerEu;
import com.chervon.operation.domain.dto.DealerEuDto;
import com.chervon.operation.domain.dto.DealerEuPageDto;
import com.chervon.operation.domain.dto.DealerEuRead;
import com.chervon.operation.domain.vo.DealerEuPageVo;
import com.chervon.operation.domain.vo.DealerEuVo;
import com.chervon.operation.mapper.DealerEuMapper;
import com.chervon.operation.service.DealerEuService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.chervon.common.core.constant.CommonConstant.TEXT_SIZE;
import static com.chervon.operation.api.exception.OperationErrorCode.*;

/**
 * <AUTHOR>
 * @since 2022-08-15 14:48
 **/
@Service
@Slf4j
public class DealerEuServiceImpl extends ServiceImpl<DealerEuMapper, DealerEu> implements DealerEuService {

    @DubboReference
    private RemoteDealerFavoritesService remoteDealerFavoritesService;

    @Override
    public List<AppDealerEuVo> list(double lat, double lng, Double minLat, Double maxLat, Double minLng, Double maxLng) {
        return this.getBaseMapper().list(lat, lng, minLat, maxLat, minLng, maxLng);
    }

    @Override
    public List<AppDealerEuVo> listWithDistanceByIds(Double lat, Double lng, List<Long> dealerIds) {
        return this.getBaseMapper().listWithDistanceByIds(lat, lng, dealerIds);
    }

    @Override
    public Integer countByIds(List<Long> dealerIds) {
        if(CollectionUtils.isEmpty(dealerIds)){
            return 0;
        }
        return this.getBaseMapper().countByIds(dealerIds);
    }
    @Override
    public PageResult<DealerEuPageVo> page(DealerEuPageDto req) {
        Page<DealerEu> page = this.page(new Page<>(req.getPageNum(), req.getPageSize()), new LambdaQueryWrapper<DealerEu>()
                .like(StringUtils.isNotBlank(req.getDealerId()), DealerEu::getId, req.getDealerId())
                .like(StringUtils.isNotBlank(req.getCountryCode()), DealerEu::getCountryCode, req.getCountryCode())
                .like(StringUtils.isNotBlank(req.getTitle()), DealerEu::getTitle, req.getTitle())
                .like(StringUtils.isNotBlank(req.getTown()), DealerEu::getTown, req.getTown())
                .like(StringUtils.isNotBlank(req.getCity()), DealerEu::getCity, req.getCity())
                .like(StringUtils.isNotBlank(req.getRegion()), DealerEu::getRegion, req.getRegion())
                .like(StringUtils.isNotBlank(req.getPostcode()), DealerEu::getPostcode, req.getPostcode())
                .like(StringUtils.isNotBlank(req.getPhoneNumber()), DealerEu::getPhoneNumber, req.getPhoneNumber())
                .like(StringUtils.isNotBlank(req.getEmail()), DealerEu::getEmail, req.getEmail())
                .ge(StringUtils.isNotBlank(req.getCreateStartTime()), DealerEu::getCreateTime, req.getCreateStartTime())
                .le(StringUtils.isNotBlank(req.getCreateEndTime()), DealerEu::getCreateTime, req.getCreateEndTime())
                .ge(StringUtils.isNotBlank(req.getUpdateStartTime()), DealerEu::getUpdateTime, req.getUpdateStartTime())
                .le(StringUtils.isNotBlank(req.getUpdateEndTime()), DealerEu::getUpdateTime, req.getUpdateEndTime())
                .orderByDesc(BaseDo::getCreateTime)
        );
        PageResult<DealerEuPageVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        res.setList(page.getRecords().stream().map(e -> {
            DealerEuPageVo vo = new DealerEuPageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setDealerId(e.getId());
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    @Override
    public DealerEuVo detail(Long dealerId) {
        if (dealerId == null) {
            return null;
        }
        DealerEu dealerEu = this.getById(dealerId);
        DealerEuVo vo = new DealerEuVo();
        BeanUtils.copyProperties(dealerEu, vo);
        vo.setDealerId(dealerEu.getId());
        return vo;
    }

    @Override
    public void add(DealerEuDto req) {
        check(req, false);
        DealerEu dealerEu = new DealerEu();
        BeanUtils.copyProperties(req, dealerEu);
        if (StringUtils.isNotBlank(req.getGeolocation())) {
            String[] split = req.getGeolocation().split(",");
            if (split.length == 2) {
                try {
                    Double.parseDouble(split[0]);
                    dealerEu.setLat(split[0]);
                    Double.parseDouble(split[1]);
                    dealerEu.setLng(split[1]);
                } catch (Exception e) {
                    log.error("geolocation parse error");
                }
            }
        }
        this.save(dealerEu);
    }

    private void check(DealerEuDto req, boolean isEdit) {
        if (req == null) {
            req = new DealerEuDto();
        }
        if (isEdit && req.getDealerId() == null) {
            throw ExceptionMessageUtil.getException(OPERATION_DEALER_EU_ID_NULL);
        } else if (isEdit) {
            DealerEu dealerEu = this.getById(req.getDealerId());
            if (dealerEu == null) {
                throw ExceptionMessageUtil.getException(OPERATION_DEALER_EU_NOT_EXIST);
            }
        }
        if (StringUtils.isBlank(req.getTitle())) {
            throw ExceptionMessageUtil.getException(OPERATION_DEALER_EU_TITLE_BLANK);
        }
        if (StringUtils.isBlank(req.getCountryCode())) {
            throw ExceptionMessageUtil.getException(OPERATION_DEALER_EU_COUNTRY_BLANK);
        }
        if (StringUtils.isBlank(req.getPostcode())) {
            throw ExceptionMessageUtil.getException(OPERATION_DEALER_EU_POSTCODE_BLANK);
        }
        LambdaQueryWrapper<DealerEu> wrapper = new LambdaQueryWrapper<DealerEu>()
                .eq(DealerEu::getCountryCode, req.getCountryCode())
                .eq(DealerEu::getTitle, req.getTitle())
                .eq(DealerEu::getPostcode, req.getPostcode());
        if (isEdit) {
            wrapper.ne(DealerEu::getId, req.getDealerId());
        }
        long count = this.count(wrapper);
        if (count > 0) {
            throw ExceptionMessageUtil.getException(OPERATION_DEALER_EU_TITLE_EXIST);
        }
    }

    @Override
    public void edit(DealerEuDto req) {
        check(req, true);
        DealerEu dealerEu = new DealerEu();
        BeanUtils.copyProperties(req, dealerEu);
        if (StringUtils.isNotBlank(req.getGeolocation())) {
            String[] split = req.getGeolocation().split(",");
            if (split.length == 2) {
                try {
                    Double.parseDouble(split[0]);
                    dealerEu.setLat(split[0]);
                    Double.parseDouble(split[1]);
                    dealerEu.setLng(split[1]);
                } catch (Exception e) {
                    log.error("geolocation parse error");
                    dealerEu.setLat("");
                    dealerEu.setLng("");
                }
            }
        }
        dealerEu.setId(req.getDealerId());
        this.updateById(dealerEu);
    }

    @Override
    public void delete(List<Long> dealerIds) {
        if (CollectionUtils.isEmpty(dealerIds)) {
            throw ExceptionMessageUtil.getException(OPERATION_DEALER_EU_BATCH_REMOVE_IDS_EMPTY);
        }
        this.removeBatchByIds(dealerIds);
        remoteDealerFavoritesService.removeByDealerIds(dealerIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> importDealer(List<DealerEuRead> data, String countryCode) {
        List<String> res = new ArrayList<>();
        if (!validImport(data, res)) {
            return res;
        }
        List<DealerEu> oldList = this.list(new LambdaQueryWrapper<DealerEu>().eq(DealerEu::getCountryCode, countryCode).select(DealerEu::getId));
        List<Long> oldIds = oldList.stream().map(BaseDo::getId).collect(Collectors.toList());
        if (!oldIds.isEmpty()) {
            this.removeBatchByIds(oldIds);
            remoteDealerFavoritesService.removeByDealerIds(oldIds);
        }
        List<DealerEu> collect = data.stream().map(e -> {
            DealerEu dealerEu = new DealerEu();
            BeanUtils.copyProperties(e, dealerEu);
            dealerEu.setCountryCode(countryCode);
            if (StringUtils.isNotBlank(e.getGeolocation())) {
                String[] split = e.getGeolocation().split(",");
                if (split.length == 2) {
                    try {
                        Double.parseDouble(split[0]);
                        dealerEu.setLat(split[0]);
                        Double.parseDouble(split[1]);
                        dealerEu.setLng(split[1]);
                    } catch (Exception ex) {
                        log.error("geolocation parse error");
                    }
                }
            }
            return dealerEu;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
        return null;
    }

    private boolean validImport(List<DealerEuRead> data, List<String> res) {
        List<Integer> titleNullFlag = new ArrayList<>();
        List<Integer> postcodeNullFlag = new ArrayList<>();
//        Map<String, List<Integer>> titlePostcodeMap = new HashMap<>();
        List<Integer> titleSizeFlag = new ArrayList<>();
        List<Integer> addressOneSizeFlag = new ArrayList<>();
        List<Integer> addressTwoSizeFlag = new ArrayList<>();
        List<Integer> townSizeFlag = new ArrayList<>();
        List<Integer> citySizeFlag = new ArrayList<>();
        List<Integer> regionSizeFlag = new ArrayList<>();
        List<Integer> postcodeSizeFlag = new ArrayList<>();
        List<Integer> phoneNumberSizeFlag = new ArrayList<>();
        List<Integer> emailSizeFlag = new ArrayList<>();
        List<Integer> geolocationSizeFlag = new ArrayList<>();
        List<Integer> langCodeSizeFlag = new ArrayList<>();
        List<Integer> premiumSizeFlag = new ArrayList<>();
        List<Integer> proXRangeSellerSizeFlag = new ArrayList<>();
        List<Integer> rideOnSellerSizeFlag = new ArrayList<>();
        List<Integer> adSizeFlag = new ArrayList<>();

        for (int i = 2, j = data.size() + 2; i < j; i++) {
            DealerEuRead read = data.get(i - 2);

            if (StringUtils.isBlank(read.getTitle())) {
                titleNullFlag.add(i);
            } else if (read.getTitle().length() > TEXT_SIZE) {
                titleSizeFlag.add(i);
            }

            if (StringUtils.isBlank(read.getPostcode())) {
                postcodeNullFlag.add(i);
            } else if (read.getPostcode().length() > TEXT_SIZE) {
                postcodeSizeFlag.add(i);
            }

//            String key = (read.getTitle() == null ? "" : read.getTitle()) + "##" + (read.getPostcode() == null ? "" : read.getPostcode());

//            if (titlePostcodeMap.get(key) == null) {
//                List<Integer> ids = new ArrayList<>();
//                ids.add(i);
//                titlePostcodeMap.put(key, ids);
//            } else {
//                titlePostcodeMap.get(key).add(i);
//            }

            if (StringUtils.isNotBlank(read.getAddressOne()) && read.getAddressOne().length() > 300) {
                addressOneSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getAddressTwo()) && read.getAddressTwo().length() > 300) {
                addressTwoSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getTown()) && read.getTown().length() > TEXT_SIZE) {
                townSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getCity()) && read.getCity().length() > TEXT_SIZE) {
                citySizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getRegion()) && read.getRegion().length() > TEXT_SIZE) {
                regionSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getPhoneNumber()) && read.getPhoneNumber().length() > TEXT_SIZE) {
                phoneNumberSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getEmail()) && read.getEmail().length() > TEXT_SIZE) {
                emailSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getGeolocation()) && read.getGeolocation().length() > TEXT_SIZE) {
                geolocationSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getLangCode()) && read.getLangCode().length() > TEXT_SIZE) {
                langCodeSizeFlag.add(i);
            }


            if (StringUtils.isNotBlank(read.getPremium()) && read.getPremium().length() > TEXT_SIZE) {
                premiumSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getProXRangeSeller()) && read.getProXRangeSeller().length() > TEXT_SIZE) {
                proXRangeSellerSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getRideOnSeller()) && read.getRideOnSeller().length() > TEXT_SIZE) {
                rideOnSellerSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getAd()) && read.getAd().length() > TEXT_SIZE) {
                adSizeFlag.add(i);
            }
        }
        // 检测title是否必填
        if (titleNullFlag.size() > 0) {
            res.add("第" + titleNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Title】为空");
        }
        // 检测title长度
        if (titleSizeFlag.size() > 0) {
            res.add("第" + titleSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Title】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测postcode是否必填
        if (postcodeNullFlag.size() > 0) {
            res.add("第" + postcodeNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Postcode】为空");
        }
        // 检测postcode长度
        if (postcodeSizeFlag.size() > 0) {
            res.add("第" + postcodeSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Postcode】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测title postcode是否唯一
//        for (Map.Entry<String, List<Integer>> entry : titlePostcodeMap.entrySet()) {
//            List<Integer> value = entry.getValue();
//            if (value.size() != 1) {
//                res.add("第" + value.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Title、Postcode】重复");
//            }
//        }
        // 检测addressOne长度
        if (addressOneSizeFlag.size() > 0) {
            res.add("第" + addressOneSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Address_one】内容超过" + 300 + "个字符");
        }
        // 检测addressTwo长度
        if (addressTwoSizeFlag.size() > 0) {
            res.add("第" + addressTwoSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Address_two】内容超过" + 300 + "个字符");
        }
        // 检测town长度
        if (townSizeFlag.size() > 0) {
            res.add("第" + townSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Town】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测city长度
        if (citySizeFlag.size() > 0) {
            res.add("第" + citySizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【City】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测region长度
        if (regionSizeFlag.size() > 0) {
            res.add("第" + regionSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Region】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测Phone_number长度
        if (phoneNumberSizeFlag.size() > 0) {
            res.add("第" + phoneNumberSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Phone_number】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测email长度
        if (emailSizeFlag.size() > 0) {
            res.add("第" + emailSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Email】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测Geolocation长度
        if (geolocationSizeFlag.size() > 0) {
            res.add("第" + geolocationSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Geolocation】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测LangCode长度
        if (langCodeSizeFlag.size() > 0) {
            res.add("第" + langCodeSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Langcode】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测Premium长度
        if (premiumSizeFlag.size() > 0) {
            res.add("第" + premiumSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Premium】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测pro_x_range_seller长度
        if (proXRangeSellerSizeFlag.size() > 0) {
            res.add("第" + proXRangeSellerSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Pro_x_range_seller】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测Ride_on_seller长度
        if (rideOnSellerSizeFlag.size() > 0) {
            res.add("第" + rideOnSellerSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【Ride_on_seller】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测ad长度
        if (adSizeFlag.size() > 0) {
            res.add("第" + adSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【AD】内容超过" + TEXT_SIZE + "个字符");
        }
        return res.isEmpty();
    }
}
