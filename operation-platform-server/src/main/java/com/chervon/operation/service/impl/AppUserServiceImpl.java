package com.chervon.operation.service.impl;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.operation.domain.bo.DictBo;
import com.chervon.operation.domain.bo.DictNodeBo;
import com.chervon.operation.domain.vo.app.AppUserExcel;
import com.chervon.operation.service.AppUserService;
import com.chervon.operation.service.DictService;
import com.chervon.usercenter.api.dto.AppUserPageDto;
import com.chervon.usercenter.api.service.RemoteAppUserService;
import com.chervon.usercenter.api.vo.AppUserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/20 14:42
 */
@Service
@Slf4j
public class AppUserServiceImpl implements AppUserService {

    private static final String APP_PRESENCE_DIST = "appPresence";

    private static final String APP_TYPE_DIST = "appType";

    private static final String USER_TYPE_DIST = "userType";

    private static final String USER_SOURCE_DIST = "userSource";

    @DubboReference
    private RemoteAppUserService remoteAppUserService;

    @Autowired
    private DictService dictService;

    @Override
    public PageResult<AppUserVo> page(AppUserPageDto req) {
        return remoteAppUserService.pageAppUser(req);
    }

    @Override
    public List<AppUserExcel> listData(AppUserPageDto req) {
        List<AppUserVo> appUsers = remoteAppUserService.listAppUser(req);
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Arrays.asList(APP_PRESENCE_DIST, APP_TYPE_DIST, USER_TYPE_DIST, USER_SOURCE_DIST));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        return appUsers.stream().map(e -> {
            AppUserExcel excel = new AppUserExcel();
            excel.setAppVersion(e.getAppVersion());
            excel.setEmail(e.getEmail());
            excel.setUserId(e.getUserId());
            excel.setLastLoginTime(DateTimeZoneUtil.format(e.getLastLoginTime(), req.getZone()));
            excel.setRegisterTime(DateTimeZoneUtil.format(e.getRegisterTime(), req.getZone()));
            excel.setAppPresence(collect.get(APP_PRESENCE_DIST).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getAppPresenceCode()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            excel.setIp(e.getIp());
            excel.setPhoneModel(e.getPhoneModel());
            excel.setPhoneOsVersion(e.getPhoneOsVersion());

            excel.setAppType(collect.get(APP_TYPE_DIST).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getAppTypeCode()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            excel.setUserType(collect.get(USER_TYPE_DIST).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getUserTypeCode()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            excel.setUserSource(collect.get(USER_SOURCE_DIST).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getUserSourceCode()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            return excel;
        }).collect(Collectors.toList());
    }
}
