package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.app.api.vo.UserSettingBo;
import com.chervon.operation.api.dto.MessagePushResultCountDto;
import com.chervon.operation.domain.dataobject.MarketingMsg;
import com.chervon.operation.domain.dto.app.MarketingMsgDto;
import com.chervon.operation.domain.dto.app.MarketingMsgManagePageDto;
import com.chervon.operation.domain.dto.app.MarketingMsgOperationDto;
import com.chervon.operation.domain.dto.app.MarketingMsgReleasePageDto;
import com.chervon.operation.domain.vo.app.MarketingMsgManagePageVo;
import com.chervon.operation.domain.vo.app.MarketingMsgReleasePageVo;
import com.chervon.operation.domain.vo.app.MarketingMsgVo;
import com.chervon.operation.domain.vo.message.MessageRecordResultVo;
import com.chervon.operation.domain.vo.message.MessageRecordSearchVo;
import com.chervon.usercenter.api.vo.AppUserVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/20 18:53
 */
public interface MarketingMsgService extends IService<MarketingMsg> {

    /**
     * 营销消息管理分页查询
     *
     * @param req 查询套件
     * @return 分页数据
     */
    PageResult<MarketingMsgManagePageVo> managePage(MarketingMsgManagePageDto req);

    /**
     * 营销消息发布管理分页查询
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<MarketingMsgReleasePageVo> releasePage(MarketingMsgReleasePageDto req);

    /**
     * 营销消息发布管理分页查询
     *
     * @return 分页数据
     */
    PageResult<MessageRecordResultVo> recordPage(MessageRecordSearchVo messageRecordVo);

    /**
     * 营销消息发布管理分页查询
     *
     * @return 分页数据
     */
    List<MessageRecordResultVo> recordList(MessageRecordSearchVo messageRecordVo);

    /**
     * 保存
     *
     * @param req 入参
     */
    void save(MarketingMsgDto req);

    /**
     * 编辑
     *
     * @param req 入参
     */
    void update(MarketingMsgDto req);

    /**
     * 编辑
     */
    void updateMsgCount(List<MessagePushResultCountDto> values);

    /**
     * 详情
     *
     * @param id 营销消息id
     * @return 消息详情
     */
    MarketingMsgVo detail(Long id);

    /**
     * 复制
     *
     * @param req 入参
     */
    void copy(MarketingMsgDto req);

    /**
     * 操作
     *
     * @param op 操作参数
     */
    void operation(MarketingMsgOperationDto op);

    /**
     * 查询驳回原因
     *
     * @param op 入参
     * @return 驳回原因
     */
    String view(MarketingMsgOperationDto op);

    /**
     * 删除
     *
     * @param id 营销消息id
     */
    void delete(Long id);

    /**
     * APP启动后检测是否有需要推送的营销消息
     *
     * @param appUserVo     APP用户RPC类
     * @param userSettingBo 用户设置RPC类
     * @param groupNames    用户所属分组名列表
     */
    void checkAfterAppStarted(AppUserVo appUserVo, UserSettingBo userSettingBo, List<String> groupNames);

    /**
     * 发送消息
     *
     * @param marketingMsg 消息对象
     */
    void pushMessage(MarketingMsg marketingMsg);
}
