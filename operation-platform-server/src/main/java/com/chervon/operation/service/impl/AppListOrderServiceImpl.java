package com.chervon.operation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.operation.api.enums.AppListOrderItemCodeEnum;
import com.chervon.operation.api.vo.CategoryVo;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.dataobject.AppListOrder;
import com.chervon.operation.domain.dataobject.Category;
import com.chervon.operation.domain.dto.app.AppListOrderDto;
import com.chervon.operation.domain.dto.app.AppListOrderElementDto;
import com.chervon.operation.domain.vo.app.AppListOrderElementVo;
import com.chervon.operation.domain.vo.app.AppListOrderListVo;
import com.chervon.operation.domain.vo.app.AppListOrderVo;
import com.chervon.operation.mapper.AppListOrderMapper;
import com.chervon.operation.service.AppListOrderService;
import com.chervon.operation.service.CategoryService;
import com.chervon.technology.api.RemoteTechProductOperationService;
import com.chervon.technology.api.vo.ProductOperationVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.chervon.operation.api.exception.OperationErrorCode.OPERATION_APP_LIST_ORDER_ITEM_ILLEGAL;
import static com.chervon.operation.api.exception.OperationErrorCode.OPERATION_APP_LIST_ORDER_ITEM_NULL;

/**
 * <AUTHOR>
 * @date 2022/8/26 12:12
 */
@Service
@Slf4j
public class AppListOrderServiceImpl extends ServiceImpl<AppListOrderMapper, AppListOrder> implements AppListOrderService {

    @DubboReference
    private RemoteTechProductOperationService remoteTechProductOperationService;

    @Autowired
    private CategoryService categoryService;

    @Override
    public AppListOrderVo detail(String itemCode) {
        if (itemCode == null || !Arrays.asList(AppListOrderItemCodeEnum.CATEGORY.getType(), AppListOrderItemCodeEnum.PID.getType()).contains(itemCode)) {
            return new AppListOrderVo();
        }
        return innerDetail(itemCode);
    }

    private AppListOrderVo innerDetail(String itemCode) {
        AppListOrderVo res = new AppListOrderVo();
        //组装返回
        res.setItemCode(itemCode);
        List<AppListOrderElementVo> elements = new ArrayList<>();
        res.setElements(elements);

        //品类顺序
        if (StringUtils.equals(itemCode,AppListOrderItemCodeEnum.CATEGORY.getType())) {
            //按照app列表显示顺序查询品类
            List<CategoryVo> categoryVoList = categoryService.listOrderByAppShowOrder();
            //拼接返回参数
            for (CategoryVo categoryVo:categoryVoList) {
                    AppListOrderElementVo vo = new AppListOrderElementVo();
                    vo.setElementCode(String.valueOf(categoryVo.getId()));
                    vo.setElement(categoryVo.getCategoryName().getMessage());
                    vo.setIsShow(categoryVo.getAppShowStatus());
                    elements.add(vo);
            }
        }
        //商品型号
        else if (StringUtils.equals(itemCode,AppListOrderItemCodeEnum.PID.getType())) {
            //按照app列表显示顺序查询品类
            List<ProductOperationVo> productOperationVos = remoteTechProductOperationService.listEGOReleasedProductOrderByAppShowOrder();
            //拼接返回参数
            for (ProductOperationVo productOperationVo:productOperationVos) {
                    AppListOrderElementVo vo = new AppListOrderElementVo();
                    vo.setElementCode(String.valueOf(productOperationVo.getId()));
                    vo.setElement(productOperationVo.getCommodityModel());
                    vo.setIsShow(productOperationVo.getAppShowStatus());
                    elements.add(vo);
            }
        }
        return res;
    }

    @Override
    public void edit(AppListOrderDto req) {
        String itemCode = req.getItemCode();
        //check param itemCode
        if (itemCode == null || !Arrays.asList(AppListOrderItemCodeEnum.CATEGORY.getType(), AppListOrderItemCodeEnum.PID.getType()).contains(itemCode)) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_LIST_ORDER_ITEM_ILLEGAL);
        }
        //get record by itemCode
        AppListOrder appListOrder = getByItemCode(itemCode);
        if (appListOrder == null) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_LIST_ORDER_ITEM_NULL);
        }
        List<AppListOrderElementDto> elements = req.getElements();
        //更新品类显示顺序，显示隐藏状态
        if (StringUtils.equals(itemCode,AppListOrderItemCodeEnum.CATEGORY.getType())) {
            List<Category> categories=new ArrayList<>();
            for(int i=0;i<elements.size();i++){
                Category category=new Category();
                category.setId(Long.valueOf(elements.get(i).getElementCode()));
                category.setAppShowStatus(elements.get(i).getIsShow());
                category.setAppShowOrder(i);
                categories.add(category);
            }
             //批量更新品类
            categoryService.updateBatchCatoryById(categories);
        }
        //更新产品显示顺序，显示隐藏状态
        else if(StringUtils.equals(itemCode,AppListOrderItemCodeEnum.PID.getType())) {
            List<ProductOperationVo> productOperationVos=new ArrayList<>();
            for(int i=0;i<elements.size();i++){
                ProductOperationVo productOperationVo=new ProductOperationVo();
                productOperationVo.setId(Long.valueOf(elements.get(i).getElementCode()));
                productOperationVo.setAppShowStatus(elements.get(i).getIsShow());
                productOperationVo.setAppShowOrder(i);
                productOperationVos.add(productOperationVo);
            }
            //批量更新产品信息
            remoteTechProductOperationService.updateBatchProductById(productOperationVos);
        }
        appListOrder.setUpdateBy(null);
        updateById(appListOrder);
    }

    /**
     * 根据ItemCode获取App列表顺序
     * @param itemCode
     * @return
     */
    private AppListOrder getByItemCode(String itemCode) {
        return getOne(Wrappers.<AppListOrder>lambdaQuery()
                .eq(AppListOrder::getItemCode, itemCode));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AppListOrderListVo> listAll() {
        List<AppListOrder> list = listByItemCodes(Arrays.asList(AppListOrderItemCodeEnum.CATEGORY.getType(), AppListOrderItemCodeEnum.PID.getType()));
        List<AppListOrderListVo> res = new ArrayList<>();
        list.forEach(e -> {
            AppListOrderListVo vo = new AppListOrderListVo();
            vo.setItemCode(e.getItemCode());
            vo.setUpdateTime(e.getUpdateTime());
            vo.setUpdateBy(e.getUpdateBy());
            res.add(vo);
        });
        return res;
    }

    /**
     * 根据itemcodes查询列表
     * @param itemCodes
     * @return
     */
    private List<AppListOrder> listByItemCodes(List<String> itemCodes) {
        return list(Wrappers.<AppListOrder>lambdaQuery()
                .in(AppListOrder::getItemCode,itemCodes ));
    }

}
