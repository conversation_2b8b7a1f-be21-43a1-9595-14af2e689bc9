package com.chervon.operation.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.enums.ApplicationEnum;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.bo.DictBo;
import com.chervon.operation.domain.bo.DictNodeBo;
import com.chervon.operation.domain.dataobject.Category;
import com.chervon.operation.domain.dataobject.Parts;
import com.chervon.operation.domain.dataobject.ProductParts;
import com.chervon.operation.domain.dto.faq.parts.PartsFaqDto;
import com.chervon.operation.domain.dto.faq.parts.PartsFaqListDto;
import com.chervon.operation.domain.dto.link.parts.PartsLinkDto;
import com.chervon.operation.domain.dto.manual.parts.PartsManualDto;
import com.chervon.operation.domain.dto.operationguidance.parts.PartsOperationGuidanceDto;
import com.chervon.operation.domain.dto.parts.*;
import com.chervon.operation.domain.vo.faq.parts.PartsFaqVo;
import com.chervon.operation.domain.vo.link.parts.PartsLinkVo;
import com.chervon.operation.domain.vo.manual.parts.PartsManualVo;
import com.chervon.operation.domain.vo.operationguidance.parts.PartsOperationGuidanceVo;
import com.chervon.operation.domain.vo.parts.PartsExcel;
import com.chervon.operation.domain.vo.parts.PartsPageVo;
import com.chervon.operation.domain.vo.parts.PartsVo;
import com.chervon.operation.mapper.PartsMapper;
import com.chervon.operation.mapper.ProductPartsMapper;
import com.chervon.operation.service.*;
import com.chervon.technology.api.RemoteTechProductOperationService;
import com.chervon.technology.api.vo.CommonProductVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chervon.common.core.constant.CommonConstant.*;
import static com.chervon.operation.api.exception.OperationErrorCode.*;

/**
 * <AUTHOR>
 * @date 2022-08-10
 */
@Service
@Slf4j
public class PartsServiceImpl extends ServiceImpl<PartsMapper, Parts> implements PartsService {

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;


    @DubboReference
    private RemoteTechProductOperationService remoteTechProductOperationService;

    @Autowired
    private DictService dictService;

    @Autowired
    private AwsProperties awsProperties;

    @Autowired
    private PartsLinkService partsLinkService;

    @Autowired
    private PartsManualService partsManualService;

    @Autowired
    private PartsOperationGuidanceService partsOperationGuidanceService;

    @Autowired
    private PartsFaqService partsFaqService;

    @Resource
    private ProductPartsMapper productPartsMapper;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private UserManualService userManualService;

    private static final String PARTS_TYPE = "partsType";

    private List<Long> findNameLangIds(String name) {
        List<Long> res = new ArrayList<>();
        if (StringUtils.isBlank(name)) {
            return res;
        }
        List<Parts> list = this.list();
        List<Long> nameLangIds = list.stream().map(Parts::getNameLangId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nameLangIds)) {
            return res;
        }
        Map<@NotBlank String, @NotNull List<MultiLanguageBo>> map = remoteMultiLanguageService.listByTextLike(new HashMap<String, List<Long>>() {{
            put(name, nameLangIds);
        }}, LocaleContextHolder.getLocale().getLanguage());
        return map.get(name).stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
    }

    @Override
    public PageResult<PartsPageVo> page(PartsPageDto req) {
        List<Long> nameLangIds = findNameLangIds(req.getName());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getName()) && CollectionUtils.isEmpty(nameLangIds)) {
            return new PageResult<>(req.getPageNum(), req.getPageSize(), 0);
        }
        IPage<Parts> page = this.getBaseMapper().selectPartsPage(new Page<>(req.getPageNum(), req.getPageSize()), req, nameLangIds);
        PageResult<PartsPageVo> res = new PageResult<>(req.getPageNum(), req.getPageSize(), page.getTotal());
        res.setList(page.getRecords().stream().map(e -> {
            PartsPageVo vo = new PartsPageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setPartsId(e.getId());
            vo.setName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(e.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setRemark(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(e.getRemarkLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    private void check(PartsDto req) {
        if (StringUtils.isBlank(req.getIconType()) || !Arrays.asList("0", "1").contains(req.getIconType())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_TYPE_ERROR);
        }
        if (StringUtils.isBlank(req.getName())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_NAME_NULL);
        }
        if (StringUtils.isBlank(req.getType1())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_TYPE1_NULL);
        }
        if (StringUtils.isBlank(req.getUploadIconName())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_UPLOAD_ICON_NAME_NULL);
        }
        req.setRemark(Optional.ofNullable(req.getRemark()).orElse(""));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(PartsDto req) {
        check(req);
        Parts one = this.getOne(new LambdaQueryWrapper<Parts>().eq(Parts::getCommodityModel, req.getCommodityModel()));
        if (one != null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_MODEL_ALREADY_EXIST, req.getCommodityModel());
        }
        Map<String, String> map = new HashMap<>();
        map.put("1", req.getName());
        if (StringUtils.isNotBlank(req.getRemark())) {
            map.put("2", req.getRemark());
        }
        Map<String, MultiLanguageBo> languages = remoteMultiLanguageService.simpleCreateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), map, LocaleContextHolder.getLocale().getLanguage());
        one = new Parts();
        BeanUtils.copyProperties(req, one);
        MultiLanguageBo name = languages.getOrDefault("1", new MultiLanguageBo());
        one.setNameLangId(name.getLangId());
        one.setNameLangCode(name.getLangCode());
        MultiLanguageBo remark = languages.getOrDefault("2", new MultiLanguageBo());
        one.setRemarkLangId(remark.getLangId());
        one.setRemarkLangCode(remark.getLangCode());
        one.setIconUrl(UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), one.getUploadIconName()));
        this.save(one);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(PartsDto req) {
        check(req);
        if (req.getPartsId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_ID_NULL);
        }
        Parts parts = this.getById(req.getPartsId());
        if (parts == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_NULL, req.getPartsId());
        }
        if (!StringUtils.equals(parts.getCommodityModel(), req.getCommodityModel())) {
            Parts one = this.getOne(new LambdaQueryWrapper<Parts>().eq(Parts::getCommodityModel, req.getCommodityModel()).ne(Parts::getId, req.getPartsId()));
            if (one != null) {
                throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_MODEL_ALREADY_EXIST, req.getCommodityModel());
            }
        }
        Parts one = new Parts();
        BeanUtils.copyProperties(req, one);
        one.setId(req.getPartsId());
        remoteMultiLanguageService.simpleUpdateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), parts.getNameLangId(), req.getName(), LocaleContextHolder.getLocale().getLanguage());
        if (parts.getRemarkLangId() != null) {
            remoteMultiLanguageService.simpleUpdateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), parts.getRemarkLangId(), req.getRemark(), LocaleContextHolder.getLocale().getLanguage());
        } else if (StringUtils.isNotBlank(req.getRemark())) {
            MultiLanguageBo bo = remoteMultiLanguageService.simpleCreateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), req.getRemark(), LocaleContextHolder.getLocale().getLanguage());
            one.setRemarkLangId(bo.getLangId());
            one.setRemarkLangCode(bo.getLangCode());
        }
        one.setIconUrl(UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), one.getUploadIconName()));
        this.updateById(one);
    }

    @Override
    public PartsVo detail(Long partsId) {
        if (partsId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_ID_NULL);
        }
        Parts parts = this.getById(partsId);
        PartsVo res = new PartsVo();
        if (parts == null) {
            return res;
        }
        BeanUtils.copyProperties(parts, res);
        res.setPartsId(partsId);
        res.setName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(parts.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        res.setRemark(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(parts.getRemarkLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        res.setShortDescription(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(parts.getShortDescriptionLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        res.setLongDescription(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(parts.getLongDescriptionLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        res.setTechnicalSpecification(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(parts.getTechnicalSpecificationLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        res.setManual(partsManualService.listByPartsId(partsId));
        res.setGuidance(partsOperationGuidanceService.listByPartsId(partsId));
        res.setFaq(partsFaqService.listByPartsId(partsId));
        res.setLink(partsLinkService.listByPartsId(partsId));
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long partsId) {
        if (partsId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_ID_NULL);
        }
        Parts parts = this.getById(partsId);
        if (parts == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_NULL, partsId);
        }
        List<PartsManualVo> partsManualVos = partsManualService.listByPartsId(partsId);
        for (PartsManualVo partsManualVo : partsManualVos) {
            manualDelete(partsManualVo.getPartsManualId());
        }
        List<PartsFaqVo> partsFaqVos = partsFaqService.listByPartsId(partsId);
        for (PartsFaqVo partsFaqVo : partsFaqVos) {
            faqDelete(partsFaqVo.getPartsFaqId());
        }
        List<PartsOperationGuidanceVo> partsOperationGuidanceVos = partsOperationGuidanceService.listByPartsId(partsId);
        for (PartsOperationGuidanceVo partsOperationGuidanceVo : partsOperationGuidanceVos) {
            operationGuidanceDelete(partsOperationGuidanceVo.getPartsOperationGuidanceId());
        }
        // 清理多语言
        cleanLang(parts);
        if (productPartsMapper.selectCount(new LambdaQueryWrapper<ProductParts>().eq(ProductParts::getPartsId, partsId)) > 0) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_PRODUCT_ALREADY_RELATED);
        }
        this.removeById(partsId);
    }

    private void cleanLang(Parts parts) {
        List<Long> langIds = new ArrayList<>();
        if (null != parts.getNameLangId()) {
            langIds.add(parts.getNameLangId());
        }
        if (null != parts.getRemarkLangId()) {
            langIds.add(parts.getRemarkLangId());
        }
        if (null != parts.getLongDescriptionLangId()) {
            langIds.add(parts.getLongDescriptionLangId());
        }
        if (null != parts.getShortDescriptionLangId()) {
            langIds.add(parts.getShortDescriptionLangId());
        }
        if (null != parts.getTechnicalSpecificationLangId()) {
            langIds.add(parts.getTechnicalSpecificationLangId());
        }
        if (langIds.size() > 0) {
            remoteMultiLanguageService.deleteByLangIds(langIds);
        }
    }

    @Override
    public PageResult<CommonProductVo> productPage(PartsProductPageDto req) {
        PageResult<CommonProductVo> res = new PageResult<>(req.getPageNum(), req.getPageSize());
        if (req.getPartsId() == null) {
            return res;
        }
        List<ProductParts> productParts = productPartsMapper.selectList(new LambdaQueryWrapper<ProductParts>().eq(ProductParts::getPartsId, req.getPartsId()));
        if (CollectionUtils.isEmpty(productParts)) {
            return res;
        }
        PageResult<CommonProductVo> page = remoteTechProductOperationService.page(LocaleContextHolder.getLocale().getLanguage(), productParts.stream().map(ProductParts::getProductId).collect(Collectors.toList()), req.getProductId(), req.getCategoryId(), req.getModel(), req.getCommodityModel(), req.getProductName(), req.getPageNum(), req.getPageSize());
        if (CollectionUtils.isEmpty(page.getList())) {
            return res;
        }
        List<Long> categoryIds = page.getList().stream().map(CommonProductVo::getCategoryId).collect(Collectors.toList());
        List<Category> categories = new ArrayList<>();
        if (!CollectionUtils.isEmpty(categoryIds)) {
            categories = categoryService.listByIds(categoryIds);
        }
        Map<Long, String> collect = categories.stream().collect(Collectors.toMap(BaseDo::getId, Category::getCategoryName));
        List<String> ids = new ArrayList<>();
        ids.addAll(collect.values());
        ids.addAll(page.getList().stream().map(CommonProductVo::getProductName).collect(Collectors.toList()));
        List<MultiLanguageBo> bos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ids)) {
            bos = remoteMultiLanguageService.listByIds(ids);
        }
        Map<Long, String> map = bos.stream().collect(Collectors.toMap(MultiLanguageBo::getLangId, MultiLanguageBo::getLangCode));
        page.getList().forEach(e -> {
            try {
                String nameId = collect.get(e.getCategoryId());
                String nameCode = map.get(Long.parseLong(nameId));
                e.setCategoryName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(nameCode).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
                String productNameCode = map.get(Long.parseLong(e.getProductName()));
                e.setProductName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(productNameCode).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        });
        return page;
    }

    @Override
    public List<String> importParts(MultipartFile file) {
        List<String> res = new ArrayList<>();
        List<PartsRead> data;
        try {
            data = EasyExcel.read(file.getInputStream()).head(PartsRead.class).sheet().doReadSync();
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(OPERATION_PARTS_READ_EXCEL_ERROR);
        }
        if (CollectionUtils.isEmpty(data)) {
            throw ExceptionMessageUtil.getException(OPERATION_PARTS_READ_EXCEL_EMPTY);
        }
        if (data.size() > 5000) {
            throw ExceptionMessageUtil.getException(OPERATION_PARTS_READ_EXCEL_MORE_THAN_5000);
        }
        if (!validImport(data, res)) {
            return res;
        }
        List<String> collect = data.stream().map(PartsRead::getPartsId).collect(Collectors.toList());
        List<Parts> or = new ArrayList<>();
        if (!CollectionUtils.isEmpty(collect)) {
            or = this.listByIds(collect);
        }
        Map<Long, Parts> orMap = or.stream().collect(Collectors.toMap(BaseDo::getId, Function.identity()));

        Map<Long, String> mUpdate = new HashMap<>();
        Map<String, String> mCreate = new HashMap<>();
        final int[] f = {1};
        List<Parts> newParts = data.stream().map(e -> {
            Parts parts = new Parts();
            if (StringUtils.isNotBlank(e.getPartsId())) {
                parts.setId(Long.parseLong(e.getPartsId()));
            }
            parts.setCommodityModel(e.getCommodityModel());
            parts.setIconType("1");
            parts.setIconUrl(e.getIconUrl());
            parts.setType1(e.getType1());
            parts.setType2(e.getType2());
            parts.setName(e.getName());
            parts.setRemark(e.getRemark());
            parts.setShortDescription(e.getShortDescription());
            parts.setLongDescription(e.getLongDescription());
            parts.setTechnicalSpecification(e.getTechnicalSpecification());
            parts.setLink(e.getLink());
            if (StringUtils.isNotBlank(e.getPartsId())) {
                parts.setId(Long.parseLong(e.getPartsId()));
                Parts p = orMap.get(parts.getId());
                if (p != null) {
                    if (p.getNameLangId() != null) {
                        mUpdate.put(p.getNameLangId(), e.getName());
                        parts.setNameLangId(p.getNameLangId());
                    } else {
                        mCreate.put(f[0] + "", e.getName());
                        parts.setName(f[0] + "");
                        f[0]++;
                    }
                    if (p.getRemarkLangId() != null) {
                        mUpdate.put(p.getRemarkLangId(), e.getRemark());
                        parts.setRemarkLangId(p.getRemarkLangId());
                    } else {
                        mCreate.put(f[0] + "", e.getRemark());
                        parts.setRemark(f[0] + "");
                        f[0]++;
                    }
                    if (p.getShortDescriptionLangId() != null) {
                        mUpdate.put(p.getShortDescriptionLangId(), e.getShortDescription());
                        parts.setShortDescriptionLangId(p.getShortDescriptionLangId());
                    } else {
                        mCreate.put(f[0] + "", e.getShortDescription());
                        parts.setShortDescription(f[0] + "");
                        f[0]++;
                    }
                    if (p.getLongDescriptionLangId() != null) {
                        mUpdate.put(p.getLongDescriptionLangId(), e.getLongDescription());
                        parts.setLongDescriptionLangId(p.getLongDescriptionLangId());
                    } else {
                        mCreate.put(f[0] + "", e.getLongDescription());
                        parts.setLongDescription(f[0] + "");
                        f[0]++;
                    }
                    if (p.getTechnicalSpecificationLangId() != null) {
                        mUpdate.put(p.getTechnicalSpecificationLangId(), e.getTechnicalSpecification());
                        parts.setTechnicalSpecificationLangId(p.getTechnicalSpecificationLangId());
                    } else {
                        mCreate.put(f[0] + "", e.getTechnicalSpecification());
                        parts.setTechnicalSpecification(f[0] + "");
                        f[0]++;
                    }
                } else {
                    mCreate.put(f[0] + "", e.getName());
                    parts.setName(f[0] + "");
                    f[0]++;
                    mCreate.put(f[0] + "", e.getRemark());
                    parts.setRemark(f[0] + "");
                    f[0]++;
                    mCreate.put(f[0] + "", e.getShortDescription());
                    parts.setShortDescription(f[0] + "");
                    f[0]++;
                    mCreate.put(f[0] + "", e.getLongDescription());
                    parts.setLongDescription(f[0] + "");
                    f[0]++;
                    mCreate.put(f[0] + "", e.getTechnicalSpecification());
                    parts.setTechnicalSpecification(f[0] + "");
                    f[0]++;
                }
            } else {
                mCreate.put(f[0] + "", e.getName());
                parts.setName(f[0] + "");
                f[0]++;
                mCreate.put(f[0] + "", e.getRemark());
                parts.setRemark(f[0] + "");
                f[0]++;
                mCreate.put(f[0] + "", e.getShortDescription());
                parts.setShortDescription(f[0] + "");
                f[0]++;
                mCreate.put(f[0] + "", e.getLongDescription());
                parts.setLongDescription(f[0] + "");
                f[0]++;
                mCreate.put(f[0] + "", e.getTechnicalSpecification());
                parts.setTechnicalSpecification(f[0] + "");
                f[0]++;
            }
            return parts;
        }).collect(Collectors.toList());
        if (mUpdate.size() > 0) {
            remoteMultiLanguageService.simpleUpdateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), mUpdate, LocaleContextHolder.getLocale().getLanguage());
        }
        if (mCreate.size() > 0) {
            Map<String, MultiLanguageBo> boMap = remoteMultiLanguageService.simpleCreateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), mCreate, LocaleContextHolder.getLocale().getLanguage());
            newParts.forEach(e -> {
                if (e.getNameLangId() == null) {
                    MultiLanguageBo bo = boMap.get(e.getName());
                    e.setNameLangId(bo.getLangId());
                    e.setNameLangCode(bo.getLangCode());
                    e.setName(mCreate.get(e.getName()));
                }
                if (e.getRemarkLangId() == null) {
                    MultiLanguageBo bo = boMap.get(e.getRemark());
                    e.setRemarkLangId(bo.getLangId());
                    e.setRemarkLangCode(bo.getLangCode());
                    e.setRemark(mCreate.get(e.getRemark()));
                }
                if (e.getShortDescriptionLangId() == null) {
                    MultiLanguageBo bo = boMap.get(e.getShortDescription());
                    e.setShortDescriptionLangId(bo.getLangId());
                    e.setShortDescriptionLangCode(bo.getLangCode());
                    e.setShortDescription(mCreate.get(e.getShortDescription()));
                }
                if (e.getLongDescriptionLangId() == null) {
                    MultiLanguageBo bo = boMap.get(e.getLongDescription());
                    e.setLongDescriptionLangId(bo.getLangId());
                    e.setLongDescriptionLangCode(bo.getLangCode());
                    e.setLongDescription(mCreate.get(e.getLongDescription()));
                }
                if (e.getTechnicalSpecificationLangId() == null) {
                    MultiLanguageBo bo = boMap.get(e.getTechnicalSpecification());
                    e.setTechnicalSpecificationLangId(bo.getLangId());
                    e.setTechnicalSpecificationLangCode(bo.getLangCode());
                    e.setTechnicalSpecification(mCreate.get(e.getTechnicalSpecification()));
                }
            });
        }
        this.saveOrUpdateBatch(newParts);
        // 处理link
        partsLinkService.importLink(newParts.stream()
                .filter(e -> e.getId() != null && StringUtils.isNotBlank(e.getLink()))
                .collect(Collectors.toMap(BaseDo::getId, Parts::getLink)));
        return res;
    }

    private boolean validImport(List<PartsRead> data, List<String> res) {
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList(PARTS_TYPE));
        Map<String, Map<String, String>> dictMap = new HashMap<>();
        dictList.forEach(e -> dictMap.put(e.getDictName(), e.getNodes().stream().collect(Collectors.toMap(DictNodeBo::getDescription, DictNodeBo::getLabel))));
        Map<String, String> partsType = dictMap.getOrDefault(PARTS_TYPE, new HashMap<>());
        List<Integer> idString = new ArrayList<>();
        Map<String, List<Integer>> idMap = new HashMap<>();
        Map<String, List<Integer>> modelMap = new HashMap<>();
        List<Integer> nameNullFlag = new ArrayList<>();
        List<Integer> modelNullFlag = new ArrayList<>();
        List<Integer> iconUrlNullFlag = new ArrayList<>();
        List<Integer> iconUrlFlag = new ArrayList<>();
        List<Integer> type1NullFlag = new ArrayList<>();
        List<Integer> type1Flag = new ArrayList<>();
        List<Integer> type2Flag = new ArrayList<>();
        List<Integer> modelSizeFlag = new ArrayList<>();
        List<Integer> nameSizeFlag = new ArrayList<>();
        List<Integer> remarkSizeFlag = new ArrayList<>();
        List<Integer> iconUrlSizeFlag = new ArrayList<>();

        List<Integer> shortSizeFlag = new ArrayList<>();
        List<Integer> longSizeFlag = new ArrayList<>();
        List<Integer> techSizeFlag = new ArrayList<>();
        List<Integer> linkSizeFlag = new ArrayList<>();

        for (int i = 2, j = data.size() + 2; i < j; i++) {
            PartsRead read = data.get(i - 2);

            if (StringUtils.isNotBlank(read.getPartsId())) {
                try {
                    Long.parseLong(read.getPartsId());
                } catch (Exception e) {
                    idString.add(i);
                }
                if (idMap.get(read.getPartsId()) == null) {
                    List<Integer> ids = new ArrayList<>();
                    ids.add(i);
                    idMap.put(read.getPartsId(), ids);
                } else {
                    idMap.get(read.getPartsId()).add(i);
                }
            }

            if (StringUtils.isBlank(read.getCommodityModel())) {
                modelNullFlag.add(i);
            } else if (read.getCommodityModel().length() > TEXT_SIZE) {
                modelSizeFlag.add(i);
            } else {
                if (modelMap.get(read.getCommodityModel()) == null) {
                    List<Integer> models = new ArrayList<>();
                    models.add(i);
                    modelMap.put(read.getCommodityModel(), models);
                } else {
                    modelMap.get(read.getCommodityModel()).add(i);
                }
            }

            if (StringUtils.isBlank(read.getName())) {
                nameNullFlag.add(i);
            } else if (read.getName().length() > TEXT_SIZE) {
                nameSizeFlag.add(i);
            }

            if (StringUtils.isBlank(read.getIconUrl())) {
                iconUrlNullFlag.add(i);
            } else if (read.getIconUrl().length() > LINK_SIZE) {
                iconUrlSizeFlag.add(i);
            } else if (!(read.getIconUrl().startsWith("http://") || read.getIconUrl().startsWith("https://"))) {
                iconUrlFlag.add(i);
            }

            if (StringUtils.isBlank(read.getType1())) {
                type1NullFlag.add(i);
            } else if (!partsType.containsKey(read.getType1())) {
                type1Flag.add(i);
            } else {
                read.setType1(partsType.get(read.getType1()));
            }

            if (!StringUtils.isBlank(read.getType2())) {
                if (!partsType.containsKey(read.getType2())) {
                    type2Flag.add(i);
                } else {
                    read.setType2(partsType.get(read.getType2()));
                }
            }

            if (StringUtils.isNotBlank(read.getRemark()) && read.getRemark().length() > TEXT_AREA_SIZE) {
                remarkSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getShortDescription()) && read.getShortDescription().length() > SHORT_DESCRIPTION_SIZE) {
                shortSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getLongDescription()) && read.getLongDescription().length() > LONG_DESCRIPTION_SIZE) {
                longSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getTechnicalSpecification()) && read.getTechnicalSpecification().length() > TECHNICAL_SPECIFICATION_SIZE) {
                techSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getLink()) && read.getLink().length() > LINK_SIZE) {
                linkSizeFlag.add(i);
            }

        }
        // 检测id是否为数字
        if (idString.size() > 0) {
            res.add("第" + idString.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【配件ID】非法");
        }
        // 检测id是否唯一
        for (Map.Entry<String, List<Integer>> entry : idMap.entrySet()) {
            List<Integer> value = entry.getValue();
            if (value.size() != 1) {
                res.add("第" + value.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【配件ID】重复");
            }
        }
        // 检测商品型号Model #是否必填
        if (modelNullFlag.size() > 0) {
            res.add("第" + modelNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【商品型号Model #】为空");
        }
        // 检测商品型号Model #长度
        if (modelSizeFlag.size() > 0) {
            res.add("第" + modelSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【商品型号Model #】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测商品型号Model #是否唯一
        for (Map.Entry<String, List<Integer>> entry : modelMap.entrySet()) {
            List<Integer> v = entry.getValue();
            if (v.size() != 1) {
                res.add("第" + v.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【商品型号Model #】表内重复");
            }
        }
        // 检测配件名称是否必填
        if (nameNullFlag.size() > 0) {
            res.add("第" + nameNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【配件名称】为空");
        }
        // 检测配件名称长度
        if (nameSizeFlag.size() > 0) {
            res.add("第" + nameSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【配件名称】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测配件图片链接是否必填
        if (iconUrlNullFlag.size() > 0) {
            res.add("第" + iconUrlNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【配件图片链接】为空");
        }
        // 检测配件图片链接长度
        if (iconUrlSizeFlag.size() > 0) {
            res.add("第" + iconUrlSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【配件图片链接】内容超过" + LINK_SIZE + "个字符");
        }
        // 检测配件图片链接不是http://或者https://开头
        if (iconUrlFlag.size() > 0) {
            res.add("第" + iconUrlFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【配件图片链接】不是以https://或者http://开头");
        }
        // 检测配件类型1是否必填
        if (type1NullFlag.size() > 0) {
            res.add("第" + type1NullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【配件类型1】为空");
        }
        // 检测配件类型1是否正确
        if (type1Flag.size() > 0) {
            res.add("第" + type1Flag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【配件类型1】不正确");
        }
        // 检测配件类型2是否正确
        if (type2Flag.size() > 0) {
            res.add("第" + type2Flag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【配件类型2】不正确");
        }
        // 检测配件备注长度
        if (remarkSizeFlag.size() > 0) {
            res.add("第" + remarkSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【备注】内容超过" + TEXT_AREA_SIZE + "个字符");
        }
        // 检测短描述长度
        if (shortSizeFlag.size() > 0) {
            res.add("第" + shortSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【产品短描述】内容超过" + SHORT_DESCRIPTION_SIZE + "个字符");
        }
        // 检测长描述长度
        if (longSizeFlag.size() > 0) {
            res.add("第" + longSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【产品长描述】内容超过" + LONG_DESCRIPTION_SIZE + "个字符");
        }
        // 检测技术规格长度
        if (techSizeFlag.size() > 0) {
            res.add("第" + techSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【技术规格】内容超过" + TECHNICAL_SPECIFICATION_SIZE + "个字符");
        }
        // 检测购买链接长度
        if (linkSizeFlag.size() > 0) {
            res.add("第" + linkSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【购买链接】内容超过" + LINK_SIZE + "个字符");
        }

        if (!res.isEmpty()) {
            return false;
        }

        List<Long> ids = data.stream().filter(e -> StringUtils.isNotBlank(e.getPartsId())).map(e -> Long.parseLong(e.getPartsId())).distinct().collect(Collectors.toList());
        if (!ids.isEmpty()) {
            List<Parts> partsList = this.listByIds(ids);
            List<Long> existIds = partsList.stream().map(BaseDo::getId).collect(Collectors.toList());
            for (int i = 2, j = data.size() + 2; i < j; i++) {
                PartsRead read = data.get(i - 2);
                if (StringUtils.isNotBlank(read.getPartsId()) && !existIds.contains(Long.parseLong(read.getPartsId()))) {
                    res.add("第" + i + "行，【配件ID】不存在");
                }
            }
        }

        List<String> excelModels = data.stream().map(PartsRead::getCommodityModel).collect(Collectors.toList());
        List<Parts> partsList = this.list(new LambdaQueryWrapper<Parts>().in(Parts::getCommodityModel, excelModels));
        List<String> collect = partsList.stream().map(e -> e.getId() + e.getCommodityModel()).collect(Collectors.toList());
        List<String> collect1 = partsList.stream().map(Parts::getCommodityModel).collect(Collectors.toList());
        for (int i = 2, j = data.size() + 2; i < j; i++) {
            PartsRead read = data.get(i - 2);
            boolean exist = (StringUtils.isBlank(read.getPartsId()) || (!StringUtils.isBlank(read.getPartsId()) && !collect.contains(read.getPartsId() + read.getCommodityModel()))) && collect1.contains(read.getCommodityModel());
            if (exist) {
                res.add("第" + i + "行，【商品型号Model #】已存在");
            }
        }
        return res.isEmpty();
    }


    @Override
    public List<PartsExcel> listData(PartsPageDto req) {
        List<Long> nameLangIds = findNameLangIds(req.getName());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getName()) && CollectionUtils.isEmpty(nameLangIds)) {
            return new ArrayList<>();
        }
        List<Parts> list = this.getBaseMapper().selectPartsList(req, nameLangIds);

        Map<String, DictBo> dictMap = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList(PARTS_TYPE))
                .stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        return list.stream().map(e -> {
            PartsExcel vo = new PartsExcel();
            BeanUtils.copyProperties(e, vo);
            vo.setPartsId(e.getId());
            vo.setName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(e.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setRemark(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(e.getRemarkLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setCreateTime(DateTimeZoneUtil.format(e.getCreateTime(), req.getZone()));
            vo.setUpdateTime(DateTimeZoneUtil.format(e.getUpdateTime(), req.getZone()));
            vo.setType1(dictMap.get(PARTS_TYPE).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getType1()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            vo.setType2(dictMap.get(PARTS_TYPE).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getType2()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(PartsEditDto req) {
        if (req.getPartsId() == null) {
            throw ExceptionMessageUtil.getException(OPERATION_PARTS_EDIT_ID_NULL);
        }
        if (StringUtils.isBlank(req.getItem())) {
            throw ExceptionMessageUtil.getException(OPERATION_PARTS_EDIT_ITEM_BLANK);
        }
        Parts p = this.getById(req.getPartsId());
        if (p == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_NULL, req.getPartsId());
        }
        Parts one = new Parts();
        one.setId(p.getId());
        switch (req.getKey()) {
            case SHORT_DESCRIPTION:
                one.setShortDescription(req.getItem());
                if (p.getShortDescriptionLangId() == null) {
                    MultiLanguageBo bo = remoteMultiLanguageService.simpleCreateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), req.getItem(), LocaleContextHolder.getLocale().getLanguage());
                    one.setShortDescriptionLangId(bo.getLangId());
                    one.setShortDescriptionLangCode(bo.getLangCode());
                } else {
                    remoteMultiLanguageService.simpleUpdateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), p.getShortDescriptionLangId(), req.getItem(), LocaleContextHolder.getLocale().getLanguage());
                }
                break;
            case LONG_DESCRIPTION:
                one.setLongDescription(req.getItem());
                if (p.getLongDescriptionLangId() == null) {
                    MultiLanguageBo bo = remoteMultiLanguageService.simpleCreateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), req.getItem(), LocaleContextHolder.getLocale().getLanguage());
                    one.setLongDescriptionLangId(bo.getLangId());
                    one.setLongDescriptionLangCode(bo.getLangCode());
                } else {
                    remoteMultiLanguageService.simpleUpdateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), p.getLongDescriptionLangId(), req.getItem(), LocaleContextHolder.getLocale().getLanguage());
                }
                break;
            case TECHNICAL_SPECIFICATION:
                one.setTechnicalSpecification(req.getItem());
                if (p.getTechnicalSpecificationLangId() == null) {
                    MultiLanguageBo bo = remoteMultiLanguageService.simpleCreateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), req.getItem(), LocaleContextHolder.getLocale().getLanguage());
                    one.setTechnicalSpecificationLangId(bo.getLangId());
                    one.setTechnicalSpecificationLangCode(bo.getLangCode());
                } else {
                    remoteMultiLanguageService.simpleUpdateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), p.getTechnicalSpecificationLangId(), req.getItem(), LocaleContextHolder.getLocale().getLanguage());
                }
                break;
            default:
                break;
        }
        this.updateById(one);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void manualEdit(PartsManualDto req) {
        partsManualService.edit(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void manualDelete(Long partsManualId) {
        partsManualService.delete(partsManualId);
    }

    @Override
    public String manualDownload(Long partsManualId) {
        return partsManualService.getUrl(partsManualId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void manualAdd(PartsManualDto req) {
        partsManualService.add(req);
    }

    @Override
    public PartsManualVo manualDetail(Long partsManualId) {
        return partsManualService.detail(partsManualId);
    }

    @Override
    public List<PartsManualVo> manualList(Long partsId) {
        return partsManualService.listByPartsId(partsId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operationGuidanceEdit(PartsOperationGuidanceDto req) {
        partsOperationGuidanceService.edit(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operationGuidanceDelete(Long partsOperationGuidanceId) {
        partsOperationGuidanceService.delete(partsOperationGuidanceId);
    }

    @Override
    public String operationGuidanceDownload(Long partsOperationGuidanceId) {
        return partsOperationGuidanceService.getUrl(partsOperationGuidanceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operationGuidanceAdd(PartsOperationGuidanceDto req) {
        partsOperationGuidanceService.add(req);
    }

    @Override
    public List<PartsOperationGuidanceVo> operationGuidanceList(Long partsId) {
        return partsOperationGuidanceService.listByPartsId(partsId);
    }

    @Override
    public PartsOperationGuidanceVo operationGuidanceDetail(Long partsOperationGuidanceId) {
        return partsOperationGuidanceService.detail(partsOperationGuidanceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operationGuidanceOrder(List<Long> partsOperationGuidanceIds) {
        partsOperationGuidanceService.order(partsOperationGuidanceIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void faqEdit(PartsFaqDto req) {
        partsFaqService.edit(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void faqDelete(Long partsFaqId) {
        partsFaqService.delete(partsFaqId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void faqAdd(PartsFaqDto req) {
        partsFaqService.add(req);
    }

    @Override
    public List<PartsFaqVo> faqList(PartsFaqListDto req) {
        return partsFaqService.search(req);
    }

    @Override
    public PartsFaqVo faqDetail(Long partsFaqId) {
        return partsFaqService.detail(partsFaqId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void faqOrder(List<Long> partsFaqIds) {
        partsFaqService.order(partsFaqIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void linkEdit(PartsLinkDto req) {
        partsLinkService.edit(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void linkDelete(Long partsLinkId) {
        partsLinkService.delete(partsLinkId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void linkAdd(PartsLinkDto req) {
        partsLinkService.add(req);
    }

    @Override
    public List<PartsLinkVo> linkList(Long partsId) {
        return partsLinkService.listByPartsId(partsId);
    }

    @Override
    public PartsLinkVo linkDetail(Long partsLinkId) {
        return partsLinkService.detail(partsLinkId);
    }

}
