package com.chervon.operation.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.enums.AppShowEnum;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.api.vo.CategoryVo;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.config.MultiLanguageUtil;
import com.chervon.operation.config.OperationCommon;
import com.chervon.operation.domain.dataobject.Category;
import com.chervon.operation.domain.dto.category.CategoryAddDto;
import com.chervon.operation.domain.dto.category.CategoryEditDto;
import com.chervon.operation.domain.dto.category.SearchCategoryDto;
import com.chervon.operation.mapper.CategoryMapper;
import com.chervon.operation.service.CategoryService;
import com.chervon.technology.api.RemoteTechProductOperationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 20220425
 * 品牌写服务
 */
@Service
@Slf4j
@EnableConfigurationProperties(AwsProperties.class)
@AllArgsConstructor
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {

    private final CategoryMapper categoryMapper;

    private final AwsProperties awsProperties;

    @DubboReference
    private RemoteMultiLanguageService languageService;


    @DubboReference
    private RemoteTechProductOperationService remoteProductService;
    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    private CategoryVo changeVo(Category category) {
        if (null == category) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CATEGORY_NOT_EXIST);
        }
        CategoryVo categoryVo = ConvertUtil.convert(category, CategoryVo.class);
        categoryVo.setCategoryLangId(category.getCategoryName());
        // 获取多语言code
        MultiLanguageBo bo = languageService.getById(category.getCategoryName());
        MultiLanguageVo languageVo = new MultiLanguageVo(bo.getLangId(),
                com.chervon.operation.config.MultiLanguageUtil.getByLangCode(bo.getLangCode(), LocaleContextHolder.getLocale().getLanguage()));
        categoryVo.setCategoryName(languageVo);
        return categoryVo;
    }

    @Override
    public CategoryVo getDetail(Long id) {
        Category category = this.getById(id);
        return changeVo(category);
    }

    @Override
    public void checkExistByCategoryId(Long categoryId) {
        long count = countById(categoryId);
        if (count == 0) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CATEGORY_NOT_EXIST);
        }
    }

    private long countById(Long categoryId) {
        return count(Wrappers.<Category>lambdaQuery().eq(Category::getId, categoryId));
    }

    private String getFileUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        return UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), url);
    }

    @Override
    public PageResult<CategoryVo> list(SearchCategoryDto searchCategoryDto) {
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<Category>()
                // 根据ID模糊搜索
                .like(null != searchCategoryDto.getId(), Category::getId, searchCategoryDto.getId())
                // 根据创建,更新时间区间搜索
                .ge(StringUtils.isNotBlank(searchCategoryDto.getCreateStartTime()),
                        Category::getCreateTime, searchCategoryDto.getCreateStartTime())
                .le(StringUtils.isNotBlank(searchCategoryDto.getCreateEndTime()),
                        Category::getCreateTime, searchCategoryDto.getCreateEndTime())
                .ge(StringUtils.isNotBlank(searchCategoryDto.getUpdateStartTime()),
                        Category::getUpdateTime, searchCategoryDto.getUpdateStartTime())
                .le(StringUtils.isNotBlank(searchCategoryDto.getUpdateEndTime()),
                        Category::getUpdateTime, searchCategoryDto.getUpdateEndTime())
                // 根据多语言ID搜索
                .like(StringUtils.isNotBlank(searchCategoryDto.getLangId()),
                        Category::getCategoryName, searchCategoryDto.getLangId());
        if (StringUtils.isNotEmpty(searchCategoryDto.getName())) {
            //多语言模糊搜索
            LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Category::getIsDeleted, 0);
            List<Category> categoryList = this.list(wrapper);
            List<Long> ids = new ArrayList<>();
            List<String> langIdList = categoryList.stream().map(Category::getCategoryName).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(langIdList)) {
                for (String str : langIdList) {
                    ids.add(Long.valueOf(str));
                }
            }
            Map<String, List<Long>> query = new HashMap<>();
            query.put(searchCategoryDto.getName(), ids);
            Map<String, List<MultiLanguageBo>> listByText = remoteMultiLanguageService.listByTextLike(query, LocaleContextHolder.getLocale().getLanguage());
            List<MultiLanguageBo> langIdlist = listByText.get(searchCategoryDto.getName());
            List<Long> langIds = langIdlist.stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(langIds)) {
                queryWrapper.in(Category::getCategoryName, langIds);
            } else {
                queryWrapper.eq(Category::getId, null);
            }
        }
        queryWrapper.orderByDesc(Category::getCreateTime);
        Page<Category> page = categoryMapper.selectPage(new Page<>(searchCategoryDto.getPageNum(),
                searchCategoryDto.getPageSize()), queryWrapper);
        PageResult<CategoryVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<String> langIds = page.getRecords().stream().map(Category::getCategoryName).collect(Collectors.toList());
        if (langIds != null && langIds.size() > 0) {
            List<MultiLanguageBo> languageBoList;
            // 转化为Map
            Map<Long, String> langMaps = new HashMap<>();
            if (!CollectionUtils.isEmpty(langIds)) {
                languageBoList = languageService.listByIds(langIds);
                languageBoList.forEach(code -> langMaps.put(code.getLangId(), code.getLangCode()));
            }
            log.info("list:LocaleContextHolder.getLocale().getLanguage() :{}", LocaleContextHolder.getLocale().getLanguage());
            page.getRecords().forEach(e -> {
                e.setCategoryIcon(getFileUrl(e.getCategoryIcon()));
                CategoryVo vo = ConvertUtil.convert(e, CategoryVo.class);
                if (!CollectionUtils.isEmpty(langMaps)) {
                    String langCode = langMaps.get(Long.valueOf(e.getCategoryName()));
                    MultiLanguageVo languageVo = new MultiLanguageVo(Long.valueOf(e.getCategoryName()),
                            com.chervon.operation.config.MultiLanguageUtil.getByLangCode(langCode, LocaleContextHolder.getLocale().getLanguage()));
                    vo.setCategoryName(languageVo);
                }
                res.getList().add(vo);
            });
        }
        return res;
    }

    @Override
    public List<CategoryVo> allList() {
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(Category::getCreateTime);
        List<Category> doList = categoryMapper.selectList(queryWrapper);
        List<String> langIds = doList.stream().map(Category::getCategoryName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(langIds)) {
            return null;
        }
        List<MultiLanguageBo> languageBoList = languageService.listByIds(langIds);
        // 转化为Map
        Map<Long, String> langMaps = new HashMap<>(languageBoList.size());
        languageBoList.forEach(code -> langMaps.put(code.getLangId(), code.getLangCode()));
        log.info("allList:LocaleContextHolder.getLocale().getLanguage() :{}", LocaleContextHolder.getLocale().getLanguage());
        if (!CollectionUtils.isEmpty(doList)) {
            List<CategoryVo> categoryList = new ArrayList<>();
            doList.forEach(categoryDo -> {
                categoryDo.setCategoryIcon(getFileUrl(categoryDo.getCategoryIcon()));
                CategoryVo vo = ConvertUtil.convert(categoryDo, CategoryVo.class);
                String langCode = langMaps.get(Long.valueOf(categoryDo.getCategoryName()));
                MultiLanguageVo languageVo = new MultiLanguageVo(Long.valueOf(categoryDo.getCategoryName()),
                        com.chervon.operation.config.MultiLanguageUtil.getByLangCode(langCode, LocaleContextHolder.getLocale().getLanguage()));
                vo.setCategoryName(languageVo);
                categoryList.add(vo);
            });
            return categoryList;
        }
        return null;
    }

    /**
     * 获取所有品类名称的所有多语言版本
     *
     * @return
     */
    @Override
    public Set<String> listCategoryNames() {
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Category::getCategoryName).orderByDesc(Category::getCreateTime);
        List<Category> categoryList = categoryMapper.selectList(queryWrapper);
        if(CollectionUtil.isEmpty(categoryList)){
            return new HashSet<>();
        }
        List<String> langIds = categoryList.stream().map(Category::getCategoryName).collect(Collectors.toList());
        Map<String, Map<String, String>> stringMapMap = languageService.listLanguageContentForAllLanguagesByLangIds(langIds);
        Set<String> result = stringMapMap.values()
                .stream()
                .flatMap(map -> map.values().stream().map(String::valueOf))
                .collect(Collectors.toSet());
        return result;
    }

    @Override
    public List<Long> getAllCategoryIds() {
        return null;
    }

    @Override
    public void addCategory(CategoryAddDto categoryAdd) {
        // 查找是否已经存在相同名称的品类,如何校验品类已经存在
        Set<String> categoryNames = listCategoryNames();
        if (categoryNames.contains(categoryAdd.getCategoryName())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CATEGORY_ALREADY_EXIST, categoryAdd.getCategoryName());
        }
        // 生成多语言Id
        MultiLanguageBo languageBo = languageService.simpleCreateMultiLanguage(OperationCommon.APPLICATION_NAME,
                categoryAdd.getCategoryName(), LocaleContextHolder.getLocale().getLanguage());
        categoryAdd.setCategoryName(String.valueOf(languageBo.getLangId()));
        // 添加到数据库中
        Category newCategory = ConvertUtil.convert(categoryAdd, Category.class);
        newCategory.setAppShowStatus(AppShowEnum.SHOW.getValue());
        newCategory.setAppShowOrder(categoryMapper.getAppShowMaxOrder()+1);
        save(newCategory);
    }


    @Override
    public void editCategory(CategoryEditDto categoryEdit) {
        // 查看数据是否存在
        Category category = this.getById(categoryEdit.getId());
        if (null == category) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CATEGORY_NOT_EXIST, categoryEdit.getId());
        }
        // 查找是否已经存在相同名称的品类
        if (null != categoryEdit.getCategoryName() && StringUtils.isNotEmpty(categoryEdit.getCategoryName().getMessage())) {
            List<CategoryVo> lists = this.allList();
            if (!CollectionUtils.isEmpty(lists)) {
                lists.forEach(categoryVo -> {
                    if (!categoryVo.getCategoryName().getLangId().equals(categoryEdit.getCategoryName().getLangId()) &&
                            categoryEdit.getCategoryName().getMessage().equals(categoryVo.getCategoryName().getMessage())) {
                        throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CATEGORY_ALREADY_EXIST, categoryEdit.getCategoryName());
                    }
                });
            }
        }
        // 更新多语言
        languageService.simpleUpdateMultiLanguage(OperationCommon.APPLICATION_NAME,
                categoryEdit.getCategoryName().getLangId(), categoryEdit.getCategoryName().getMessage(),
                LocaleContextHolder.getLocale().getLanguage());
        Category categoryDo = ConvertUtil.convert(categoryEdit, Category.class);
        categoryDo.setCategoryName(String.valueOf(categoryEdit.getCategoryName().getLangId()));
        categoryDo.setId(categoryEdit.getId());
        this.updateById(categoryDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCategory(Long id) {
        // 查看数据是否存在
        Category category = this.getById(id);
        if (null == category) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CATEGORY_NOT_EXIST, id);
        } else if (StringUtils.isNotEmpty(category.getCategoryName())) {
            // 清理多语言
            remoteMultiLanguageService.deleteByLangIds(Collections.singletonList(Long.valueOf(category.getCategoryName())));
        }
        // 查看是否被产品关联
        Boolean used = remoteProductService.ifCategoryUsed(id);
        if (used) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CATEGORY_ALREADY_BE_USED, id);
        }
        this.removeById(id);
    }

    private Category findByName(String categoryName) {
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Category::getCategoryName, categoryName);
        return categoryMapper.selectOne(queryWrapper);
    }

    @Override
    public Map<Long, CategoryVo> getMyMap(Map<Long, Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return null;
        }
        List<Long> ids = new ArrayList<>(categoryIds.size());
        for (Long productId : categoryIds.keySet()) {
            ids.add(categoryIds.get(productId));
        }
        LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Category::getId, ids);
        List<Category> categories = this.list(wrapper);
        if (CollectionUtils.isEmpty(categories)) {
            return null;
        }
        List<String> langIds = categories.stream().map(Category::getCategoryName).collect(Collectors.toList());
        List<MultiLanguageBo> languageBoList = languageService.listByIds(langIds);
        // 转化为Map
        Map<Long, String> langMaps = new HashMap<>(languageBoList.size());
        languageBoList.forEach(code -> langMaps.put(code.getLangId(), code.getLangCode()));

        Map<Long, CategoryVo> resultBrandVo = new HashMap<>(categoryIds.size());
        Map<Long, CategoryVo> tempCategory = new HashMap<>(categories.size());
        categories.forEach(category -> {
            CategoryVo categoryVo = ConvertUtil.convert(category, CategoryVo.class);
            String langCode = langMaps.get(Long.valueOf(category.getCategoryName()));
            MultiLanguageVo languageVo = new MultiLanguageVo(Long.valueOf(category.getCategoryName()),
                    com.chervon.operation.config.MultiLanguageUtil.getByLangCode(langCode, LocaleContextHolder.getLocale().getLanguage()));
            categoryVo.setCategoryName(languageVo);
            tempCategory.put(category.getId(), categoryVo);
        });

        for (Long productId : categoryIds.keySet()) {
            Long categoryId = categoryIds.get(productId);
            resultBrandVo.put(productId, tempCategory.get(categoryId));
        }
        return resultBrandVo;
    }

    @Override
    public List<CategoryVo> getByIds(List<Long> categoryIds) {
        if (CollectionUtil.isEmpty(categoryIds)) {
            return null;
        }
        LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Category::getId, categoryIds);
        List<Category> categories = this.list(wrapper);
        if (CollectionUtils.isEmpty(categories)) {
            return null;
        }
        List<String> langIds = categories.stream().map(Category::getCategoryName).collect(Collectors.toList());
        List<MultiLanguageBo> languageBoList = languageService.listByIds(langIds);
        // 转化为Map
        Map<Long, String> langMaps = new HashMap<>(languageBoList.size());
        languageBoList.forEach(code -> langMaps.put(code.getLangId(), code.getLangCode()));
        List<CategoryVo> categoryList = new ArrayList<>();
        categories.forEach(categoryDo -> {
            categoryDo.setCategoryIcon(getFileUrl(categoryDo.getCategoryIcon()));
            CategoryVo vo = ConvertUtil.convert(categoryDo, CategoryVo.class);
            String langCode = langMaps.get(Long.valueOf(categoryDo.getCategoryName()));
            MultiLanguageVo languageVo = new MultiLanguageVo(Long.valueOf(categoryDo.getCategoryName()),
                    com.chervon.operation.config.MultiLanguageUtil.getByLangCode(langCode, LocaleContextHolder.getLocale().getLanguage()));
            vo.setCategoryName(languageVo);
            categoryList.add(vo);
        });
        return categoryList;
    }

    @Override
    public Map<Long, CategoryVo> getMapByIds(List<Long> categoryIds) {
        Map<Long, CategoryVo> map = new HashMap<>(categoryIds.size());
        if (CollectionUtil.isEmpty(categoryIds)) {
            return map;
        }
        LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Category::getId, categoryIds);
        List<Category> categories = this.list(wrapper);
        if (CollectionUtils.isEmpty(categories)) {
            return map;
        }
        List<String> langIds = categories.stream().map(Category::getCategoryName).collect(Collectors.toList());
        List<MultiLanguageBo> languageBoList = languageService.listByIds(langIds);
        // 转化为Map
        Map<Long, String> langMaps = new HashMap<>(languageBoList.size());
        languageBoList.forEach(code -> langMaps.put(code.getLangId(), code.getLangCode()));
        categories.forEach(categoryDo -> {
            categoryDo.setCategoryIcon(getFileUrl(categoryDo.getCategoryIcon()));
            CategoryVo vo = ConvertUtil.convert(categoryDo, CategoryVo.class);
            String langCode = langMaps.get(Long.valueOf(categoryDo.getCategoryName()));
            MultiLanguageVo languageVo = new MultiLanguageVo(Long.valueOf(categoryDo.getCategoryName()),
                    MultiLanguageUtil.getByLangCode(langCode, LocaleContextHolder.getLocale().getLanguage()));
            vo.setCategoryName(languageVo);
            map.put(categoryDo.getId(), vo);
        });
        return map;
    }

    @Override
    public List<CategoryVo> listOrderByAppShowOrder() {
        List<Category> categories = list(Wrappers.<Category>lambdaQuery()
                .orderByAsc(Category::getAppShowOrder));
        return assembleCategoryVoList(categories);
    }

    @Override
    public List<Long> listCategoryIdsByOrderByAppShowOrder(int appShowStatus) {
        return list(Wrappers.<Category>lambdaQuery()
                .eq(Category::getAppShowStatus,appShowStatus)
                .orderByAsc(Category::getAppShowOrder))
                .stream()
                .map(Category::getId)
                .collect(Collectors.toList());
    }

    //获取品类名称多语言，icon转换
    private List<CategoryVo> assembleCategoryVoList(List<Category> categories) {
        List<String> langIds = categories.stream().map(Category::getCategoryName).collect(Collectors.toList());
        List<MultiLanguageBo> languageBoList = languageService.listByIds(langIds);
        // 转化为Map
        Map<Long, String> langMaps = new HashMap<>(languageBoList.size());
        languageBoList.forEach(code -> langMaps.put(code.getLangId(), code.getLangCode()));
        List<CategoryVo> categoryVoList = new ArrayList<>();
        categories.forEach(categoryDo -> {
            categoryDo.setCategoryIcon(getFileUrl(categoryDo.getCategoryIcon()));
            CategoryVo vo = ConvertUtil.convert(categoryDo, CategoryVo.class);
            String langCode = langMaps.get(Long.valueOf(categoryDo.getCategoryName()));
            MultiLanguageVo languageVo = new MultiLanguageVo(Long.valueOf(categoryDo.getCategoryName()),
                    MultiLanguageUtil.getByLangCode(langCode, LocaleContextHolder.getLocale().getLanguage()));
            vo.setCategoryName(languageVo);
            categoryVoList.add(vo);
        });
        return categoryVoList;
    }

    @Override
    public void updateBatchCatoryById(List<Category> categories) {
        updateBatchById(categories);
    }
}
