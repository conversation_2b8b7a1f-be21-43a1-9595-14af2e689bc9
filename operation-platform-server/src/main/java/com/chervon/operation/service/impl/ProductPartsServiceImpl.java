package com.chervon.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.iot.app.api.RemoteAppDeviceService;
import com.chervon.iot.app.api.RemoteAppSnowBlowerService;
import com.chervon.iot.app.api.vo.RemoteDevicePartsService;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.config.MultiLanguageUtil;
import com.chervon.operation.domain.dataobject.Parts;
import com.chervon.operation.domain.dataobject.ProductParts;
import com.chervon.operation.domain.dto.parts.product.ProductPartsAddDto;
import com.chervon.operation.domain.dto.parts.product.ProductPartsEditDto;
import com.chervon.operation.domain.dto.parts.product.ProductPartsPageDto;
import com.chervon.operation.domain.vo.parts.PartsVo;
import com.chervon.operation.domain.vo.parts.product.ProductPartsVo;
import com.chervon.operation.mapper.ProductPartsMapper;
import com.chervon.operation.service.PartsService;
import com.chervon.operation.service.ProductPartsService;
import com.chervon.technology.api.RemoteTechProductOperationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chervon.operation.api.exception.OperationErrorCode.*;

/**
 * <AUTHOR>
 * @date 2022-08-10
 */
@Service
@Slf4j
public class ProductPartsServiceImpl extends ServiceImpl<ProductPartsMapper, ProductParts> implements ProductPartsService {

    @DubboReference
    private RemoteAppSnowBlowerService remoteAppSnowBlowerService;

    @Autowired
    private PartsService partsService;

    @DubboReference
    private RemoteTechProductOperationService remoteTechProductOperationService;

    @Override
    public PageResult<ProductPartsVo> page(ProductPartsPageDto req) {
        //分页查询产品配件关系
        PageResult<ProductPartsVo> res = new PageResult<>(req.getPageNum(), req.getPageSize());
        Page<ProductParts> page = this.page(new Page<>(req.getPageNum(), req.getPageSize()), new LambdaQueryWrapper<ProductParts>().eq(ProductParts::getProductId, req.getProductId())
                .eq(StringUtils.isNotEmpty(req.getPartsId()), ProductParts::getPartsId, req.getPartsId())
                .orderByAsc(ProductParts::getOrderNum)
                .orderByDesc(ProductParts::getUpdateTime));
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return res;
        }
        res.setTotal(page.getTotal());
        //查询配件信息
        Map<Long, Parts> partsMap = partsService.listByIds(
                        page.getRecords()
                                .stream()
                                .map(ProductParts::getPartsId)
                                .collect(Collectors.toList())
                )
                .stream()
                .collect(Collectors.toMap(BaseDo::getId, Function.identity()));

        //组装返回
        res.setList(page.getRecords().stream().map(e -> {
            ProductPartsVo vo = new ProductPartsVo();
            vo.setInstanceId(e.getInstanceId());
            vo.setMaintenancePeriod(e.getMaintenancePeriod());
            vo.setMaintenanceType(e.getMaintenanceType());
            vo.setMaintenanceRemind(e.getMaintenanceRemind());
            Parts p = partsMap.getOrDefault(e.getPartsId(), new Parts());
            PartsVo parts = new PartsVo();
            BeanUtils.copyProperties(p, parts);
            parts.setPartsId(p.getId());
            parts.setName(MultiLanguageUtil.getByLangCode(Optional.ofNullable(p.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setParts(parts);
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    @Override
    public void add(ProductPartsAddDto req) {
        if (CollectionUtils.isEmpty(req.getPartsId())) {
            throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_ADD_PARTS_ID_LIST_EMPTY);
        }
        //检查产品是否存在
        remoteTechProductOperationService.checkProductExistById(req.getProductId());

        List<Long> partsIds = findPartsIdList(req.getProductId());
        //已添加的产品 配件关系是否存在
        boolean contain = req.getPartsId().stream().anyMatch(partsIds::contains);
        if (contain) {
            throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_ADD_PARTS_ID_EXIST);
        }
        // 判断新增的配件id是否存在
        req.getPartsId().forEach(e -> {
            long count = partsService.count(new LambdaQueryWrapper<Parts>().eq(BaseDo::getId, e));
            if (count <= 0) {
                throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_ADD_PARTS_PART_ID_NOT_EXIST, e);
            }
        });
        List<ProductParts> pps = req.getPartsId().stream().map(e -> {
            ProductParts pp = new ProductParts();
            pp.setProductId(req.getProductId());
            pp.setPartsId(e);
            pp.setOrderNum(0);
            pp.setInstanceId(SnowFlake.nextId());
            return pp;
        }).collect(Collectors.toList());
        this.saveBatch(pps);
        // 产品更新后， 配件更新
        remoteAppSnowBlowerService.productPartsUpdateCallback(req.getProductId());
    }

    @Override
    public ProductPartsVo detail(Long instanceId) {
        if (instanceId == null) {
            throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_INSTANCE_ID_NULL);
        }
        ProductParts one = this.getOne(new LambdaQueryWrapper<ProductParts>().eq(ProductParts::getInstanceId, instanceId));
        if (one == null) {
            throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_NULL, instanceId);
        }

        ProductPartsVo res = new ProductPartsVo();
        res.setInstanceId(one.getInstanceId());
        res.setMaintenancePeriod(one.getMaintenancePeriod());
        res.setMaintenanceType(one.getMaintenanceType());
        res.setMaintenanceRemind(one.getMaintenanceRemind());
        Parts p = partsService.getById(one.getPartsId());
        if (p != null) {
            PartsVo parts = new PartsVo();
            BeanUtils.copyProperties(p, parts);
            parts.setPartsId(p.getId());
            parts.setName(MultiLanguageUtil.getByLangCode(Optional.ofNullable(p.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            res.setParts(parts);
        }
        return res;
    }

    @Override
    public void edit(ProductPartsEditDto req) {
        if (Objects.isNull(req.getInstanceId())) {
            throw ExceptionMessageUtil.getException(PARAMETER_NOT_PROVIDED,"instanceId");
        }
        if (Objects.isNull(req.getMaintenanceType())) {
            throw ExceptionMessageUtil.getException(PARAMETER_NOT_PROVIDED,"maintenanceType");
        }
        if (Objects.isNull(req.getMaintenancePeriod())) {
            throw ExceptionMessageUtil.getException(PARAMETER_NOT_PROVIDED,"maintenancePeriod");
        }
        if (Objects.isNull(req.getMaintenanceRemind())) {
            throw ExceptionMessageUtil.getException(PARAMETER_NOT_PROVIDED,"maintenanceRemind");
        }
        if (req.getMaintenanceType() != null && (req.getMaintenancePeriod()==null || req.getMaintenancePeriod()<1 || req.getMaintenanceRemind()==null || req.getMaintenanceRemind()<1)) {
            throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_MAINTENANCE_PARAM_ERROR);
        }
        if (!Objects.isNull(req.getMaintenancePeriod()) && !Objects.isNull(req.getMaintenanceRemind()) && req.getMaintenancePeriod() < req.getMaintenanceRemind()) {
            throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_MAINTENANCE_CYCLE_ERROR);
        }
        ProductParts one = this.getOne(new LambdaQueryWrapper<ProductParts>().eq(ProductParts::getInstanceId, req.getInstanceId()));
        if (one == null) {
            throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_NULL, req.getInstanceId());
        }
        this.update(new ProductParts(), new LambdaUpdateWrapper<ProductParts>()
                .set(ProductParts::getMaintenancePeriod, req.getMaintenancePeriod())
                .set(ProductParts::getMaintenanceType, req.getMaintenanceType())
                .set(ProductParts::getMaintenanceRemind, req.getMaintenanceRemind())
                .eq(ProductParts::getId, one.getId()));

        // 产品更新后， 配件更新
        remoteAppSnowBlowerService.productPartsUpdateCallback(one.getProductId());
    }

    @Override
    public void delete(Long instanceId) {
        if (instanceId == null) {
            throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_INSTANCE_ID_NULL);
        }
        ProductParts one = this.getOne(new LambdaQueryWrapper<ProductParts>().eq(ProductParts::getInstanceId, instanceId));
        if (one == null) {
            throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_NULL, instanceId);
        }
        this.removeById(one.getId());

        // 产品更新后， 配件更新
        remoteAppSnowBlowerService.productPartsUpdateCallback(one.getProductId());
    }

    @Override
    public List<Long> findPartsIdList(Long productId) {
        return this.list(new LambdaQueryWrapper<ProductParts>()
                        .eq(ProductParts::getProductId, productId)
                        .select(ProductParts::getId, ProductParts::getPartsId))
                .stream().map(ProductParts::getPartsId).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductPartsVo> list(Long productId) {
        List<ProductParts> list = this.list(new LambdaQueryWrapper<ProductParts>().eq(ProductParts::getProductId, productId)
                .orderByAsc(ProductParts::getOrderNum)
                .orderByDesc(ProductParts::getCreateTime));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        Map<Long, Parts> partsMap = partsService.listByIds(list.stream().map(ProductParts::getPartsId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(BaseDo::getId, Function.identity()));
        return list.stream().map(e -> {
            ProductPartsVo vo = new ProductPartsVo();
            vo.setInstanceId(e.getInstanceId());
            vo.setMaintenancePeriod(e.getMaintenancePeriod());
            vo.setMaintenanceType(e.getMaintenanceType());
            vo.setMaintenanceRemind(e.getMaintenanceRemind());
            Parts p = partsMap.getOrDefault(e.getPartsId(), new Parts());
            PartsVo parts = new PartsVo();
            BeanUtils.copyProperties(p, parts);
            parts.setPartsId(p.getId());
            parts.setName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(p.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setParts(parts);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void order(List<Long> instanceIds) {
        if (CollectionUtils.isEmpty(instanceIds)) {
            throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_ORDER_PRODUCT_PARTS_ID_LIST_EMPTY);
        }
        List<ProductParts> list = this.list(new LambdaQueryWrapper<ProductParts>().in(ProductParts::getInstanceId, instanceIds));
        if (list.size() != instanceIds.size()) {
            throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_ORDER_ERROR);
        }
        Map<Long, List<ProductParts>> collect = list.stream().collect(Collectors.groupingBy(ProductParts::getProductId));
        if (collect.size() > 1) {
            throw ExceptionMessageUtil.getException(OPERATION_PRODUCT_PARTS_ORDER_ERROR);
        }
        Map<Long, Long> map = list.stream().collect(Collectors.toMap(ProductParts::getInstanceId, BaseDo::getId));
        List<ProductParts> l = new ArrayList<>();
        for (int i = 0, j = instanceIds.size(); i < j; i++) {
            ProductParts pp = new ProductParts();
            pp.setId(map.get(instanceIds.get(i)));
            pp.setOrderNum(i);
            l.add(pp);
        }
        updateBatchById(l);
    }

}
