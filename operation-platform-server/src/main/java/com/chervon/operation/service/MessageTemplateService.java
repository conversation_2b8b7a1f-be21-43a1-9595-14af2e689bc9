package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.operation.domain.dataobject.MessageTemplate;
import com.chervon.operation.domain.dto.message.template.MessageTemplateAddDto;
import com.chervon.operation.domain.dto.message.template.MessageTemplateEditDto;
import com.chervon.operation.api.dto.MessageTemplateIdDto;
import com.chervon.operation.api.dto.MessageTemplateListDto;
import com.chervon.operation.api.vo.MessageTemplateDetailVo;
import com.chervon.operation.api.vo.MessageTemplateListVo;

/**
 * <AUTHOR>
 * @since 2022-09-07 16:00
 **/
public interface MessageTemplateService extends IService<MessageTemplate> {
    /**
     * 添加消息模板
     *
     * @param messageTemplateAddDto 添加消息模板Dto
     */
    void add(MessageTemplateAddDto messageTemplateAddDto);

    /**
     * 编辑消息模板
     *
     * @param messageTemplateEditDto 编辑消息模板Dto
     */
    void edit(MessageTemplateEditDto messageTemplateEditDto);

    /**
     * 删除消息模板
     *
     * @param messageTemplateIdDto 模板消息Id
     */
    void delete(MessageTemplateIdDto messageTemplateIdDto);

    /**
     * 分页获取消息模板
     *
     * @param messageTemplateListDto 查询条件
     * @return 分页结果
     */
    PageResult<MessageTemplateListVo> page(MessageTemplateListDto messageTemplateListDto);

    /**
     * 获取消息模板模板
     *
     * @param messageTemplateIdDto 消息模板Id
     * @return 消息模板详情Vo
     */
    MessageTemplateDetailVo detail(MessageTemplateIdDto messageTemplateIdDto);
}
