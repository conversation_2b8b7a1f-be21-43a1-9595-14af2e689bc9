package com.chervon.operation.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.dataobject.OperationGuidance;
import com.chervon.operation.domain.dataobject.ProductOperationGuidance;
import com.chervon.operation.domain.dto.operationguidance.product.ProductOperationGuidanceDto;
import com.chervon.operation.domain.dto.operationguidance.product.ProductOperationGuidanceOrderDto;
import com.chervon.operation.domain.dto.postsale.PostSaleBaseDto;
import com.chervon.operation.domain.vo.operationguidance.OperationGuidanceVo;
import com.chervon.operation.domain.vo.operationguidance.product.ProductOperationGuidanceVo;
import com.chervon.operation.mapper.ProductOperationGuidanceMapper;
import com.chervon.operation.service.OperationGuidanceService;
import com.chervon.operation.service.ProductOperationGuidanceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/31 14:02
 */
@Service
@Slf4j
public class ProductOperationGuidanceServiceImpl extends ServiceImpl<ProductOperationGuidanceMapper, ProductOperationGuidance> implements ProductOperationGuidanceService {

    @Autowired
    private OperationGuidanceService operationGuidanceService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Override
    public List<ProductOperationGuidanceVo> listByProductId(Long productId) {
        List<ProductOperationGuidance> list = this.list(new LambdaQueryWrapper<ProductOperationGuidance>()
                .eq(ProductOperationGuidance::getProductId, productId)
                .orderByAsc(ProductOperationGuidance::getSequence)
                .orderByDesc(ProductOperationGuidance::getCreateTime));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> operationGuidanceIds = list.stream().map(ProductOperationGuidance::getOperationGuidanceId).distinct().collect(Collectors.toList());
        List<OperationGuidance> operationGuidanceList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(operationGuidanceIds)) {
            operationGuidanceList = operationGuidanceService.list(new LambdaQueryWrapper<OperationGuidance>().in(OperationGuidance::getId, operationGuidanceIds));
        }
        Map<Long, OperationGuidance> collect = operationGuidanceList.stream().collect(Collectors.toMap(BaseDo::getId, Function.identity()));
        return list.stream().map(e -> {
            ProductOperationGuidanceVo vo = new ProductOperationGuidanceVo();
            vo.setIfCommon(e.getCommonId() != null);
            vo.setProductOperationGuidanceId(e.getId());
            OperationGuidanceVo operationGuidance = new OperationGuidanceVo();
            OperationGuidance guidance = collect.getOrDefault(e.getOperationGuidanceId(), new OperationGuidance());
            BeanUtils.copyProperties(guidance, operationGuidance);
            operationGuidance.setName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(guidance.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            operationGuidance.setDescription(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(guidance.getDescriptionLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setOperationGuidance(operationGuidance);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public void edit(ProductOperationGuidanceDto req) {
        if (req.getOperateItemId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_OPERATION_GUIDANCE_ID_NULL);
        }
        ProductOperationGuidance productOperationGuidance = this.getById(req.getOperateItemId());
        if (productOperationGuidance == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_OPERATION_GUIDANCE_NULL, req.getOperateItemId());
        }
        if (productOperationGuidance.getCommonId() != null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_OPERATION_GUIDANCE_COMMON_CANNOT_MODIFY);
        }
        req.getOperationGuidance().setOperationGuidanceId(productOperationGuidance.getOperationGuidanceId());
        operationGuidanceService.edit(req.getOperationGuidance());
        ProductOperationGuidance one = new ProductOperationGuidance();
        one.setId(productOperationGuidance.getId());
        this.updateById(one);
    }

    @Override
    public void delete(PostSaleBaseDto req) {
        if (req.getOperateItemId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_OPERATION_GUIDANCE_ID_NULL);
        }
        ProductOperationGuidance productOperationGuidance = this.getById(req.getOperateItemId());
        if (productOperationGuidance == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_OPERATION_GUIDANCE_NULL, req.getOperateItemId());
        }
        if (productOperationGuidance.getCommonId() != null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_OPERATION_GUIDANCE_COMMON_CANNOT_MODIFY);
        }
        OperationGuidance operationGuidance = operationGuidanceService.getById(productOperationGuidance.getOperationGuidanceId());
        if (operationGuidance != null) {
            operationGuidanceService.removeById(operationGuidance.getId());
            // 清理多语言
            List<Long> list = new ArrayList<>();
            if (operationGuidance.getNameLangId() != null) {
                list.add(operationGuidance.getNameLangId());
            }
            if (operationGuidance.getDescriptionLangId() != null) {
                list.add(operationGuidance.getDescriptionLangId());
            }
            if (CollectionUtil.isNotEmpty(list)) {
                remoteMultiLanguageService.deleteByLangIds(list);
            }
        }
        this.removeById(productOperationGuidance.getId());
    }

    @Override
    public String getUrl(Long productOperationGuidanceId) {
        if (productOperationGuidanceId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_OPERATION_GUIDANCE_ID_NULL);
        }
        ProductOperationGuidance productOperationGuidance = this.getById(productOperationGuidanceId);
        if (productOperationGuidance == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_OPERATION_GUIDANCE_NULL, productOperationGuidanceId);
        }
        OperationGuidance guidance = operationGuidanceService.getById(productOperationGuidance.getOperationGuidanceId());
        return Optional.ofNullable(guidance).orElse(new OperationGuidance()).getUrl();
    }

    @Override
    public void add(ProductOperationGuidanceDto req) {
        OperationGuidance operationGuidance = operationGuidanceService.add(req.getOperationGuidance());
        ProductOperationGuidance one = new ProductOperationGuidance();
        one.setOperationGuidanceId(operationGuidance.getId());
        one.setProductId(req.getProductId());
        one.setSequence(0);
        this.save(one);
    }

    @Override
    public ProductOperationGuidanceVo detail(Long productOperationGuidanceId) {
        if (productOperationGuidanceId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_OPERATION_GUIDANCE_ID_NULL);
        }
        ProductOperationGuidance productOperationGuidance = this.getById(productOperationGuidanceId);
        if (productOperationGuidance == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_OPERATION_GUIDANCE_NULL, productOperationGuidanceId);
        }
        ProductOperationGuidanceVo res = new ProductOperationGuidanceVo();
        res.setProductOperationGuidanceId(productOperationGuidance.getId());
        res.setIfCommon(productOperationGuidance.getCommonId() != null);
        OperationGuidanceVo operationGuidance = new OperationGuidanceVo();
        OperationGuidance guidance = operationGuidanceService.getById(productOperationGuidance.getOperationGuidanceId());
        if (guidance != null) {
            BeanUtils.copyProperties(guidance, operationGuidance);
            operationGuidance.setName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(guidance.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            operationGuidance.setDescription(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(guidance.getDescriptionLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        }
        res.setOperationGuidance(operationGuidance);
        return res;
    }

    @Override
    public void order(ProductOperationGuidanceOrderDto req) {
        if (CollectionUtils.isEmpty(req.getProductOperationGuidanceIds())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_OPERATION_GUIDANCE_ORDER_ID_LIST_EMPTY);
        }
        List<ProductOperationGuidanceVo> list = this.listByProductId(req.getProductId());

        List<Long> ids = list.stream().map(ProductOperationGuidanceVo::getProductOperationGuidanceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        req.getProductOperationGuidanceIds().removeIf(e -> !ids.contains(e));
        if (!CollectionUtils.isEmpty(req.getProductOperationGuidanceIds())) {
            List<ProductOperationGuidance> ll = new ArrayList<>();
            for (int i = 0; i < req.getProductOperationGuidanceIds().size(); i++) {
                ProductOperationGuidance p = new ProductOperationGuidance();
                p.setId(req.getProductOperationGuidanceIds().get(i));
                p.setSequence(i);
                ll.add(p);
            }
            this.updateBatchById(ll);
        }
    }


    @Override
    public List<Long> listProductIdByCommonId(Long commonOperationGuidanceId) {
        return list(Wrappers.<ProductOperationGuidance>lambdaQuery()
                .eq(ProductOperationGuidance::getCommonId,commonOperationGuidanceId))
                .stream().map(ProductOperationGuidance::getProductId)
                .collect(Collectors.toList());
    }

    @Override
    public long countProductIdByCommonId(Long commonOperationGuidanceId) {
        return count(Wrappers.<ProductOperationGuidance>lambdaQuery()
                .eq(ProductOperationGuidance::getCommonId,commonOperationGuidanceId));

    }

    @Override
    public void checkBoundByCommonIdAndPIds(Long commonId, List<String> pIds) {
           if(countByCommonIdAndPIds(commonId,pIds)>0){
               throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_PRODUCT_ALREDY_BINDED,pIds);
           }
    }

    public long countByCommonIdAndPIds(Long commonId,List<String> pIds) {
        return count(Wrappers.<ProductOperationGuidance>lambdaQuery()
                .eq(ProductOperationGuidance::getCommonId,commonId)
                .in(!CollectionUtils.isEmpty(pIds),ProductOperationGuidance::getProductId,pIds));

    }
}
