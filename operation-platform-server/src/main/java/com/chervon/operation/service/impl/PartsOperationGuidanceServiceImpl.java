package com.chervon.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.api.exception.OperationException;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.dataobject.OperationGuidance;
import com.chervon.operation.domain.dataobject.PartsFaq;
import com.chervon.operation.domain.dataobject.PartsOperationGuidance;
import com.chervon.operation.domain.dto.operationguidance.parts.PartsOperationGuidanceDto;
import com.chervon.operation.domain.vo.operationguidance.OperationGuidanceVo;
import com.chervon.operation.domain.vo.operationguidance.parts.PartsOperationGuidanceVo;
import com.chervon.operation.mapper.PartsOperationGuidanceMapper;
import com.chervon.operation.service.OperationGuidanceService;
import com.chervon.operation.service.PartsOperationGuidanceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/22 19:31
 */
@Service
@Slf4j
public class PartsOperationGuidanceServiceImpl extends ServiceImpl<PartsOperationGuidanceMapper, PartsOperationGuidance> implements PartsOperationGuidanceService {

    @Autowired
    private OperationGuidanceService operationGuidanceService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Override
    public List<PartsOperationGuidanceVo> listByPartsId(Long partsId) {
        List<PartsOperationGuidance> list = this.list(new LambdaQueryWrapper<PartsOperationGuidance>()
                .eq(PartsOperationGuidance::getPartsId, partsId)
                .orderByAsc(PartsOperationGuidance::getSequence)
                .orderByDesc(PartsOperationGuidance::getCreateTime));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> operationGuidanceIds = list.stream().map(PartsOperationGuidance::getOperationGuidanceId).distinct().collect(Collectors.toList());
        List<OperationGuidance> operationGuidanceList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(operationGuidanceIds)) {
            operationGuidanceList = operationGuidanceService.list(new LambdaQueryWrapper<OperationGuidance>().in(OperationGuidance::getId, operationGuidanceIds));
        }
        Map<Long, OperationGuidance> collect = operationGuidanceList.stream().collect(Collectors.toMap(BaseDo::getId, Function.identity()));
        return list.stream().map(e -> {
            PartsOperationGuidanceVo vo = new PartsOperationGuidanceVo();
            vo.setPartsOperationGuidanceId(e.getId());
            OperationGuidanceVo operationGuidance = new OperationGuidanceVo();
            OperationGuidance guidance = collect.getOrDefault(e.getOperationGuidanceId(), new OperationGuidance());
            BeanUtils.copyProperties(guidance, operationGuidance);
            operationGuidance.setName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(guidance.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            operationGuidance.setDescription(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(guidance.getDescriptionLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setOperationGuidance(operationGuidance);
            return vo;
        }).collect(Collectors.toList());
    }

    private void checkPartsOperationGuidance(PartsOperationGuidanceDto req) {
        if (req.getOperationGuidance() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_OPERATION_GUIDANCE_NULL, "null");
        }
    }

    @Override
    public void edit(PartsOperationGuidanceDto req) {
        if (req.getOperateItemId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_OPERATION_GUIDANCE_ID_NULL);
        }
        PartsOperationGuidance partsOperationGuidance = this.getById(req.getOperateItemId());
        if (partsOperationGuidance == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_OPERATION_GUIDANCE_NULL, req.getOperateItemId());
        }
        checkPartsOperationGuidance(req);
        req.getOperationGuidance().setOperationGuidanceId(partsOperationGuidance.getOperationGuidanceId());
        operationGuidanceService.edit(req.getOperationGuidance());
        PartsOperationGuidance one = new PartsOperationGuidance();
        one.setId(partsOperationGuidance.getId());
        this.updateById(one);
    }

    @Override
    public void delete(Long partsOperationGuidanceId) {
        if (partsOperationGuidanceId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_OPERATION_GUIDANCE_ID_NULL);
        }
        PartsOperationGuidance partsOperationGuidance = this.getById(partsOperationGuidanceId);
        if (partsOperationGuidance == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_OPERATION_GUIDANCE_NULL, partsOperationGuidanceId);
        }
        OperationGuidance operationGuidance = operationGuidanceService.getById(partsOperationGuidance.getOperationGuidanceId());
        if (operationGuidance != null) {
            List<Long> langs = new ArrayList<>();
            if (operationGuidance.getNameLangId() != null) {
                langs.add(operationGuidance.getNameLangId());
            }
            if (operationGuidance.getDescriptionLangId() != null) {
                langs.add(operationGuidance.getDescriptionLangId());
            }
            if (langs.size() > 0) {
                remoteMultiLanguageService.deleteByLangIds(langs);
            }
            operationGuidanceService.removeById(partsOperationGuidance.getOperationGuidanceId());
        }
        this.removeById(partsOperationGuidance.getId());
    }

    @Override
    public String getUrl(Long partsOperationGuidanceId) {
        if (partsOperationGuidanceId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_OPERATION_GUIDANCE_ID_NULL);
        }
        PartsOperationGuidance partsOperationGuidance = this.getById(partsOperationGuidanceId);
        if (partsOperationGuidance == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_OPERATION_GUIDANCE_NULL, partsOperationGuidanceId);
        }
        OperationGuidance operationGuidance = operationGuidanceService.getById(partsOperationGuidance.getOperationGuidanceId());
        return Optional.ofNullable(operationGuidance).orElse(new OperationGuidance()).getUrl();
    }

    @Override
    public void add(PartsOperationGuidanceDto req) {
        checkPartsOperationGuidance(req);
        OperationGuidance userOperationGuidance = operationGuidanceService.add(req.getOperationGuidance());
        PartsOperationGuidance one = new PartsOperationGuidance();
        one.setOperationGuidanceId(userOperationGuidance.getId());
        one.setPartsId(req.getPartsId());
        this.save(one);
    }

    @Override
    public PartsOperationGuidanceVo detail(Long partsOperationGuidanceId) {
        if (partsOperationGuidanceId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_OPERATION_GUIDANCE_ID_NULL);
        }
        PartsOperationGuidance partsOperationGuidance = this.getById(partsOperationGuidanceId);
        if (partsOperationGuidance == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_OPERATION_GUIDANCE_NULL, partsOperationGuidanceId);
        }
        PartsOperationGuidanceVo res = new PartsOperationGuidanceVo();
        res.setPartsOperationGuidanceId(partsOperationGuidance.getId());
        OperationGuidanceVo manual = new OperationGuidanceVo();
        OperationGuidance operationGuidance = operationGuidanceService.getById(partsOperationGuidance.getOperationGuidanceId());
        if (operationGuidance != null) {
            BeanUtils.copyProperties(operationGuidance, manual);
            manual.setName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(operationGuidance.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            manual.setDescription(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(operationGuidance.getDescriptionLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        }
        res.setOperationGuidance(manual);
        return res;
    }

    @Override
    public void order(List<Long> partsOperationGuidanceIds) {
        if (CollectionUtils.isEmpty(partsOperationGuidanceIds)) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_PARTS_OPERATION_GUIDANCE_ORDER_ID_LIST_EMPTY);
        }
        List<PartsOperationGuidance> ll = new ArrayList<>();
        for (int i = 0; i < partsOperationGuidanceIds.size(); i++) {
            PartsOperationGuidance p = new PartsOperationGuidance();
            p.setId(partsOperationGuidanceIds.get(i));
            p.setSequence(i);
            ll.add(p);
        }
        this.updateBatchById(ll);
    }
}
