package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.operation.domain.dataobject.OperationGuidance;
import com.chervon.operation.domain.dto.operationguidance.OperationGuidanceDto;

/**
 * <AUTHOR>
 * @date 2022/8/19 15:32
 */
public interface OperationGuidanceService extends IService<OperationGuidance> {

    /**
     * 新增操作指导
     *
     * @param operationGuidance 对象
     * @return 操作指导
     */
    OperationGuidance add(OperationGuidanceDto operationGuidance);

    /**
     * 修改操作指导
     *
     * @param operationGuidance 对象
     */
    void edit(OperationGuidanceDto operationGuidance);
}
