package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.operation.domain.dataobject.PartsOperationGuidance;
import com.chervon.operation.domain.dto.operationguidance.parts.PartsOperationGuidanceDto;
import com.chervon.operation.domain.vo.operationguidance.parts.PartsOperationGuidanceVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/22 19:31
 */
public interface PartsOperationGuidanceService extends IService<PartsOperationGuidance> {

    /**
     * 根据配件id获取操作指导列表
     *
     * @param partsId 配件id
     * @return 操作指导列表
     */
    List<PartsOperationGuidanceVo> listByPartsId(Long partsId);

    /**
     * 编辑
     *
     * @param req 修改对象
     */
    void edit(PartsOperationGuidanceDto req);

    /**
     * 删除
     *
     * @param partsOperationGuidanceId 配件下用户手册id
     */
    void delete(Long partsOperationGuidanceId);

    /**
     * 获取下载地址
     *
     * @param partsOperationGuidanceId 配件下用户手册id
     * @return url
     */
    String getUrl(Long partsOperationGuidanceId);

    /**
     * 新增
     *
     * @param req 新增对象
     */
    void add(PartsOperationGuidanceDto req);

    /**
     * 详情
     *
     * @param partsOperationGuidanceId 配件下用户手册id
     * @return 详情
     */
    PartsOperationGuidanceVo detail(Long partsOperationGuidanceId);

    /**
     * 排序
     *
     * @param partsOperationGuidanceIds id集合
     */
    void order(List<Long> partsOperationGuidanceIds);
}
