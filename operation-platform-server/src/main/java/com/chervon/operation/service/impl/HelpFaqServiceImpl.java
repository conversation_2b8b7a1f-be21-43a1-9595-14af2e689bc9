package com.chervon.operation.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.iot.app.api.RemoteAppHelpFaqService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.api.vo.HelpFaqAppVo;
import com.chervon.operation.common.CommonConstant;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.bo.DictBo;
import com.chervon.operation.domain.bo.DictNodeBo;
import com.chervon.operation.domain.dataobject.HelpFaq;
import com.chervon.operation.domain.dto.faq.help.HelpFaqDto;
import com.chervon.operation.domain.dto.faq.help.HelpFaqPageDto;
import com.chervon.operation.domain.dto.faq.help.HelpFaqRead;
import com.chervon.operation.domain.vo.faq.help.HelpFaqExcel;
import com.chervon.operation.domain.vo.faq.help.HelpFaqPageVo;
import com.chervon.operation.domain.vo.faq.help.HelpFaqVo;
import com.chervon.operation.mapper.HelpFaqMapper;
import com.chervon.operation.service.DictService;
import com.chervon.operation.service.HelpFaqModelService;
import com.chervon.operation.service.HelpFaqService;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.api.vo.sf.SfHelpFaqRecord;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chervon.common.core.constant.CommonConstant.*;
import static com.chervon.operation.api.exception.OperationErrorCode.*;

/**
 * <AUTHOR>
 * @date 2022/11/17 11:11
 */
@Service
@Slf4j
public class HelpFaqServiceImpl extends ServiceImpl<HelpFaqMapper, HelpFaq> implements HelpFaqService {
    private static final String SOURCE_CODE = "helpFaqSource";
    private static final String APP_SHOW_CODE = "appShow";
    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;
    @Autowired
    private DictService dictService;
    @DubboReference
    private RemoteAppHelpFaqService remoteAppHelpFaqService;
    @DubboReference
    private SaleForceService saleForceService;
    @Autowired
    private HelpFaqMapper helpFaqMapper;
    @Autowired
    private HelpFaqModelService helpFaqModelService;

    @Override
    public PageResult<HelpFaqAppVo> selectAppHelpFaqPage(String searchContent, Integer pageNum, Integer pageSize) {

        IPage<HelpFaq> pageResult = helpFaqMapper.selectAppHelpFaqPage(searchContent, new Page<>(pageNum, pageSize));
        List<HelpFaq> helpFaqs = pageResult.getRecords();
        //数据转换
        List<HelpFaqAppVo> convertHelpFaqs = helpFaqs.stream().map(e -> {
            HelpFaqAppVo vo = new HelpFaqAppVo();
            BeanUtils.copyProperties(e, vo);
            vo.setHelpFaqId(e.getId());
            vo.getModel().addAll(helpFaqModelService.listByHelpFaqId(e.getId()).stream().map(x -> x.getCommodityModel()).collect(Collectors.toList()));
            return vo;
        }).collect(Collectors.toList());
        //拼装结果
        PageResult<HelpFaqAppVo> result = new PageResult<>(pageNum,
                pageSize);
        result.setPages(pageResult.getPages());
        result.setTotal(pageResult.getTotal());
        result.setList(convertHelpFaqs);
        return result;
    }

    @Override
    public PageResult<HelpFaqPageVo> page(HelpFaqPageDto req) {
        PageResult<HelpFaqPageVo> res = new PageResult<>(req.getPageNum(), req.getPageSize());
        IPage<HelpFaq> page = helpFaqMapper.selectHelpFaqPage(new Page<>(req.getPageNum(), req.getPageSize()), req);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return res;
        }
        res.setTotal(page.getTotal());
        res.setList(page.getRecords().stream().map(e -> {
            HelpFaqPageVo vo = new HelpFaqPageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setHelpFaqId(e.getId());
            vo.getModel().addAll(helpFaqModelService.listByHelpFaqId(e.getId()).stream().map(x -> x.getCommodityModel()).collect(Collectors.toList()));
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    private void check(HelpFaqDto req) {
        if (StringUtils.isBlank(req.getTitle())) {
            throw ExceptionMessageUtil.getException(OPERATION_HELP_FAQ_TITLE_NULL);
        }
        if (StringUtils.isBlank(req.getAnswer())) {
            throw ExceptionMessageUtil.getException(OPERATION_HELP_FAQ_ANSWER_NULL);
        }
        if (CollectionUtils.isEmpty(req.getModel())) {
            throw ExceptionMessageUtil.getException(OPERATION_HELP_FAQ_MODEL_EMPTY);
        } else {
            req.getModel().removeIf(StringUtils::isBlank);
            if (CollectionUtils.isEmpty(req.getModel())) {
                throw ExceptionMessageUtil.getException(OPERATION_HELP_FAQ_MODEL_EMPTY);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(HelpFaqDto req) {
        check(req);
        HelpFaq helpFaq = new HelpFaq();
        helpFaq.setTitle(req.getTitle());
        helpFaq.setAnswer(req.getAnswer());
        helpFaq.setAppShowCode(CommonConstant.HELP_FAQ_APP_SHOW_STATUS_1);
        helpFaq.setSourceCode(CommonConstant.HELP_FAQ_DATA_SOURCE_PLATFORM);
        save(helpFaq);
        //更新helpFaq和model关系
        helpFaqModelService.batchUpdateByHelpFaqId(helpFaq.getId(), req.getModel());

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(HelpFaqDto req) {
        if (req.getHelpFaqId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_HELP_FAQ_ID_NULL);
        }
        HelpFaq help = this.getById(req.getHelpFaqId());
        if (help == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_HELP_FAQ_NULL, req.getHelpFaqId());
        }
        if (!StringUtils.equals("1", help.getSourceCode())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_HELP_FAQ_CANNOT_EDIT);
        }
        check(req);
        HelpFaq one = new HelpFaq();
        one.setId(help.getId());
        one.setTitle(req.getTitle());
        one.setAnswer(req.getAnswer());
        updateById(one);
        //更新helpFaq和model关系
        helpFaqModelService.batchUpdateByHelpFaqId(req.getHelpFaqId(), req.getModel());

    }

    @Override
    public HelpFaqVo detail(Long helpFaqId) {
        if (helpFaqId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_HELP_FAQ_ID_NULL);
        }
        HelpFaq help = this.getById(helpFaqId);
        if (help == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_HELP_FAQ_NULL, helpFaqId);
        }

        HelpFaqVo res = new HelpFaqVo();
        BeanUtils.copyProperties(help, res);
        res.setHelpFaqId(helpFaqId);
        res.getModel().addAll(helpFaqModelService.listByHelpFaqId(helpFaqId).stream().map(x -> x.getCommodityModel()).collect(Collectors.toList()));
        res.setAppShowCode(help.getAppShowCode());
        return res;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long helpFaqId) {
        if (helpFaqId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_HELP_FAQ_ID_NULL);
        }
        HelpFaq help = this.getById(helpFaqId);
        if (help == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_HELP_FAQ_NULL, helpFaqId);
        }
        if (!StringUtils.equals("1", help.getSourceCode())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_HELP_FAQ_CANNOT_EDIT);
        }
        removeById(helpFaqId);
        helpFaqModelService.removeByHelpFaqId(helpFaqId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> importHelpFaq(MultipartFile file) {
        List<String> res = new ArrayList<>();
        List<HelpFaqRead> data;
        try {
            data = EasyExcel.read(file.getInputStream()).head(HelpFaqRead.class).sheet().doReadSync();
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(OPERATION_HELP_FAQ_READ_EXCEL_ERROR);
        }
        if (CollectionUtils.isEmpty(data)) {
            throw ExceptionMessageUtil.getException(OPERATION_HELP_FAQ_READ_EXCEL_EMPTY);
        }
        if (data.size() > 5000) {
            throw ExceptionMessageUtil.getException(OPERATION_HELP_FAQ_READ_EXCEL_MORE_THAN_5000);
        }
        if (!validImport(data, res)) {
            return res;
        }
        //组装表格主键集合
        List<String> dataHelpFaqIds = data.stream().map(HelpFaqRead::getHelpFaqId).collect(Collectors.toList());
        List<HelpFaq> dbHelpFaqs = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataHelpFaqIds)) {
            dbHelpFaqs = list(
                    Wrappers.<HelpFaq>lambdaQuery()
                            .in(HelpFaq::getId, dataHelpFaqIds)
            );
        }
        //组装表格主键对应的表数据
        Map<Long, HelpFaq> dbHelFaqMap = dbHelpFaqs.stream().collect(Collectors.toMap(HelpFaq::getId, Function.identity()));
        data.forEach(e -> {
            HelpFaq faq = new HelpFaq();
            faq.setTitle(e.getTitle());
            faq.setAnswer(e.getAnswer());
            List<String> faqModels = new ArrayList<>();
            if (StringUtils.isNotEmpty(e.getModel())) {
                String models = e.getModel().replaceAll("\\s", "");
                faqModels.addAll(Arrays.stream(models.split("[;,:]")).collect(Collectors.toList()));
            }
            if (StringUtils.isNotBlank(e.getHelpFaqId())) {
                HelpFaq f = dbHelFaqMap.get(Long.parseLong(e.getHelpFaqId()));
                if (f != null) {
                    faq.setId(f.getId());
                    updateById(faq);
                    helpFaqModelService.batchUpdateByHelpFaqId(f.getId(), faqModels);
                } else {
                    faq.setAppShowCode(CommonConstant.HELP_FAQ_APP_SHOW_STATUS_1);
                    faq.setSourceCode(CommonConstant.HELP_FAQ_DATA_SOURCE_PLATFORM);

                }
            } else {
                faq.setAppShowCode(CommonConstant.HELP_FAQ_APP_SHOW_STATUS_1);
                faq.setSourceCode(CommonConstant.HELP_FAQ_DATA_SOURCE_PLATFORM);
            }
            saveOrUpdate(faq);
            helpFaqModelService.batchUpdateByHelpFaqId(faq.getId(), faqModels);
        });
        return res;
    }

    private boolean validImport(List<HelpFaqRead> data, List<String> res) {
        List<Integer> idString = new ArrayList<>();
        Map<String, List<Integer>> idMap = new HashMap<>();
        List<Integer> titleNullFlag = new ArrayList<>();
        List<Integer> answerNullFlag = new ArrayList<>();
        List<Integer> modelNullFlag = new ArrayList<>();
        List<Integer> titleSizeFlag = new ArrayList<>();
        List<Integer> answerSizeFlag = new ArrayList<>();
        List<Integer> modelSizeFlag = new ArrayList<>();

        for (int i = 2, j = data.size() + 2; i < j; i++) {
            HelpFaqRead read = data.get(i - 2);

            if (StringUtils.isNotBlank(read.getHelpFaqId())) {
                try {
                    Long.parseLong(read.getHelpFaqId());
                } catch (Exception e) {
                    idString.add(i);
                }
                if (idMap.get(read.getHelpFaqId()) == null) {
                    List<Integer> ids = new ArrayList<>();
                    ids.add(i);
                    idMap.put(read.getHelpFaqId(), ids);
                } else {
                    idMap.get(read.getHelpFaqId()).add(i);
                }
            }

            if (StringUtils.isBlank(read.getTitle())) {
                titleNullFlag.add(i);
            } else if (read.getTitle().length() > TEXT_SIZE) {
                titleSizeFlag.add(i);
            }

            if (StringUtils.isBlank(read.getAnswer())) {
                answerNullFlag.add(i);
            } else if (read.getAnswer().length() > FAQ_ANSWER_SIZE) {
                answerSizeFlag.add(i);
            }

            if (StringUtils.isBlank(read.getModel())) {
                modelNullFlag.add(i);
            } else if (read.getModel().length() > TEXT_AREA_SIZE) {
                modelSizeFlag.add(i);
            }
        }
        // 检测id是否为数字
        if (idString.size() > 0) {
            res.add("第" + idString.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【帮助ID】非法");
        }
        // 检测id是否唯一
        for (Map.Entry<String, List<Integer>> entry : idMap.entrySet()) {
            List<Integer> value = entry.getValue();
            if (value.size() != 1) {
                res.add("第" + value.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【帮助ID】重复");
            }
        }
        // 检测title是否必填
        if (titleNullFlag.size() > 0) {
            res.add("第" + titleNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【标题】为空");
        }
        // 检测title长度
        if (titleSizeFlag.size() > 0) {
            res.add("第" + titleSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【标题】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测answer是否必填
        if (answerNullFlag.size() > 0) {
            res.add("第" + answerNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【帮助内容】为空");
        }
        // 检测answer长度
        if (answerSizeFlag.size() > 0) {
            res.add("第" + answerSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【帮助内容】内容超过" + FAQ_ANSWER_SIZE + "个字符");
        }
        // 检测model是否必填
        if (modelNullFlag.size() > 0) {
            res.add("第" + modelNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【商品型号】为空");
        }
        // 检测model长度
        if (modelSizeFlag.size() > 0) {
            res.add("第" + modelSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【商品型号】内容超过" + TEXT_AREA_SIZE + "个字符");
        }
        if (!res.isEmpty()) {
            return false;
        }

        List<Long> ids = data.stream().filter(e -> StringUtils.isNotBlank(e.getHelpFaqId())).map(e -> Long.parseLong(e.getHelpFaqId())).distinct().collect(Collectors.toList());
        if (!ids.isEmpty()) {
            List<HelpFaq> faqs = this.list(new LambdaQueryWrapper<HelpFaq>().in(HelpFaq::getId, ids));
            List<Long> existIds = faqs.stream().map(HelpFaq::getId).collect(Collectors.toList());
            for (int i = 2, j = data.size() + 2; i < j; i++) {
                HelpFaqRead read = data.get(i - 2);
                if (StringUtils.isBlank(read.getHelpFaqId())) {
                    continue;
                }
                if (!existIds.contains(Long.parseLong(read.getHelpFaqId()))) {
                    res.add("第" + i + "行，【帮助ID】不存在");
                }
            }
        }
        return res.isEmpty();
    }

    @Override
    public List<HelpFaqExcel> listData(HelpFaqPageDto req) {
        List<HelpFaqExcel> res = new ArrayList<>();

        List<HelpFaq> list = this.getBaseMapper().selectHelpFaqList(req);
        if (CollectionUtils.isEmpty(list)) {
            return res;
        }
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Arrays.asList(SOURCE_CODE, APP_SHOW_CODE));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        res = list.stream().map(e -> {
            HelpFaqExcel excel = new HelpFaqExcel();
            BeanUtils.copyProperties(e, excel);
            excel.setHelpFaqId(e.getId());
            excel.setTitle(e.getTitle());
            excel.setModel(String.join(";", helpFaqModelService.listByHelpFaqId(e.getId()).stream().map(x -> x.getCommodityModel()).collect(Collectors.toList())));
            // 设置数据来源
            excel.setSource(collect.get(SOURCE_CODE).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getSourceCode()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            excel.setReadCount(e.getReadCount());
            excel.setPraiseCount(e.getPraiseCount());
            // 设置app是否显示
            excel.setAppShow(collect.get(APP_SHOW_CODE).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getAppShowCode()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            if (e.getSyncTime() != null) {
                excel.setSyncTime(e.getSyncTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }

            if (e.getCreateTime() != null) {
                excel.setCreateTime(e.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if (e.getUpdateTime() != null) {
                excel.setUpdateTime(e.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            return excel;
        }).collect(Collectors.toList());
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void show(Long helpFaqId) {
        if (helpFaqId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_HELP_FAQ_ID_NULL);
        }
        HelpFaq help = this.getById(helpFaqId);
        if (help == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_HELP_FAQ_NULL, helpFaqId);
        }
        if (StringUtils.equals(CommonConstant.HELP_FAQ_APP_SHOW_STATUS_1, help.getAppShowCode())) {
            throw ExceptionMessageUtil.getException(OPERATION_HELP_FAQ_ALREADY_SHOW);
        }
        HelpFaq one = new HelpFaq();
        one.setId(help.getId());
        one.setAppShowCode(CommonConstant.HELP_FAQ_APP_SHOW_STATUS_1);
        updateById(one);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void hide(Long helpFaqId) {
        if (helpFaqId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_HELP_FAQ_ID_NULL);
        }
        HelpFaq help = this.getById(helpFaqId);
        if (help == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_HELP_FAQ_NULL, helpFaqId);
        }
        if (StringUtils.equals(CommonConstant.HELP_FAQ_APP_SHOW_STATUS_0, help.getAppShowCode())) {
            throw ExceptionMessageUtil.getException(OPERATION_HELP_FAQ_ALREADY_HIDE);
        }
        HelpFaq one = new HelpFaq();
        one.setId(help.getId());
        one.setAppShowCode(CommonConstant.HELP_FAQ_APP_SHOW_STATUS_0);
        updateById(one);
    }

    @Override
    public void renew() {
        syncHelpFaqFromSf();
    }

    @Override
    public List<HelpFaq> recommend(Long helpFaqId, List<String> models) {
        return helpFaqMapper.recommend(helpFaqId, models);
    }

    @Override
    @XxlJob("syncHelpFaqFromSf")
    @Transactional(rollbackFor = Exception.class)
    public void syncHelpFaqFromSf() {
        LocalDateTime syncTime = null;
        //获取xxl-job设置时间
        String defLocalDateTimeTimeStr = XxlJobHelper.getJobParam();
        if (StringUtils.isNotBlank(defLocalDateTimeTimeStr)) {
            syncTime = LocalDateTime.parse(defLocalDateTimeTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } else {
            /**
             * 查询出最新同步的时间
             */
            HelpFaq latestHelpFaq = getOne(Wrappers.<HelpFaq>lambdaQuery()
                    .orderByDesc(HelpFaq::getSyncTime)
                    .last("limit 1")
            );
            if (Objects.nonNull(latestHelpFaq)) {
                syncTime = latestHelpFaq.getSyncTime();
            }
        }
        //从sf获取数据
        List<SfHelpFaqRecord> sfHelpFaqRecords = saleForceService.listKnowledgeKav(syncTime);
        sfHelpFaqRecords.forEach(sfHelpFaqRecord -> {
            HelpFaq oldHelpFaq = getByFaqSFId(sfHelpFaqRecord.getSfId());
            if (Objects.isNull(oldHelpFaq)) {
                oldHelpFaq = new HelpFaq();
            }
            BeanUtils.copyProperties(sfHelpFaqRecord, oldHelpFaq);
            List<String> faqModels = new ArrayList<>();
            if (StringUtils.isNotEmpty(sfHelpFaqRecord.getModel())) {
                String models = sfHelpFaqRecord.getModel().replaceAll("\\s", "");
                faqModels.addAll(Arrays.stream(models.split("[;,:]")).collect(Collectors.toList()));
            }
            oldHelpFaq.setSyncTime(sfHelpFaqRecord.getLastModifiedDate());
            oldHelpFaq.setSourceCode(CommonConstant.HELP_FAQ_DATA_SOURCE_SF);
            saveOrUpdate(oldHelpFaq);
            //更新helpFaq和model关系
            helpFaqModelService.batchUpdateByHelpFaqId(oldHelpFaq.getId(), faqModels);
        });
    }

    private List<HelpFaq> listByFaqSFId(List<String> sfIds) {
        return list(Wrappers.<HelpFaq>lambdaQuery()
                .in(HelpFaq::getSfId, sfIds)
        );
    }

    private HelpFaq getByFaqSFId(String sfId) {
        return getOne(Wrappers.<HelpFaq>lambdaQuery()
                .eq(HelpFaq::getSfId, sfId)
        );
    }
}
