package com.chervon.operation.service;

import com.chervon.common.core.domain.PageResult;
import com.chervon.operation.domain.dto.app.MsgPushResultPageDto;
import com.chervon.operation.domain.vo.app.MsgPushResultVo;

/**
 * <AUTHOR>
 * @date 2022/11/25 10:52
 */
public interface MsgService {

    /**
     * 推送结果查询
     *
     * @param req         查询条件
     * @param messageType 消息类型，0系统消息，1营销消息
     * @return 分页查询
     */
    PageResult<MsgPushResultVo> pushResult(MsgPushResultPageDto req, Integer messageType);
}
