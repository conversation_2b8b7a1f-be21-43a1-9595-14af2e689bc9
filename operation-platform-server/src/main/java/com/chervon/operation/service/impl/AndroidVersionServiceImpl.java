package com.chervon.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.enums.ApplicationEnum;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.dataobject.AndroidVersion;
import com.chervon.operation.domain.dto.androidversion.AndroidVersionDto;
import com.chervon.operation.domain.dto.androidversion.AndroidVersionPageDto;
import com.chervon.operation.domain.vo.androidversion.AndroidVersionPageVo;
import com.chervon.operation.domain.vo.androidversion.AndroidVersionVo;
import com.chervon.operation.mapper.AndroidVersionMapper;
import com.chervon.operation.service.AndroidVersionService;
import com.chervon.operation.util.VersionCompareUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.chervon.operation.api.exception.OperationErrorCode.*;

/**
 * <AUTHOR>
 * @date 2022/11/10 17:45
 */
@Service
@Slf4j
public class AndroidVersionServiceImpl extends ServiceImpl<AndroidVersionMapper, AndroidVersion> implements AndroidVersionService {

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Override
    public PageResult<AndroidVersionPageVo> page(AndroidVersionPageDto req) {
        PageResult<AndroidVersionPageVo> res = new PageResult<>(req.getPageNum(), req.getPageSize());
        LambdaQueryWrapper<AndroidVersion> wrapper = new LambdaQueryWrapper<AndroidVersion>()
                .like(StringUtils.isNotBlank(req.getAndroidVersionId()), AndroidVersion::getId, req.getAndroidVersionId())
                .like(StringUtils.isNotBlank(req.getVersion()), AndroidVersion::getVersion, req.getVersion())
                .like(StringUtils.isNotBlank(req.getName()), AndroidVersion::getName, req.getName())
                .ge(StringUtils.isNotBlank(req.getCreateStartTime()), AndroidVersion::getCreateTime, req.getCreateStartTime())
                .le(StringUtils.isNotBlank(req.getCreateEndTime()), AndroidVersion::getCreateTime, req.getCreateEndTime())
                .ge(StringUtils.isNotBlank(req.getUpdateStartTime()), AndroidVersion::getUpdateTime, req.getUpdateStartTime())
                .le(StringUtils.isNotBlank(req.getUpdateEndTime()), AndroidVersion::getUpdateTime, req.getUpdateEndTime())
                .eq(req.getBusinessType() != null && req.getBusinessType() != 0, AndroidVersion::getBusinessType, req.getBusinessType())
                .orderByDesc(AndroidVersion::getCreateTime);
        Page<AndroidVersion> page = this.page(new Page<>(req.getPageNum(), req.getPageSize()), wrapper);
        res.setTotal(page.getTotal());
        res.setList(page.getRecords().stream().map(e -> {
            AndroidVersionPageVo vo = new AndroidVersionPageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setAndroidVersionId(e.getId());
            vo.setUpdateContent(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(e.getUpdateContentLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    @Override
    public AndroidVersionVo detail(Long androidVersionId) {
        if (androidVersionId == null) {
            throw ExceptionMessageUtil.getException(OPERATION_ANDROID_VERSION_ID_NULL);
        }
        AndroidVersionVo res = new AndroidVersionPageVo();
        AndroidVersion one = Optional.ofNullable(this.getById(androidVersionId)).orElse(new AndroidVersion());
        res.setAndroidVersionId(androidVersionId);
        res.setVersion(one.getVersion());
        res.setName(one.getName());
        res.setUpdateContentLangId(one.getUpdateContentLangId());
        res.setUpdateContent(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(one.getUpdateContentLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        res.setBusinessType(one.getBusinessType());
        return res;
    }

    @Override
    public void add(AndroidVersionDto req) {
        if (req == null) {
            throw ExceptionMessageUtil.getException(OPERATION_ANDROID_VERSION_ADD_REQ_NULL);
        }
        if (StringUtils.isBlank(req.getVersion()) && !VersionCompareUtils.VERSION_REGEX.matcher(req.getVersion()).matches()) {
            throw ExceptionMessageUtil.getException(OPERATION_ANDROID_VERSION_ADD_REQ_VERSION_ILLEGAL);
        }
        if (StringUtils.isBlank(req.getName())) {
            throw ExceptionMessageUtil.getException(OPERATION_ANDROID_VERSION_ADD_REQ_NAME_BLANK);
        }
        if (StringUtils.isBlank(req.getUpdateContent())) {
            throw ExceptionMessageUtil.getException(OPERATION_ANDROID_VERSION_ADD_REQ_UPDATE_CONTENT_BLANK);
        }
        if (req.getBusinessType() == null || req.getBusinessType() == 0) {
            throw ExceptionMessageUtil.getException(OPERATION_ANDROID_VERSION_BUSINESS_TYPE_ERROR);
        }
        QueryWrapper<AndroidVersion> queryWrapper = new QueryWrapper<AndroidVersion>()
                .eq("business_type", req.getBusinessType())
                .orderByDesc("CONVERT(substring_index(substring_index(substring_index(version,'-',1),'.',1),'.',-1),SIGNED)")
                .orderByDesc("CONVERT(substring_index(substring_index(substring_index(version,'-',1),'.',2),'.',-1),SIGNED)")
                .orderByDesc("CONVERT(substring_index(substring_index(substring_index(version,'-',1),'.',3),'.',-1),SIGNED)")
                .last("limit 1");
        AndroidVersion one = this.getOne(queryWrapper);
        if (one != null && VersionCompareUtils.compareVersion(req.getVersion(), one.getVersion()) <= 0) {
            throw ExceptionMessageUtil.getException(OPERATION_ANDROID_VERSION_ADD_REQ_VERSION_SMALL);
        }
        one = new AndroidVersion();
        one.setName(req.getName());
        one.setVersion(req.getVersion());
        one.setUpdateContent(req.getUpdateContent());
        MultiLanguageBo bo = remoteMultiLanguageService.simpleCreateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), req.getUpdateContent(), LocaleContextHolder.getLocale().getLanguage());
        one.setUpdateContentLangId(bo.getLangId());
        one.setUpdateContentLangCode(bo.getLangCode());
        one.setBusinessType(req.getBusinessType());
        this.save(one);
    }

    @Override
    public void delete(Long androidVersionId) {
        if (androidVersionId == null) {
            throw ExceptionMessageUtil.getException(OPERATION_ANDROID_VERSION_ID_NULL);
        }
        AndroidVersion androidVersion = this.getById(androidVersionId);
        if (androidVersion != null) {
            if (StringUtils.isNotEmpty(androidVersion.getUpdateContentLangCode()))
            this.removeById(androidVersionId);
            // 清理多语言
            remoteMultiLanguageService.deleteByLangCodes(Arrays.asList(androidVersion.getUpdateContentLangCode()));
        }
    }
}
