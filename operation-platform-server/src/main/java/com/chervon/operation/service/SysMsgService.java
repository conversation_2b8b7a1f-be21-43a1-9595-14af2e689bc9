package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.app.api.vo.UserSettingBo;
import com.chervon.operation.api.dto.MessagePushResultCountDto;
import com.chervon.operation.domain.dataobject.SysMsg;
import com.chervon.operation.domain.dto.app.SysMsgDto;
import com.chervon.operation.domain.dto.app.SysMsgManagePageDto;
import com.chervon.operation.domain.dto.app.SysMsgOperationDto;
import com.chervon.operation.domain.dto.app.SysMsgReleasePageDto;
import com.chervon.operation.domain.vo.app.SysMsgManagePageVo;
import com.chervon.operation.domain.vo.app.SysMsgReleasePageVo;
import com.chervon.operation.domain.vo.app.SysMsgVo;
import com.chervon.operation.domain.vo.message.MessageRecordResultVo;
import com.chervon.operation.domain.vo.message.MessageRecordSearchVo;
import com.chervon.usercenter.api.vo.AppUserVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/20 18:53
 */
public interface SysMsgService extends IService<SysMsg> {

    /**
     * 系统消息管理分页查询
     *
     * @param req 查询套件
     * @return 分页数据
     */
    PageResult<SysMsgManagePageVo> managePage(SysMsgManagePageDto req);

    /**
     * 系统消息发布管理分页查询
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<SysMsgReleasePageVo> releasePage(SysMsgReleasePageDto req);

    /**
     * 系统消息发布管理分页查询
     *
     * @return 分页数据
     */
    PageResult<MessageRecordResultVo> recordPage(MessageRecordSearchVo messageRecordVo);

    /**
     * 系统消息发布管理
     */
    List<MessageRecordResultVo> recordList(MessageRecordSearchVo messageRecordVo);

    /**
     * 保存
     *
     * @param req 入参
     */
    void save(SysMsgDto req);

    /**
     * 编辑
     *
     * @param req 入参
     */
    void update(SysMsgDto req);

    /**
     * 编辑
     */
    void updateMsgCount(List<MessagePushResultCountDto> countDtoList);

    /**
     * 详情
     *
     * @param id 系统消息id
     * @return 消息详情
     */
    SysMsgVo detail(Long id);

    /**
     * 复制
     *
     * @param req 入参
     */
    void copy(SysMsgDto req);

    /**
     * 操作
     *
     * @param op 操作参数
     */
    void operation(SysMsgOperationDto op);

    /**
     * 查询驳回原因
     *
     * @param op 入参
     * @return 驳回原因
     */
    String view(SysMsgOperationDto op);

    /**
     * 删除
     *
     * @param id 系统消息id
     */
    void delete(Long id);

    /**
     * APP启动后检测是否有需要推送的系统消息
     *
     * @param appUserVo     APP用户RPC类
     * @param userSettingBo 用户设置RPC类
     * @param groupNames    用户所属分组名列表
     */
    void checkAfterAppStarted(AppUserVo appUserVo, UserSettingBo userSettingBo, List<String> groupNames);

    /**
     * 发送消息
     *
     * @param sysMsg 消息对象
     */
    void pushMessage(SysMsg sysMsg);
}
