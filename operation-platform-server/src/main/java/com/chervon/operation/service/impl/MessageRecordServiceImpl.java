package com.chervon.operation.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.message.api.RemoteMessageService;
import com.chervon.message.api.dto.SearchMessageDto;
import com.chervon.message.api.enums.MessageTypeEnum;
import com.chervon.message.api.vo.MessageVo;
import com.chervon.operation.domain.vo.message.MessageRecordResultVo;
import com.chervon.operation.domain.vo.message.MessageRecordSearchVo;
import com.chervon.operation.service.MessageRecordService;
import com.chervon.technology.api.RemoteFaultMessageService;
import com.chervon.technology.api.dto.FaultMessageRecordSearchDto;
import com.chervon.technology.api.vo.FaultMessageRecordVo;
import com.chervon.usercenter.api.service.RemoteAppUserService;
import com.chervon.usercenter.api.vo.AppUserVo;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 手机端推送消息记录
 *
 * <AUTHOR>
 * @since 2022年12月18日
 **/
@SuppressWarnings("Duplicates")
@Slf4j
@Service
@AllArgsConstructor
public class MessageRecordServiceImpl implements MessageRecordService {

    @DubboReference
    private RemoteFaultMessageService remoteFaultMessageService;

    @Autowired
    private MarketingMsgServiceImpl marketingMsgService;
    @Autowired
    private SysMsgServiceImpl sysMsgService;

    @DubboReference
    private RemoteMessageService remoteMessageService;

    @DubboReference
    private RemoteAppUserService remoteAppUserService;

    /**
     * 消息列表-营销、系统、告警
     *
     * @return 被引用次数
     */
    @Override
    public PageResult<MessageRecordResultVo> getMessageRecord(MessageRecordSearchVo messageRecordVo) {
        PageResult<MessageRecordResultVo> resultVoPageResult = new PageResult<>();
        if (MessageTypeEnum.SYS_MSG.getValue().equals(messageRecordVo.getMessageType())) {
            //系统
            resultVoPageResult = sysMsgService.recordPage(messageRecordVo);
        } else if (MessageTypeEnum.MARKETING_MSG.getValue().equals(messageRecordVo.getMessageType())) {
            //营销
            resultVoPageResult = marketingMsgService.recordPage(messageRecordVo);
        } else if (MessageTypeEnum.DEVICE_MSG.getValue().equals(messageRecordVo.getMessageType())) {
            //设备消息
            FaultMessageRecordSearchDto faultMessageRecordSearchDto = ConvertUtil.convert(messageRecordVo, FaultMessageRecordSearchDto.class);
            faultMessageRecordSearchDto.setPushMethod(messageRecordVo.getPushTypeCode());
            PageResult<FaultMessageRecordVo> recordVoPageResult = remoteFaultMessageService.pageRecord(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), faultMessageRecordSearchDto));
            //需要一个转换
            List<FaultMessageRecordVo> list = recordVoPageResult.getList();
            List<MessageRecordResultVo> recordResultVos = Lists.newArrayList();
            list.forEach(a -> {
                MessageRecordResultVo convert = ConvertUtil.convert(a, MessageRecordResultVo.class);
                convert.setMessageType(MessageTypeEnum.DEVICE_MSG.getValue());
                recordResultVos.add(convert);
            });
            resultVoPageResult.setPages(recordVoPageResult.getPages());
            resultVoPageResult.setTotal(recordVoPageResult.getTotal());
            resultVoPageResult.setPageNum(recordVoPageResult.getPageNum());
            resultVoPageResult.setPageSize(recordVoPageResult.getPageSize());
            resultVoPageResult.setList(recordResultVos);
        }
        return resultVoPageResult;
    }


    /**
     * 消息列表-营销、系统、告警
     *
     * @return 被引用次数
     */
    @Override
    public List<MessageRecordResultVo> getMessageRecordList(MessageRecordSearchVo messageRecordVo) {
        if (MessageTypeEnum.SYS_MSG.getValue().equals(messageRecordVo.getMessageType())) {
            //系统
            return sysMsgService.recordList(messageRecordVo);
        } else if (MessageTypeEnum.MARKETING_MSG.getValue().equals(messageRecordVo.getMessageType())) {
            //营销
            return marketingMsgService.recordList(messageRecordVo);
        } else if (MessageTypeEnum.DEVICE_MSG.getValue().equals(messageRecordVo.getMessageType())) {
            //设备消息
            FaultMessageRecordSearchDto faultMessageRecordSearchDto = ConvertUtil.convert(messageRecordVo, FaultMessageRecordSearchDto.class);
            faultMessageRecordSearchDto.setPushMethod(messageRecordVo.getPushTypeCode());
            List<FaultMessageRecordVo> recordVoPageResult = remoteFaultMessageService.listRecord(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), faultMessageRecordSearchDto));
            //需要一个转换
            List<MessageRecordResultVo> recordResultVos = Lists.newArrayList();
            recordVoPageResult.forEach(a -> {
                MessageRecordResultVo convert = ConvertUtil.convert(a, MessageRecordResultVo.class);
                convert.setMessageType(MessageTypeEnum.DEVICE_MSG.getValue());
                recordResultVos.add(convert);
            });
            return recordResultVos;
        }
        return Lists.newArrayList();
    }

    /**
     * 消息推送记录
     *
     * @return
     */
    @Override
    public PageResult<MessageVo> getPushRecordPage(SearchMessageDto searchMessageDto) {
        PageResult<MessageVo> pageResult = new PageResult<>(searchMessageDto.getPageNum(), searchMessageDto.getPageSize());
        if (StringUtils.isNotEmpty(searchMessageDto.getEmail())) {
            List<AppUserVo> appUserVos = remoteAppUserService.listAppUserByEmailAndUserIds(searchMessageDto.getEmail(), null);
            if (CollectionUtil.isEmpty(appUserVos)) {
                return pageResult;
            }
            if (CollectionUtil.isNotEmpty(appUserVos) && StringUtils.isNotEmpty(searchMessageDto.getUserId())) {
                appUserVos = appUserVos.stream().filter(appUserVo -> appUserVo.getUserId().contains(searchMessageDto.getUserId())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(appUserVos)) {
                    return pageResult;
                }
                searchMessageDto.setUserId(null);
            }
            List<String> uerIds = appUserVos.stream().map(AppUserVo::getUserId).collect(Collectors.toList());
            searchMessageDto.setUserIds(uerIds);
        }
        PageResult<MessageVo> messagePage = remoteMessageService.getPushRecordPage(searchMessageDto);
        List<MessageVo> messageList = messagePage.getList();
        if (CollectionUtil.isNotEmpty(messageList)) {
            List<Long> userIds = messageList.stream().map(MessageVo::getUserId).collect(Collectors.toList());
            userIds.remove(null);
            List<AppUserVo> appUserVos = remoteAppUserService.listAppUserByEmailAndUserIds(null, userIds);
            Map<String, String> userVoMap = appUserVos.stream().collect(Collectors.toMap(AppUserVo::getUserId, AppUserVo::getEmail));
            messageList.forEach(m -> {
                m.setEmail(userVoMap.get(m.getUserId() != null ? m.getUserId().toString() : null));
                m.setMessageType(searchMessageDto.getMessageType());
            });
        }
        //获取userId 获取 email
        return messagePage;
    }

    /**
     * 消息推送记录-不分页
     *
     * @return
     */
    @SuppressWarnings("Duplicates")
    @Override
    public List<MessageVo> getPushRecordList(SearchMessageDto searchMessageDto) {
        if (StringUtils.isNotEmpty(searchMessageDto.getEmail())) {
            List<AppUserVo> appUserVos = remoteAppUserService.listAppUserByEmailAndUserIds(searchMessageDto.getEmail(), null);
            if (CollectionUtil.isEmpty(appUserVos)) {
                return new ArrayList<>();
            }
            if (CollectionUtil.isNotEmpty(appUserVos) && StringUtils.isNotEmpty(searchMessageDto.getUserId())) {
                appUserVos = appUserVos.stream().filter(appUserVo -> appUserVo.getUserId().contains(searchMessageDto.getUserId())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(appUserVos)) {
                    return new ArrayList<>();
                }
                searchMessageDto.setUserId(null);
            }
            List<String> uerIds = appUserVos.stream().map(AppUserVo::getUserId).collect(Collectors.toList());
            searchMessageDto.setUserIds(uerIds);
        }
        searchMessageDto.setRequestType("1");
        List<MessageVo> messageList = remoteMessageService.getPushRecordList(searchMessageDto);
        if (CollectionUtil.isNotEmpty(messageList)) {
            List<Long> userIds = messageList.stream().map(MessageVo::getUserId).collect(Collectors.toList());
            userIds.remove(null);
            List<AppUserVo> appUserVos = remoteAppUserService.listAppUserByEmailAndUserIds(null, userIds);
            Map<String, String> userVoMap = appUserVos.stream().collect(Collectors.toMap(AppUserVo::getUserId, AppUserVo::getEmail));
            messageList.forEach(m -> {
                m.setEmail(userVoMap.get(m.getUserId() != null ? m.getUserId().toString() : null));
                m.setMessageType(searchMessageDto.getMessageType());
            });
        }
        return messageList;
    }


}
