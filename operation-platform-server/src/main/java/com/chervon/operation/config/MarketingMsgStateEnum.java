package com.chervon.operation.config;

import com.chervon.operation.api.exception.OperationException;
import lombok.Getter;

import static com.chervon.operation.api.exception.OperationErrorCode.OPERATION_APP_MARKETING_MSG_OPERATE_ILLEGAL;
import static com.chervon.operation.config.MarketingMsgOperationEnum.*;

/**
 * <AUTHOR>
 * @date 2022/7/23 16:25
 */
@Getter
public enum MarketingMsgStateEnum {

    /**
     * 待发布
     */
    WILL_RELEASE("will_release", "待发布", new MarketingMsgOperationEnum[]{VIEW, UPDATE, APPLY_RELEASE, DELETE, COPY}),
    /**
     * 测试验证中
     */
    TEST_CHECK_ING("test_check_ing", "测试验证中", new MarketingMsgOperationEnum[]{VIEW, CANCEL_APPLY_RELEASE, COPY, ENSURE_TEST, REFUSE_TEST}),
    /**
     * 测试被驳回
     */
    TEST_REFUSED("test_refused", "测试被驳回", new MarketingMsgOperationEnum[]{VIEW, UPDATE, VIEW_REFUSE_TEST_REASON, APPLY_RELEASE, DELETE, COPY}),
    /**
     * 发布审核中
     */
    RELEASE_VERIFY_ING("release_verify_ing", "发布审核中", new MarketingMsgOperationEnum[]{VIEW, CANCEL_APPLY_RELEASE, COPY, ENSURE_RELEASE, REFUSE_RELEASE}),
    /**
     * 发布被驳回
     */
    RELEASE_REFUSED("release_refused", "发布被驳回", new MarketingMsgOperationEnum[]{VIEW, UPDATE, VIEW_REFUSE_RELEASE_REASON, APPLY_RELEASE, DELETE, COPY}),
    /**
     * 已发布
     */
    RELEASED("released", "已发布", new MarketingMsgOperationEnum[]{VIEW, APPLY_STOP_RELEASE, COPY}),
    /**
     * 停止发布审核中
     */
    STOP_RELEASE_VERIFY_ING("stop_release_verify_ing", "停止发布审核中", new MarketingMsgOperationEnum[]{VIEW, CANCEL_APPLY_STOP_RELEASE, COPY, ENSURE_STOP_RELEASE, REFUSE_STOP_RELEASE}),
    /**
     * 停止发布被驳回
     */
    STOP_RELEASE_REFUSED("stop_release_refused", "停止发布被驳回", new MarketingMsgOperationEnum[]{VIEW, VIEW_REFUSE_STOP_RELEASE_REASON, APPLY_STOP_RELEASE, COPY}),
    /**
     * 停止发布
     */
    STOP_RELEASED("stop_released", "停止发布", new MarketingMsgOperationEnum[]{VIEW, UPDATE, APPLY_RELEASE, COPY}),
    /**
     * 已结束
     */
    FINISHED("finished", "已结束", new MarketingMsgOperationEnum[]{VIEW, UPDATE, APPLY_RELEASE, COPY}),
    ;

    /**
     * code
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;

    /**
     * 操作集合
     */
    private final MarketingMsgOperationEnum[] operations;

    MarketingMsgStateEnum(String code, String description, MarketingMsgOperationEnum[] operations) {
        this.code = code;
        this.description = description;
        this.operations = operations;
    }

    public static MarketingMsgStateEnum getFromCode(String code) {
        for (MarketingMsgStateEnum e : MarketingMsgStateEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_OPERATE_ILLEGAL, code);
    }

}
