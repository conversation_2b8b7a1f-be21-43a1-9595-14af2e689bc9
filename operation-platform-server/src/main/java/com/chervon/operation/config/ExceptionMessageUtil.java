package com.chervon.operation.config;

import cn.hutool.extra.spring.SpringUtil;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.api.exception.OperationException;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.exception.TechnologyException;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/17 11:18
 */
public class ExceptionMessageUtil {

    private static final RemoteMultiLanguageService remoteMultiLanguageService = SpringUtil.getBean("remoteMultiLanguageService");

    public static OperationException getException(OperationErrorCode errorCode, Object... args) {
        OperationException exception = new OperationException(errorCode, args);
        String code = errorCode.getCode();
        if (code != null) {
            String language = remoteMultiLanguageService.simpleFindMultiLanguageByCode(code);
            if (language != null) {
                exception.setTempMessage(language);
            }
        }
        return exception;
    }

    public static TechnologyException getException(TechnologyErrorCode errorCode, Object... args) {
        TechnologyException exception = new TechnologyException(errorCode, args);
        String code = errorCode.getCode();
        if (code != null) {
            String language = remoteMultiLanguageService.simpleFindMultiLanguageByCode(code);
            if (language != null) {
                exception.setTempMessage(language);
            }
        }
        return exception;
    }

}
