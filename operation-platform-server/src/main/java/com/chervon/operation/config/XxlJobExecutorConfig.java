package com.chervon.operation.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 运营中心执行器注册配置
 * <AUTHOR>
 * @since 2022-12-16 15:28
 */
@Slf4j
@Configuration
public class XxlJobExecutorConfig {
    @Value("${xxl.job.admin.addresses}")
    private String adminAddresses;
    @Value("${spring.application.name}")
    private String appName;

    @Bean
    public XxlJobSpringExecutor xxlJobSpringExecutor() {
        log.info(">>>>>>>>>>> xxl-job config init.");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(appName);
        return xxlJobSpringExecutor;
    }
}

