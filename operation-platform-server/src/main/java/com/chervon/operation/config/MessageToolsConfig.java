package com.chervon.operation.config;

import com.chervon.common.i18n.util.MessageTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * @Author：flynn.wang
 * @Date：2024/5/22 19:03
 */
@Component
public class MessageToolsConfig {

    @Autowired
    MessageTools messageTools;

    @Bean(name = "messageTools")
    public MessageTools get() {
        return messageTools;
    }
}
