package com.chervon.operation.config;

import cn.hutool.extra.spring.SpringUtil;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/17 11:18
 */
public class MultiLanguageUtil {

    private static final RemoteMultiLanguageService remoteMultiLanguageService = SpringUtil.getBean("remoteMultiLanguageService");

    public static String getByLangCode(String langCode) {
        return remoteMultiLanguageService.simpleFindMultiLanguageByCode(langCode);
    }

    public static Map<String, String> getByLangCodes(Collection<String> langCodes) {
        return remoteMultiLanguageService.simpleFindMultiLanguageByCodes(langCodes);
    }

    public static String getByLangCode(String langCode, String lang) {
        return remoteMultiLanguageService.simpleFindMultiLanguageByCode(langCode, lang);
    }

    public static Map<String, String> getByLangCodes(Collection<String> langCodes, String lang) {
        return remoteMultiLanguageService.simpleFindMultiLanguageByCodes(langCodes, lang);
    }
}
