package com.chervon.operation.rpc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chervon.operation.api.RemoteAppVersionService;
import com.chervon.operation.api.vo.AppVersionVo;
import com.chervon.operation.domain.dataobject.AndroidVersion;
import com.chervon.operation.service.AndroidVersionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/11/21 17:54
 */
@DubboService
@Service
@Slf4j
public class RemoteAppVersionServiceImpl implements RemoteAppVersionService {

    @Autowired
    private AndroidVersionService androidVersionService;

    @Override
    public AppVersionVo latest(int businessType) {
        AppVersionVo res = new AppVersionVo();
        QueryWrapper<AndroidVersion> queryWrapper = new QueryWrapper<AndroidVersion>()
                .eq("business_type", businessType)
                .orderByDesc("CONVERT(substring_index(substring_index(substring_index(version,'-',1),'.',1),'.',-1),SIGNED)")
                .orderByDesc("CONVERT(substring_index(substring_index(substring_index(version,'-',1),'.',2),'.',-1),SIGNED)")
                .orderByDesc("CONVERT(substring_index(substring_index(substring_index(version,'-',1),'.',3),'.',-1),SIGNED)")
                .last("limit 1");
        AndroidVersion one = androidVersionService.getOne(queryWrapper);
        if (one == null) {
            return res;
        }
        res.setVersion(one.getVersion());
        res.setUpdateContent(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(one.getUpdateContentLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        return res;
    }
}
