package com.chervon.operation.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.operation.api.RemoteCategoryService;
import com.chervon.operation.api.vo.CategoryLevelBo;
import com.chervon.operation.api.vo.CategoryVo;
import com.chervon.operation.domain.dataobject.Category;
import com.chervon.operation.service.CategoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-07-29
 */
@Slf4j
@DubboService
public class RemoteCategoryServiceImpl implements RemoteCategoryService {

    @Autowired
    private CategoryService categoryService;

    @Override
    public List<CategoryVo> allList(BaseRemoteReqDto<String> dto) {
        LocaleContextHolder.setLocale(new Locale(dto.getLanguage()));
        return categoryService.allList();
    }

    @Override
    public Map<Long, CategoryVo> getMyMap(BaseRemoteReqDto<Map<Long, Long>> categoryIds) {
        LocaleContextHolder.setLocale(new Locale(categoryIds.getLanguage()));
        return categoryService.getMyMap(categoryIds.getReq());
    }

    @Override
    public CategoryVo getDetail(BaseRemoteReqDto<Long> id) {
        LocaleContextHolder.setLocale(new Locale(id.getLanguage()));
        return categoryService.getDetail(id.getReq());
    }

    @Override
    public void checkExistByCategoryId(Long categoryId) {
        categoryService.checkExistByCategoryId(categoryId);
    }

    @Override
    public List<CategoryVo> listByIds(BaseRemoteReqDto<List<Long>> categoryIds) {
        LocaleContextHolder.setLocale(new Locale(categoryIds.getLanguage()));
        List<Category> list = categoryService.list(new LambdaQueryWrapper<Category>().in(
                !CollectionUtils.isEmpty(categoryIds.getReq()), Category::getId, categoryIds.getReq()).select(Category::getId, Category::getCategoryName));
        return list.stream().filter(e-> StringUtils.isNotBlank(e.getCategoryName())).map(e -> {
            CategoryVo vo = new CategoryVo();
            vo.setId(e.getId());
            vo.setCategoryLangId(e.getCategoryName());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public CategoryLevelBo getCategoryLevel(Long categoryId) {
        Category category = categoryService.getById(categoryId);
        if (category == null) {
            return null;
        }
        return ConvertUtil.convert(category, CategoryLevelBo.class);
    }
}
