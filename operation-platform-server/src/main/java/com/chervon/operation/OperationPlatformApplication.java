package com.chervon.operation;

import com.chervon.common.satoken.config.SaTokenConfiguration;
import com.chervon.common.sso.SaTokenConfigure;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(exclude = {SaTokenConfigure.class, SaTokenConfiguration.class},
        scanBasePackages = {"com.chervon.common.log.aspect","com.chervon.operation"})
@EnableDubbo
public class OperationPlatformApplication {

    public static void main(String[] args) {
        SpringApplication.run(OperationPlatformApplication.class, args);
    }

}
