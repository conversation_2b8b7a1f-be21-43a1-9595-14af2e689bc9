package com.chervon.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.operation.domain.dataobject.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 品类信息表
 * 
 * <AUTHOR>
 *
 * @date 2022-04-25 19:00:12
 */
@Mapper
public interface CategoryMapper extends BaseMapper<Category> {

    /**
     * 获取所有的品类Id
     * @return 品类Id列表
     */
    List<Long> getAllCategoryIds();

    /**
     * get AppShow max order
     * @return
     */
    Integer getAppShowMaxOrder();
}
