package com.chervon.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.operation.domain.dataobject.CommonOperationGuidance;
import com.chervon.operation.domain.dto.operationguidance.common.CommonOperationGuidanceManagePageDto;
import com.chervon.operation.domain.dto.operationguidance.common.CommonOperationGuidanceReleasePageDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/24 16:03
 */
@Mapper
public interface CommonOperationGuidanceMapper extends BaseMapper<CommonOperationGuidance> {

    /**
     * 分页查询
     *
     * @param page        容器
     * @param search      查询条件
     * @param nameLangIds 名称多语言id集合
     * @return 分页数据
     */
    IPage<CommonOperationGuidance> selectManagePage(IPage<CommonOperationGuidance> page, @Param("search") CommonOperationGuidanceManagePageDto search,
                                                    @Param("nameLangIds") List<Long> nameLangIds);

    /**
     * 列表查询
     *
     * @param search      查询条件
     * @param nameLangIds 名称多语言id集合
     * @return 列表数据
     */
    List<CommonOperationGuidance> selectManageList(@Param("search") CommonOperationGuidanceManagePageDto search,
                                                   @Param("nameLangIds") List<Long> nameLangIds);

    /**
     * 分页查询
     *
     * @param page        容器
     * @param search      查询条件
     * @param nameLangIds 名称多语言id集合
     * @return 分页数据
     */
    IPage<CommonOperationGuidance> selectReleasePage(IPage<CommonOperationGuidance> page, @Param("search") CommonOperationGuidanceReleasePageDto search,
                                                     @Param("nameLangIds") List<Long> nameLangIds);

    /**
     * 根据分组名称查询通用操作指导id集合
     *
     * @param groupName 分组名称
     * @return 通用指导id集合
     */
    List<Long> selectListIdByGroupName(@Param("groupName") String groupName);
}
