package com.chervon.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.operation.api.dto.MessagePushResultCountDto;
import com.chervon.operation.domain.dataobject.SysMsg;
import com.chervon.operation.domain.dto.app.SysMsgManagePageDto;
import com.chervon.operation.domain.dto.app.SysMsgReleasePageDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/20 18:52
 */
@Mapper
public interface SysMsgMapper extends BaseMapper<SysMsg> {

    /**
     * 系统消息管理分页查询
     *
     * @param page         分页容器
     * @param search       查询条件
     * @param titleLangIds 标题多语言id集合
     * @return 分页数据
     */
    IPage<SysMsg> selectManagePage(IPage<SysMsg> page, @Param("search") SysMsgManagePageDto search, @Param("titleLangIds") List<Long> titleLangIds);

    /**
     * 系统消息发布管理分页查询
     *
     * @param page         分页容器
     * @param search       查询条件
     * @param titleLangIds 标题多语言id集合
     * @return 分页数据
     */
    IPage<SysMsg> selectReleasePage(IPage<SysMsg> page, @Param("search") SysMsgReleasePageDto search, @Param("titleLangIds") List<Long> titleLangIds);


    /**
     * 系统消息发布管理
     *
     * @param search       查询条件
     * @param titleLangIds 标题多语言id集合
     * @return 分页数据
     */
    List<SysMsg> selectReleaseList(@Param("search") SysMsgReleasePageDto search, @Param("titleLangIds") List<Long> titleLangIds);

    /**
     * 根据分组名称查询系统消息id
     *
     * @param groupName 分组名称
     * @return 系统消息id集合
     */
    List<Long> selectListIdByGroupName(@Param("groupName") String groupName);

    /**
     * 消息统计信息变更
     *
     */
    int updateMsgCount(@Param("messagePushResultCountDto") MessagePushResultCountDto messagePushResultCountDto);

}
