package com.chervon.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.operation.domain.dataobject.GroupCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 设备表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
public interface GroupConditionMapper extends BaseMapper<GroupCondition> {

    /**
     * 根据分组id获取分组条件
     * <AUTHOR>
     * @date 18:27 2022/7/28
     * @param groupId:
     * @return java.util.List<com.chervon.technology.domain.entity.GroupCondition>
     **/
    List<GroupCondition> listByGroupId(Long groupId);

    /**
     * 根据分组名称获取分组条件列表
     * <AUTHOR>
     * @date 11:43 2022/8/31
     * @param groupName: 分组名称
     * @return java.util.List<com.chervon.operation.domain.dataobject.GroupCondition>
     **/
    List<GroupCondition> listByGroupName(String groupName);

    /**
     * 根据分组名称获取分组条件列表
     * <AUTHOR>
     * @param groupNames: 分组名称s
     * @return java.util.List<com.chervon.operation.domain.dataobject.GroupCondition>
     **/
    List<GroupCondition> listByGroupNames(@Param("groupNames") List<String> groupNames);
}
