package com.chervon.operation.controller;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.operation.domain.dto.faq.parts.PartsFaqDto;
import com.chervon.operation.domain.dto.faq.parts.PartsFaqListDto;
import com.chervon.operation.domain.dto.link.parts.PartsLinkDto;
import com.chervon.operation.domain.dto.manual.parts.PartsManualDto;
import com.chervon.operation.domain.dto.operationguidance.parts.PartsOperationGuidanceDto;
import com.chervon.operation.domain.dto.parts.*;
import com.chervon.operation.domain.vo.faq.parts.PartsFaqVo;
import com.chervon.operation.domain.vo.link.parts.PartsLinkVo;
import com.chervon.operation.domain.vo.manual.parts.PartsManualVo;
import com.chervon.operation.domain.vo.operationguidance.parts.PartsOperationGuidanceVo;
import com.chervon.operation.domain.vo.parts.PartsExcel;
import com.chervon.operation.domain.vo.parts.PartsPageVo;
import com.chervon.operation.domain.vo.parts.PartsVo;
import com.chervon.operation.service.PartsService;
import com.chervon.technology.api.vo.CommonProductVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.chervon.operation.service.PartsService.*;

/**
 * <AUTHOR>
 * @date 2022/11/21 21:20
 */
@Api(tags = "配件相关")
@RestController
@Slf4j
@RequestMapping("/parts")
public class PartsController {

    private final PartsService partsService;

    public PartsController(PartsService partsService) {
        this.partsService = partsService;
    }

    @ApiOperation("配件分页")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<PartsPageVo> page(@RequestBody PartsPageDto req) {
        return partsService.page(req);
    }

    @ApiOperation("新增配件")
    @PostMapping("add")
    @Log(businessType = BusinessType.INSERT)
    public void add(@RequestBody PartsDto req) {
        partsService.add(req);
    }

    @ApiOperation("编辑配件")
    @PostMapping("edit")
    @Log(businessType = BusinessType.EDIT)
    public void edit(@RequestBody PartsDto req)throws UnsupportedEncodingException {
        if (req != null && req.getUploadIconName() != null ) {
            req.setUploadIconName(URLDecoder.decode(req.getUploadIconName(),  StandardCharsets.UTF_8.name()));
        }
        partsService.edit(req);
    }

    @ApiOperation("详情配件--传入partsId")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public PartsVo detail(@RequestBody SingleInfoReq<Long> req) {
        return partsService.detail(req.getReq());
    }

    @ApiOperation("删除配件--传入partsId")
    @PostMapping("delete")
    @Log(businessType = BusinessType.DELETE)
    public void delete(@RequestBody SingleInfoReq<Long> req) {
        partsService.delete(req.getReq());
    }

    @ApiOperation("配件关联产品分页")
    @PostMapping("product/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<CommonProductVo> productPage(@RequestBody PartsProductPageDto req) {
        return partsService.productPage(req);
    }

    @ApiOperation(value = "模板下载")
    @PostMapping("template")
    @Log(businessType = BusinessType.DOWNLOAD)
    public void template(HttpServletResponse response) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("parts_template", StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), PartsRead.class).autoCloseStream(Boolean.FALSE).sheet("sheet1")
                    .doWrite(new ArrayList<PartsRead>() {{
                        add(new PartsRead());
                    }});
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载模板失败")));
        }
    }

    @ApiOperation(value = "导入")
    @PostMapping("import")
    @Log(businessType = BusinessType.IMPORT)
    public List<String> importParts(@RequestParam(value = "file") MultipartFile file) {
        return partsService.importParts(file);
    }

    @ApiOperation(value = "导出")
    @PostMapping("export")
    @Log(businessType = BusinessType.EXPORT)
    public void export(@RequestBody PartsPageDto req, HttpServletResponse response) throws IOException {
        try {
            List<PartsExcel> data = partsService.listData(req);
            if (CollectionUtils.isEmpty(data)) {
                data = new ArrayList<>();
                data.add(new PartsExcel());
            }
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.toString());
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            //进行下载
            String fileName = URLEncoder.encode("Parts-" + new SimpleDateFormat("yyyyMMdd").format(new Date()), StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            // 加上UTF-8文件的标识字符
            response.getWriter().write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
            CsvWriter csvWriter = CsvUtil.getWriter(response.getWriter());
            csvWriter.writeBeans(data);
            csvWriter.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }

    @ApiOperation("编辑短描述")
    @PostMapping("edit/short")
    @Log(businessType = BusinessType.EDIT)
    public void editShort(@RequestBody PartsEditDto req) {
        req.setKey(SHORT_DESCRIPTION);
            partsService.edit(req);
    }


    @ApiOperation("编辑产品长描述")
    @PostMapping("edit/long")
    @Log(businessType = BusinessType.EDIT)
    public void editLong(@RequestBody PartsEditDto req) {
        req.setKey(LONG_DESCRIPTION);
        partsService.edit(req);
    }

    @ApiOperation("编辑技术规格")
    @PostMapping("edit/tech")
    @Log(businessType = BusinessType.EDIT)
    public void editTech(@RequestBody PartsEditDto req) {
        req.setKey(TECHNICAL_SPECIFICATION);
        partsService.edit(req);
    }

    ////////////////////用户手册////////////////////////

    @ApiOperation("编辑用户手册")
    @PostMapping("manual/edit")
    @Log(businessType = BusinessType.EDIT)
    public void manualEdit(@RequestBody PartsManualDto req) {
        partsService.manualEdit(req);
    }

    @ApiOperation("删除用户手册--传入partsManualId")
    @PostMapping("manual/delete")
    @Log(businessType = BusinessType.DELETE)
    public void manualDelete(@RequestBody SingleInfoReq<Long> req) {
        partsService.manualDelete(req.getReq());
    }

    @ApiOperation("下载用户手册--传入partsManualId")
    @PostMapping("manual/download")
    @Log(businessType = BusinessType.DOWNLOAD)
    public String manualDownload(@RequestBody SingleInfoReq<Long> req) {
        return partsService.manualDownload(req.getReq());
    }

    @ApiOperation("添加用户手册")
    @PostMapping("manual/add")
    @Log(businessType = BusinessType.INSERT)
    public void manualAdd(@RequestBody PartsManualDto req) {
        partsService.manualAdd(req);
    }

    @ApiOperation("详情用户手册--传入partsManualId")
    @PostMapping("manual/detail")
    @Log(businessType = BusinessType.VIEW)
    public PartsManualVo manualDetail(@RequestBody SingleInfoReq<Long> req) {
        return partsService.manualDetail(req.getReq());
    }

    @ApiOperation("列表用户手册-用于新增、编辑、删除用户手册后，刷新列表-传partsId")
    @PostMapping("manual/list")
    @Log(businessType = BusinessType.VIEW)
    public List<PartsManualVo> manualList(@RequestBody SingleInfoReq<Long> req) {
        return partsService.manualList(req.getReq());
    }

    ////////////////////操作指导////////////////////////

    @ApiOperation("编辑操作指导")
    @PostMapping("operationGuidance/edit")
    @Log(businessType = BusinessType.EDIT)
    public void operationGuidanceEdit(@RequestBody PartsOperationGuidanceDto req) {
        partsService.operationGuidanceEdit(req);
    }

    @ApiOperation("删除操作指导--传入partsOperationGuidanceId")
    @PostMapping("operationGuidance/delete")
    @Log(businessType = BusinessType.DELETE)
    public void operationGuidanceDelete(@RequestBody SingleInfoReq<Long> req) {
        partsService.operationGuidanceDelete(req.getReq());
    }

    @ApiOperation("下载操作指导--传入partsOperationGuidanceId")
    @PostMapping("operationGuidance/download")
    @Log(businessType = BusinessType.DOWNLOAD)
    public String operationGuidanceDownload(@RequestBody SingleInfoReq<Long> req) {
        return partsService.operationGuidanceDownload(req.getReq());
    }

    @ApiOperation("添加操作指导")
    @PostMapping("operationGuidance/add")
    @Log(businessType = BusinessType.INSERT)
    public void operationGuidanceAdd(@RequestBody PartsOperationGuidanceDto req) {
        partsService.operationGuidanceAdd(req);
    }

    @ApiOperation("列表操作指导-用于新增、编辑、删除、排序操作指导后，刷新列表--传partsId")
    @PostMapping("operationGuidance/list")
    @Log(businessType = BusinessType.VIEW)
    public List<PartsOperationGuidanceVo> operationGuidanceList(@RequestBody SingleInfoReq<Long> req) {
        return partsService.operationGuidanceList(req.getReq());
    }

    @ApiOperation("详情操作指导--传入partsOperationGuidanceId")
    @PostMapping("operationGuidance/detail")
    @Log(businessType = BusinessType.VIEW)
    public PartsOperationGuidanceVo operationGuidanceDetail(@RequestBody SingleInfoReq<Long> req) {
        return partsService.operationGuidanceDetail(req.getReq());
    }

    @ApiOperation("排序操作指导--传入partsOperationGuidanceId集合")
    @PostMapping("operationGuidance/order")
    @Log(businessType = BusinessType.VIEW)
    public void operationGuidanceOrder(@RequestBody List<Long> req) {
        partsService.operationGuidanceOrder(req);
    }

    ////////////////////faq////////////////////////

    @ApiOperation("编辑faq")
    @PostMapping("faq/edit")
    @Log(businessType = BusinessType.EDIT)
    public void faqEdit(@RequestBody PartsFaqDto req) {
        partsService.faqEdit(req);
    }

    @ApiOperation("删除faq--传入partsFaqId")
    @PostMapping("faq/delete")
    @Log(businessType = BusinessType.DELETE)
    public void faqDelete(@RequestBody SingleInfoReq<Long> req) {
        partsService.faqDelete(req.getReq());
    }

    @ApiOperation("添加faq")
    @PostMapping("faq/add")
    @Log(businessType = BusinessType.INSERT)
    public void faqAdd(@RequestBody PartsFaqDto req) {
        partsService.faqAdd(req);
    }

    @ApiOperation("列表faq-用于新增、编辑、删除、排序faq后，刷新列表-传partsId")
    @PostMapping("faq/list")
    @Log(businessType = BusinessType.VIEW)
    public List<PartsFaqVo> faqList(@RequestBody PartsFaqListDto req) {
        return partsService.faqList(req);
    }

    @ApiOperation("详情faq--传入partsFaqId")
    @PostMapping("faq/detail")
    @Log(businessType = BusinessType.VIEW)
    public PartsFaqVo faqDetail(@RequestBody SingleInfoReq<Long> req) {
        return partsService.faqDetail(req.getReq());
    }

    @ApiOperation("排序faq--传入partsOperationGuidanceId集合")
    @PostMapping("faq/order")
    @Log(businessType = BusinessType.VIEW)
    public void faqOrder(@RequestBody List<Long> req) {
        partsService.faqOrder(req);
    }

    ///////////////////////购买链接//////////////////////////

    @ApiOperation("编辑购买链接")
    @PostMapping("link/edit")
    @Log(businessType = BusinessType.EDIT)
    public void linkEdit(@RequestBody PartsLinkDto req) {
        partsService.linkEdit(req);
    }

    @ApiOperation("删除购买链接--传入partsLinkId")
    @PostMapping("link/delete")
    @Log(businessType = BusinessType.DELETE)
    public void linkDelete(@RequestBody SingleInfoReq<Long> req) {
        partsService.linkDelete(req.getReq());
    }

    @ApiOperation("添加购买链接")
    @PostMapping("link/add")
    @Log(businessType = BusinessType.INSERT)
    public void linkAdd(@RequestBody PartsLinkDto req) {
        partsService.linkAdd(req);
    }

    @ApiOperation("列表购买链接-用于新增、编辑、删除、排序购买链接后，刷新列表-传partsId")
    @PostMapping("link/list")
    @Log(businessType = BusinessType.VIEW)
    public List<PartsLinkVo> linkList(@RequestBody SingleInfoReq<Long> req) {
        return partsService.linkList(req.getReq());
    }

    @ApiOperation("详情链接--传入partsLinkId")
    @PostMapping("link/detail")
    @Log(businessType = BusinessType.VIEW)
    public PartsLinkVo linkDetail(@RequestBody SingleInfoReq<Long> req) {
        return partsService.linkDetail(req.getReq());
    }

}
