package com.chervon.operation.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.vo.BrandVo;
import com.chervon.operation.api.vo.CategoryVo;
import com.chervon.operation.domain.bo.DictBo;
import com.chervon.operation.domain.bo.DictNodeBo;
import com.chervon.operation.domain.vo.ota.JobReleaseExportVo;
import com.chervon.operation.service.BrandService;
import com.chervon.operation.service.CategoryService;
import com.chervon.operation.service.DictService;
import com.chervon.operation.util.ExportUtil;
import com.chervon.technology.api.RemoteOtaJobService;
import com.chervon.technology.api.dto.ota.*;
import com.chervon.technology.api.enums.OtaJobReleaseOperation;
import com.chervon.technology.api.vo.ota.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 升级任务发布管理
 *
 * <AUTHOR>
 * @date 18:20 2022/8/8
 **/
@Api(tags = "升级任务发布管理")
@RestController
@RequestMapping("/ota/job")
@Slf4j
public class OtaJobController {

    @DubboReference
    RemoteOtaJobService remoteOtaJobService;

    @Autowired
    private BrandService brandService;

    @Autowired
    CategoryService categoryService;

    @DubboReference
    private RemoteMultiLanguageService languageService;

    @Autowired
    private AwsProperties awsProperties;

    @Autowired
    private DictService dictService;

    private static final String OTA_JOB_RELEASE_STATUS = "otaJobReleaseStatus";

    /**
     * 分页获取任务配置列表
     *
     * @param jobConfigListDto 搜索条件
     * @return 搜索结果
     */
    @ApiOperation("分页获取任务配置列表")
    @RequestMapping(value = "/list/config", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public R<PageResult<JobConfigListVo>> listConfig(@Validated @RequestBody JobConfigListDto jobConfigListDto) {
        PageResult<JobConfigListVo> pageResult = remoteOtaJobService.pageConfig(jobConfigListDto);
        return R.ok(pageResult);
    }

    /**
     * 分页获取任务发布列表
     *
     * @param jobReleaseListDto 搜索条件
     * @return 搜索结果
     */
    @ApiOperation("分页获取任务发布列表")
    @RequestMapping(value = "/list/release", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public R<PageResult<JobReleaseListVo>> listRelease(@Validated @RequestBody JobReleaseListDto jobReleaseListDto) {
        PageResult<JobReleaseListVo> result = getJobReleaseListVoPageResult(jobReleaseListDto);
        return R.ok(result);
    }

    @NotNull
    private PageResult<JobReleaseListVo> getJobReleaseListVoPageResult(
            JobReleaseListDto jobReleaseListDto) {
        PageResult<JobReleaseListVo> result = remoteOtaJobService.pageRelease(
                jobReleaseListDto);
        List<JobReleaseListVo> list = result.getList();
        List<Long> categoryIds = new ArrayList<>();
        List<Long> brandIds = new ArrayList<>();
        for (JobReleaseListVo jobReleaseListVo : list) {
            try {
                categoryIds.add(Long.valueOf(jobReleaseListVo.getCategoryName()));
                brandIds.add(Long.valueOf(jobReleaseListVo.getBrandName()));
            } catch (Exception ex) {
                log.info("处理品牌品类数据异常：" + ex.getMessage());
            }
        }
        Map<Long, CategoryVo> categoryMap = categoryService.getMapByIds(categoryIds);
        Map<Long, BrandVo> brandMap = brandService.getMapByIds(brandIds);
        for (JobReleaseListVo vo : list) {
            try {
                String categoryName =
                        categoryMap.get(Long.valueOf(vo.getCategoryName())).getCategoryName()
                                .getMessage();
                vo.setCategoryName(categoryName);
                String brandName =
                        brandMap.get(Long.valueOf(vo.getBrandName())).getBrandName()
                                .getMessage();
                vo.setBrandName(brandName);
            } catch (Exception exception) {
                log.info("处理品牌品类数据异常：" + exception.getMessage());
            }
        }
        return result;
    }

    /**
     * 导出任务发布列表
     *
     * @param jobReleaseListDto 搜索条件
     * @return 搜索结果
     */
    @ApiOperation("导出任务发布列表")
    @Log(businessType = BusinessType.EXPORT)
    @RequestMapping(value = "/export/release", method = RequestMethod.POST)
    public void exportRelease(@Validated @RequestBody JobReleaseListDto jobReleaseListDto,
                              HttpServletResponse response) throws IOException {
        List<JobReleaseExportVo> list = getJobReleaseExcelList(jobReleaseListDto);
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName =
                URLEncoder.encode("ReleaseList-" + new SimpleDateFormat("yyyyMMdd").format(new Date()),
                        "UTF-8").replaceAll("\\+", "%20");
        ExportUtil.exportCsv(response, list, fileName);
    }

    private List<JobReleaseExportVo> getJobReleaseExcelList(
            JobReleaseListDto jobReleaseListDto) {
        List<JobReleaseListVo> list = remoteOtaJobService.listRelease(
                jobReleaseListDto);
        List<JobReleaseExportVo> res = new ArrayList<>();
        if (CollectionUtil.isEmpty(list)) {
            res.add(new JobReleaseExportVo());
            return res;
        }
        List<Long> categoryIds = list.stream().filter(e -> {
            try {
                Long.valueOf(e.getCategoryName());
                return true;
            } catch (Exception ex) {
                log.info("处理品类数据异常：" + ex.getMessage());
                return false;
            }
        }).map(e -> Long.parseLong(e.getCategoryName())).distinct().collect(Collectors.toList());
        List<Long> brandIds = list.stream().filter(e -> {
            try {
                Long.valueOf(e.getBrandName());
                return true;
            } catch (Exception ex) {
                log.info("处理品牌数据异常：" + ex.getMessage());
                return false;
            }
        }).map(e -> Long.parseLong(e.getBrandName())).distinct().collect(Collectors.toList());
        Map<Long, CategoryVo> categoryMap = categoryService.getMapByIds(categoryIds);
        Map<Long, BrandVo> brandMap = brandService.getMapByIds(brandIds);
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList(OTA_JOB_RELEASE_STATUS));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        return list.stream().map(e -> {
            JobReleaseExportVo excel = new JobReleaseExportVo();
            BeanUtils.copyProperties(e, excel);
            try {
                excel.setCategoryName(categoryMap.get(Long.valueOf(e.getCategoryName())).getCategoryName().getMessage());
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            try {
                excel.setBrandName(brandMap.get(Long.valueOf(e.getBrandName())).getBrandName().getMessage());
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            excel.setApplyTime(DateTimeZoneUtil.format(e.getApplyTime(), jobReleaseListDto.getZone()));
            excel.setEnsuredTime(DateTimeZoneUtil.format(e.getEnsuredTime(), jobReleaseListDto.getZone()));
            // 设置状态
            excel.setReleaseStatus(collect.get(OTA_JOB_RELEASE_STATUS).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getReleaseStatus().getValue()))
                    .findFirst()
                    .orElse(new DictNodeBo()).getDescription());
            return excel;
        }).collect(Collectors.toList());
    }

    /**
     * 获取任务详情
     *
     * @param jobId: 任务id
     * @return com.chervon.common.core.domain.R
     * <AUTHOR>
     * @date 17:30 2022/7/29
     **/
    @ApiOperation("获取任务详情")
    @PostMapping(value = "/detail")
    @Log(businessType = BusinessType.VIEW)
    public R<JobDetailVo> getDetail(@Validated @RequestBody SingleInfoReq<Long> jobId) {
        JobDetailVo detail = remoteOtaJobService.getDetail(jobId.getReq(), LocaleContextHolder.getLocale().getLanguage());
//        if (StringUtils.isNotEmpty(detail.getCategoryName())) {
//            Long categoryId = Long.valueOf(detail.getCategoryName());
//            Map<Long, CategoryVo> categoryMap = categoryService.getMapByIds(Arrays.asList(categoryId));
//            if (CollectionUtil.isNotEmpty(categoryMap)) {
//                detail.setCategoryName(categoryMap.get(categoryId).getCategoryName().getMessage());
//            }
//        }
//        if (StringUtils.isNotEmpty(detail.getBrandName())) {
//            Map<Long, BrandVo> brandMap = brandService.getMapByIds(Arrays.asList(Long.valueOf(detail.getBrandName())));
//            if (CollectionUtil.isNotEmpty(brandMap)) {
//                detail.setBrandName(brandMap.get(Long.valueOf(detail.getBrandName())).
//                        getBrandName().getMessage());
//            }
//        }
        return R.ok(detail);
    }

    /**
     * 获取任务发布详情
     *
     * @param jobId: 任务id
     * @return com.chervon.common.core.domain.R
     * <AUTHOR>
     * @date 17:30 2022/7/29
     **/
    @ApiOperation("获取固件发布详情")
    @PostMapping(value = "/release/detail")
    @Log(businessType = BusinessType.VIEW)
    public R<JobReleaseDetailVo> getReleaseDetail(@RequestBody SingleInfoReq<Long> jobId) {
        JobReleaseDetailVo releaseDetail = remoteOtaJobService.getReleaseDetail(jobId.getReq());
        if (StringUtils.isNotEmpty(releaseDetail.getCategoryName())) {
            Long categoryId = Long.valueOf(releaseDetail.getCategoryName());
            Map<Long, CategoryVo> categoryMap = categoryService.getMapByIds(Collections.singletonList(categoryId));
            if (CollectionUtil.isNotEmpty(categoryMap)) {
                releaseDetail.setCategoryName(categoryMap.get(categoryId).getCategoryName().getMessage());
            }
        }
        if (StringUtils.isNotEmpty(releaseDetail.getBrandName())) {
            Map<Long, BrandVo> brandMap = brandService.
                    getMapByIds(Arrays.asList(Long.valueOf(releaseDetail.getBrandName())));
            if (CollectionUtil.isNotEmpty(brandMap)) {
                releaseDetail.setBrandName(brandMap.get(Long.valueOf(releaseDetail.getBrandName())).
                        getBrandName().getMessage());
            }
        }

        // 获取多语言code
        String langId = releaseDetail.getReleaseContent();
        if (StringUtils.isNotBlank(langId)) {
            MultiLanguageBo bo = languageService.getById(langId);
            if (bo != null) {
                releaseDetail.setLangId(langId);
                releaseDetail.setReleaseContent(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(bo.getLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            }
        }

        return R.ok(releaseDetail);
    }

    /**
     * 任务发布配置
     *
     * @param jobConfigDto: 任务id
     * @return com.chervon.common.core.domain.R
     * <AUTHOR>
     * @date 17:30 2022/7/29
     **/
    @ApiOperation("任务发布配置")
    @PostMapping(value = "/release/config")
    @Log(businessType = BusinessType.INSERT)
    public R configJobRelease(@RequestBody JobConfigDto jobConfigDto) {
        remoteOtaJobService.configJobRelease(jobConfigDto, LocaleContextHolder.getLocale().getLanguage());
        return R.ok();
    }

    /**
     * 更新任务发布状态
     *
     * @param jobReleaseStatusDto: job状态更新dto
     * @return com.chervon.common.core.domain.R
     * <AUTHOR>
     * @date 17:30 2022/7/29
     **/
    @ApiOperation("更新任务发布状态")
    @PostMapping(value = "/status/update")
    @Log(businessType = BusinessType.UPDATE)
    public R updateStatus(@RequestBody JobReleaseStatusDto jobReleaseStatusDto) {
        remoteOtaJobService.updateStatus(jobReleaseStatusDto);
        return R.ok();
    }

    /**
     * 查看升级结果
     *
     * @param otaResultDto: job结果查询dto
     * @return com.chervon.common.core.domain.R
     * <AUTHOR>
     * @date 17:30 2022/7/29
     **/
    @ApiOperation("查看升级结果")
    @PostMapping(value = "/result/list")
    @Log(businessType = BusinessType.VIEW)
    public R<OtaResultVo> getOtaResultList(@RequestBody OtaResultDto otaResultDto) {
        OtaResultVo otaResult = remoteOtaJobService.getOtaResult(otaResultDto);
        return R.ok(otaResult);
    }

    /**
     * 获取任务下的分组名称列表
     *
     * @param jobId: 任务id
     * @return com.chervon.common.core.domain.R
     * <AUTHOR>
     * @date 17:30 2022/7/29
     **/
    @ApiOperation("获取任务下的分组名称列表")
    @PostMapping(value = "/groups")
    public R<List<String>> getGroups(@RequestBody SingleInfoReq<Long> jobId) {
        Long idReq = jobId.getReq();
        List<String> groups = remoteOtaJobService.getGroups(idReq);
        return R.ok(groups);
    }

    /**
     * 获取测试驳回原因
     *
     * @param jobId:
     * @return com.chervon.common.core.domain.R<java.lang.String>
     * <AUTHOR>
     * @date 20:43 2022/9/1
     **/
    @ApiOperation("获取测试驳回原因")
    @PostMapping(value = "/refuseReason/test")
    @Log(businessType = BusinessType.VIEW)
    public R<String> getTestRefuseReason(@RequestBody SingleInfoReq<Long> jobId) {
        String reason = remoteOtaJobService.getRefuseReason(jobId.getReq(),
                OtaJobReleaseOperation.TEST_REFUSED);
        return R.ok(reason);
    }

    /**
     * 获取发布驳回原因
     *
     * @param jobId:
     * @return com.chervon.common.core.domain.R<java.lang.String>
     * <AUTHOR>
     * @date 20:43 2022/9/1
     **/
    @ApiOperation("获取发布驳回原因")
    @PostMapping(value = "/refuseReason/release")
    @Log(businessType = BusinessType.VIEW)
    public R<String> getReleaseRefuseReason(@RequestBody SingleInfoReq<Long> jobId) {
        String reason = remoteOtaJobService.getRefuseReason(jobId.getReq(),
                OtaJobReleaseOperation.REFUSED);
        return R.ok(reason);
    }

    /**
     * 获取停止发布驳回原因
     *
     * @param jobId:
     * @return com.chervon.common.core.domain.R<java.lang.String>
     * <AUTHOR>
     * @date 20:43 2022/9/1
     **/
    @ApiOperation("获取驳回作废的原因")
    @PostMapping(value = "/refuseReason/nullify")
    @Log(businessType = BusinessType.VIEW)
    public R<String> getNullifyRefuseReason(@RequestBody SingleInfoReq<Long> jobId) {
        String reason = remoteOtaJobService.getRefuseReason(jobId.getReq(), OtaJobReleaseOperation.NULLIFY_REFUSED);
        return R.ok(reason);
    }

    /**
     * 获取停止发布驳回原因
     *
     * @param jobId:
     * @return com.chervon.common.core.domain.R<java.lang.String>
     * <AUTHOR>
     * @date 20:43 2022/9/1
     **/
    @ApiOperation("获取停止发布驳回原因")
    @PostMapping(value = "/refuseReason/stop")
    @Log(businessType = BusinessType.VIEW)
    public R<String> getStopRefuseReason(@RequestBody SingleInfoReq<Long> jobId) {
        String reason = remoteOtaJobService.getRefuseReason(jobId.getReq(),
                OtaJobReleaseOperation.STOP_REFUSED);
        return R.ok(reason);
    }

    /**
     * 获取下载固件预签名url
     *
     * @param key: 存在s3的key
     * @return com.chervon.common.core.domain.R
     * <AUTHOR>
     * @date 17:30 2022/7/29
     **/
    @ApiOperation("获取下载固件预签名url")
    @PostMapping(value = "/downloadUrl")
    public R<String> getDownloadUrl(@RequestBody SingleInfoReq<String> key) {
        return R.ok(UrlUtil.completeUrl(awsProperties.getOtaBucket().getCdnHost(), key.getReq()));
    }

}
