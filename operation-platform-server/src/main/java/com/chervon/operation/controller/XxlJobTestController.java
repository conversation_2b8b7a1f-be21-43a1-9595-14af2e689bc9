package com.chervon.operation.controller;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.operation.service.XxlJobService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * XxlJob测试类
 *
 * <AUTHOR>
 * @since 2023-02-16 16:54
 **/
@Slf4j
@RestController
@RequestMapping("/xxl/job/test")
@Api(tags = "XxlJob测试类")
public class XxlJobTestController {
    @Resource
    private XxlJobService xxlJobService;

    /**
     * 根据当前服务AppName获取执行器Id
     *
     * @return 执行器ID
     */
    @ApiModelProperty("根据当前服务AppName获取执行器Id")
    @PostMapping("/job/group/id/get")
    private int getJobGroupId() {
        return xxlJobService.getJobGroupId();
    }

    /**
     * 根据描述获取任务ID
     *
     * @param req 描述
     * @return 任务ID
     */
    @ApiModelProperty("根据描述获取任务ID")
    @PostMapping("/job/info/get")
    private int getJobInfoId(@RequestBody @Validated SingleInfoReq<String> req) {
        return xxlJobService.getJobInfoId(req.getReq());
    }

    /**
     * 获取任务ID列表
     *
     * @param req 任务描述
     * @return 任务ID列表
     */
    @ApiModelProperty("获取任务ID列表")
    @PostMapping("/job/info/list")
    private R<List<Integer>> listJobInfoIdByDesc(@RequestBody @Validated SingleInfoReq<String> req) {
        return R.ok(xxlJobService.listJobInfoIdByDesc(req.getReq()));
    }

    /**
     * 批量删除任务
     *
     * @param req 任务ID列表
     */
    @ApiModelProperty("批量删除任务")
    @PostMapping("/job/info/list/remove")
    private void removeJobInfoByIdList(@RequestBody @Validated SingleInfoReq<List<Integer>> req) {
        xxlJobService.removeJobInfoByIdList(req.getReq());
    }
}
