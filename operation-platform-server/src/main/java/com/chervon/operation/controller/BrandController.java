package com.chervon.operation.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.vo.BrandVo;
import com.chervon.operation.domain.dto.SearchNameDto;
import com.chervon.operation.domain.dto.brand.BrandAddDto;
import com.chervon.operation.domain.dto.brand.BrandEditDto;
import com.chervon.operation.service.BrandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022-07-01
 */
@Api(tags = "品牌相关")
@RestController
@RequestMapping("/brand")
public class BrandController {

    @Autowired
    private BrandService brandService;

    @Autowired
    private RemoteOperationCacheService remoteOperationCacheService;


    /**
     * 创建品牌
     *
     * @param brandAddDto 创建品牌信息
     */
    @ApiOperation("添加品牌信息")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Log(businessType = BusinessType.INSERT)
    public void addBrand(@Validated @RequestBody BrandAddDto brandAddDto) throws UnsupportedEncodingException {
        if (brandAddDto != null && brandAddDto.getBrandIcon() != null ) {
            brandAddDto.setBrandIcon(URLDecoder.decode(brandAddDto.getBrandIcon(), "UTF-8"));
        }
        brandService.addBrand(brandAddDto);
    }

    /**
     * 更新品牌
     *
     * @param brandEditDto 更新品牌信息
     */
    @ApiOperation("编辑品牌信息")
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @Log(businessType = BusinessType.EDIT)
    public void editBrand(@Validated @RequestBody BrandEditDto brandEditDto) throws UnsupportedEncodingException {
        if (brandEditDto != null && brandEditDto.getBrandIcon() != null ) {
            brandEditDto.setBrandIcon(URLDecoder.decode(brandEditDto.getBrandIcon(), "UTF-8"));
        }
        brandService.editBrand(brandEditDto);
        if (brandEditDto != null && brandEditDto.getId() != null) {
            remoteOperationCacheService.removeBrandCache(brandEditDto.getId());
        }
    }

    /**
     * 根据id删除品牌
     *
     * @param req 品牌id
     */
    @ApiOperation("删除品牌信息")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @Log(businessType = BusinessType.DELETE)
    public void deleteBrand(@Validated @RequestBody SingleInfoReq<Long> req) {
        brandService.deleteBrand(req.getReq());
        remoteOperationCacheService.removeBrandCache(req.getReq());
    }

    /**
     * 获取品牌详情
     *
     * @param req 品牌id
     * @return 返回信息
     */
    @ApiOperation("根据Id获取品牌详情")
    @Log(businessType = BusinessType.VIEW)
    @RequestMapping(value = "/detail/get", method = RequestMethod.POST)
    public BrandVo getDetail(@Validated @RequestBody SingleInfoReq<Long> req) {
        return brandService.getDetail(req.getReq());
    }

    /**
     * 分页获取品牌信息
     *
     * @param searchName 搜索品牌信息
     * @return 结果
     */
    @ApiOperation("分页获取品牌列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public PageResult<BrandVo> list(@Validated @RequestBody SearchNameDto searchName) {
        return brandService.list(searchName);
    }

    /**
     * 获取所有品牌列表
     *
     * @return 结果
     */
    @ApiOperation("获取所有品牌列表")
    @RequestMapping(value = "/all", method = RequestMethod.POST)
    public List<BrandVo> allList() {
        return brandService.allList();
    }
}
