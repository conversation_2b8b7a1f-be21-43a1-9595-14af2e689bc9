package com.chervon.operation.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.operation.api.vo.IntroductionVo;
import com.chervon.operation.domain.dto.introduction.IntroductionDeleteDto;
import com.chervon.operation.domain.dto.introduction.IntroductionDto;
import com.chervon.operation.domain.dto.introduction.IntroductionListDto;
import com.chervon.operation.service.IntroductionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 引导页管理接口
 *
 * <AUTHOR>
 * @since 2022-08-23 11:30
 **/
@Api(tags = "引导页管理")
@RestController
@RequestMapping("/introduction")
@Slf4j
public class OperationIntroductionController {

    private final IntroductionService introductionService;

    public OperationIntroductionController(IntroductionService introductionService) {
        this.introductionService = introductionService;
    }

    @ApiOperation("获取引导页列表")
    @PostMapping("/list")
    @Log(businessType = BusinessType.VIEW)
    public List<IntroductionVo> list(@RequestBody IntroductionListDto req) {
        return introductionService.list(req.getProductId());
    }

    @ApiOperation("增加引导页")
    @PostMapping("/add")
    @Log(businessType = BusinessType.INSERT)
    public void add(@RequestBody IntroductionDto req) {
        introductionService.add(req);
    }

    @ApiOperation("编辑引导页")
    @PostMapping("/edit")
    @Log(businessType = BusinessType.EDIT)
    public void edit(@RequestBody IntroductionDto req) {
        introductionService.edit(req);
    }

    @ApiOperation("删除引导页")
    @PostMapping("/delete")
    @Log(businessType = BusinessType.DELETE)
    public void delete(@RequestBody IntroductionDeleteDto req) {
        introductionService.delete(req.getProductId(), req.getIntroductionId());
    }

}
