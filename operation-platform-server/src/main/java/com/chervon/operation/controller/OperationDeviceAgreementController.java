package com.chervon.operation.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.operation.domain.dto.device.agreement.*;
import com.chervon.operation.domain.vo.device.agreement.DeviceAgreementDetailVo;
import com.chervon.operation.domain.vo.device.agreement.DeviceAgreementListVo;
import com.chervon.operation.service.DeviceAgreementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022-08-25 11:24
 **/
@Api(tags = "设备协议管理")
@RestController
@RequestMapping("/device/agreement")
public class OperationDeviceAgreementController {
    @Autowired
    private DeviceAgreementService deviceAgreementService;

    /**
     * 添加设备协议
     *
     * @param deviceAgreementAddDto 添加设备协议Dto
     * @return 添加结果
     */
    @ApiOperation("添加设备协议")
    @PostMapping("/add")
    @Log(businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody @Validated DeviceAgreementAddDto deviceAgreementAddDto) {
        deviceAgreementService.add(deviceAgreementAddDto);
        return R.ok();
    }

    /**
     * 添加设备协议新版本
     *
     * @param deviceAgreementVersionAddDto 添加设备协议Dto
     * @return 添加结果
     */
    @ApiOperation("添加设备协议新版本")
    @PostMapping("/add/version")
    @Log(businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody @Validated DeviceAgreementVersionAddDto deviceAgreementVersionAddDto) {
        deviceAgreementService.addVersion(deviceAgreementVersionAddDto);
        return R.ok();
    }

    /**
     * 获取设备协议分页列表
     *
     * @param deviceAgreementListDto 搜索条件
     * @return 设备协议分页列表
     */
    @ApiOperation("获取设备协议分页列表")
    @PostMapping("/list")
    @Log(businessType = BusinessType.VIEW)
    public R<PageResult<DeviceAgreementListVo>> list(@RequestBody @Validated DeviceAgreementListDto deviceAgreementListDto) {
        return R.ok(deviceAgreementService.list(deviceAgreementListDto));
    }

    /**
     * 获取设备协议详情
     *
     * @param deviceAgreementIdDto 设备协议Id
     * @return 设备协议详情
     */
    @ApiOperation("获取设备协议详情")
    @PostMapping("/detail")
    @Log(businessType = BusinessType.VIEW)
    public R<DeviceAgreementDetailVo> detail(@RequestBody @Validated DeviceAgreementIdDto deviceAgreementIdDto) {
        return R.ok(deviceAgreementService.detail(deviceAgreementIdDto));
    }

    /**
     * 编辑设备协议
     *
     * @param deviceAgreementEditDto 编辑设备协议Dto
     * @return 编辑结果
     */
    @ApiOperation("编辑设备协议")
    @PostMapping("/edit")
    @Log(businessType = BusinessType.EDIT)
    public R<?> update(@RequestBody @Validated DeviceAgreementEditDto deviceAgreementEditDto) {
        deviceAgreementService.edit(deviceAgreementEditDto);
        return R.ok();
    }

    /**
     * 编辑设备协议
     *
     * @param deviceAgreementIdDto 设备协议Id
     * @return 删除结果
     */
    @ApiOperation("删除设备协议")
    @PostMapping("/delete")
    @Log(businessType = BusinessType.DELETE)
    public R<?> delete(@RequestBody @Validated DeviceAgreementIdDto deviceAgreementIdDto) {
        deviceAgreementService.delete(deviceAgreementIdDto);
        return R.ok();
    }
}
