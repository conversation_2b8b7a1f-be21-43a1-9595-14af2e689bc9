package com.chervon.operation.controller;

import cn.hutool.core.util.DesensitizedUtil;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.utils.CsvUtil;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.fleet.user.api.entity.dto.FleetUserPageDto;
import com.chervon.fleet.user.api.entity.vo.FleetUserExcel;
import com.chervon.fleet.user.api.entity.vo.FleetUserVo;
import com.chervon.fleet.user.api.service.RemoteFleetUserCenterService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.bo.DictBo;
import com.chervon.operation.domain.bo.DictNodeBo;
import com.chervon.operation.service.DictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-08-15 14:18
 **/
@Slf4j
@Api(tags = "fleet账户管理")
@RestController
@RequestMapping("/fleet/user")
public class FleetUserController {

    @DubboReference
    private RemoteFleetUserCenterService remoteFleetUserCenterService;

    @Autowired
    private DictService dictService;

    private static final String FLEET_USER_ROLE_TYPE = "fleet_user_role_type";

    private static final String FLEET_USER_SOURCE_TYPE = "fleet_user_source_type";

    private static final String FLEET_USER_STATE_TYPE = "fleet_user_state_type";

    private static final String FLEET_USER_ENABLED_STATE_TYPE = "fleet_user_enabled_state_type";

    @ApiOperation("分页接口")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<FleetUserVo> page(@RequestBody FleetUserPageDto req) {
        PageResult<FleetUserVo> res = remoteFleetUserCenterService.userPage(req);
        if (res != null && !CollectionUtils.isEmpty(res.getList())) {
            res.getList().forEach(e -> {
                if (StringUtils.isNotBlank(e.getUserEmail())) {
                    e.setUserEmail(DesensitizedUtil.email(e.getUserEmail()));
                }
                if (StringUtils.isNotBlank(e.getInvitationEmail())) {
                    e.setInvitationEmail(DesensitizedUtil.email(e.getInvitationEmail()));
                }
            });
        }
        return res;
    }

    @ApiOperation("详情接口")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public FleetUserVo detail(@RequestBody SingleInfoReq<Long> req) {
        if (req.getReq() == null) {
            return new FleetUserVo();
        }
        FleetUserVo res = remoteFleetUserCenterService.userDetail(req.getReq());
        if (res != null) {
            if (StringUtils.isNotBlank(res.getUserEmail())) {
                res.setUserEmail(DesensitizedUtil.email(res.getUserEmail()));
            }
            if (StringUtils.isNotBlank(res.getInvitationEmail())) {
                res.setInvitationEmail(DesensitizedUtil.email(res.getInvitationEmail()));
            }
        }
        return res;
    }

    @ApiOperation("停用")
    @PostMapping("deactivate")
    @Log(businessType = BusinessType.DEACTIVATE)
    public void deactivate(@RequestBody SingleInfoReq<Long> req) {
        if (req.getReq() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_FLEET_COMPANY_USER_ID_NULL);
        }
        remoteFleetUserCenterService.userDeactivate(req.getReq());
    }

    @ApiOperation("启用")
    @PostMapping("enable")
    @Log(businessType = BusinessType.ENABLE)
    public void enable(@RequestBody SingleInfoReq<Long> req) {
        if (req.getReq() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_FLEET_COMPANY_USER_ID_NULL);
        }
        remoteFleetUserCenterService.userEnable(req.getReq());
    }

    @ApiOperation(value = "导出")
    @PostMapping("export")
    @Log(businessType = BusinessType.EXPORT)
    public void export(@RequestBody FleetUserPageDto req, HttpServletResponse response) throws IOException {
        List<FleetUserExcel> data = remoteFleetUserCenterService.userListData(req);
        handleDataLanguage(data, req.getZone());
        CsvUtil.export(FleetUserExcel.class, data, "FleetUser", response);
    }

    private void handleDataLanguage(List<FleetUserExcel> data, int zone) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Arrays.asList(FLEET_USER_ROLE_TYPE, FLEET_USER_SOURCE_TYPE, FLEET_USER_STATE_TYPE, FLEET_USER_ENABLED_STATE_TYPE));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        data.forEach(e -> {
            // 邮箱脱敏
            if (StringUtils.isNotBlank(e.getUserEmail())) {
                e.setUserEmail(DesensitizedUtil.email(e.getUserEmail()));
            }
            if (StringUtils.isNotBlank(e.getInvitationEmail())) {
                e.setInvitationEmail(DesensitizedUtil.email(e.getInvitationEmail()));
            }
            // 设置员工角色
            e.setUserRole(collect.get(FLEET_USER_ROLE_TYPE).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), CsvUtil.unFormat(e.getUserRole())))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            // 设置员工来源
            e.setUserSourceType(collect.get(FLEET_USER_SOURCE_TYPE).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), CsvUtil.unFormat(e.getUserSourceType())))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            // 设置员工状态
            e.setUserState(collect.get(FLEET_USER_STATE_TYPE).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), CsvUtil.unFormat(e.getUserState())))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            // 设备员工启用状态
            e.setIsEnabled(collect.get(FLEET_USER_ENABLED_STATE_TYPE).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), CsvUtil.unFormat(e.getIsEnabled())))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            // 设置时间
            if (StringUtils.isNotBlank(e.getRegisterTime())) {
                e.setRegisterTime(DateTimeZoneUtil.format(new Date(Long.parseLong(CsvUtil.unFormat(e.getRegisterTime()))), zone));
            }
            if (StringUtils.isNotBlank(e.getActiveTime())) {
                e.setActiveTime(DateTimeZoneUtil.format(new Date(Long.parseLong(CsvUtil.unFormat(e.getActiveTime()))), zone));
            }
        });
    }

}
