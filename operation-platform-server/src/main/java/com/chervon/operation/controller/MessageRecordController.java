package com.chervon.operation.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.message.api.dto.SearchMessageDto;
import com.chervon.message.api.enums.MessageTypeEnum;
import com.chervon.message.api.enums.PushMethodEnum;
import com.chervon.message.api.vo.MessageSensitiveVo;
import com.chervon.message.api.vo.MessageVo;
import com.chervon.operation.domain.dto.message.template.MessageRecordResultDto;
import com.chervon.operation.domain.vo.message.MessageRecordResultVo;
import com.chervon.operation.domain.vo.message.MessageRecordSearchVo;
import com.chervon.operation.domain.vo.message.PushMessageVo;
import com.chervon.operation.service.MessageRecordService;
import com.chervon.operation.util.ExportUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022年12月18日
 **/
@Api(tags = "手机端推送消息记录")
@RestController
@RequestMapping("/message/record")
public class MessageRecordController {

    @Autowired
    private MessageRecordService messageRecordService;
//    @DubboReference
//    private RemoteMessageService remoteMessageService;

    /**
     * 获取消息记录
     */
    @ApiOperation("获取消息记录")
    @PostMapping(value = "/list")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<MessageRecordResultVo> getMessageRecord(@RequestBody @Validated MessageRecordSearchVo messageRecordVo) {
        return messageRecordService.getMessageRecord(messageRecordVo);
    }

    /**
     * 导出消息记录结果到CSV
     */
    @ApiOperation(value = " 导出消息记录结果到CSV")
    @PostMapping("list/export/csv")
    @Log(businessType = BusinessType.EXPORT)
    public void exportMessageRecord(@Validated @RequestBody MessageRecordSearchVo messageRecordVo, HttpServletResponse response) throws IOException {
        List<MessageRecordResultVo> messageRecordList = messageRecordService.getMessageRecordList(messageRecordVo);
        List<MessageRecordResultDto> messageRecordResultDtos = transMessageRecordList(messageRecordList);
        String fileName = URLEncoder.encode("MessageRecord-" + new SimpleDateFormat("yyyyMMdd").format(new Date()),
                "UTF-8").replaceAll("\\+", "%20");
        ExportUtil.exportCsv(response, messageRecordResultDtos, fileName);
    }

    private List<MessageRecordResultDto> transMessageRecordList(List<MessageRecordResultVo> list) {
        List<MessageRecordResultDto> recordResultDtos = Lists.newArrayList();
        if (CollectionUtil.isEmpty(list)) {
            recordResultDtos.add(new MessageRecordResultDto());
            return recordResultDtos;
        }
        list.forEach(m -> {
            MessageRecordResultDto convert = ConvertUtil.convert(m, MessageRecordResultDto.class);
            convert.setMessageTypeStr(MessageTypeEnum.valueOfStatus(m.getMessageType()));
            if (CollectionUtil.isNotEmpty(m.getPushTypeCodes())) {
                List<String> pushTypes = Lists.newArrayList();
                m.getPushTypeCodes().forEach(p -> {
                    PushMethodEnum pushMethodEnum = PushMethodEnum.valueOf(p);
                    pushTypes.add(pushMethodEnum.getValue());
                });
                convert.setPushTypeCodes(pushTypes);
                convert.setMsgId(m.getMsgId().toString());
            }
            recordResultDtos.add(convert);
        });
        return recordResultDtos;
    }

    @ApiOperation("消息详情-推送记录")
    @PostMapping(value = "/detail/push/record/list")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<MessageSensitiveVo> getPushRecordPage(@Validated @RequestBody SearchMessageDto searchMessage) {
        PageResult<MessageVo> voPageResult = messageRecordService.getPushRecordPage(searchMessage);
        List<MessageVo> voList = voPageResult.getList();
        PageResult<MessageSensitiveVo> result = new PageResult<>(voPageResult.getPageNum(), voPageResult.getPageSize(), voPageResult.getTotal());
        List<MessageSensitiveVo> resultList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(voList)) {
            voList.forEach(v -> {
                MessageSensitiveVo convert = ConvertUtil.convert(v, MessageSensitiveVo.class);
                convert.setUserId(v.getUserId() != null ? v.getUserId().toString() : null);
                resultList.add(convert);
            });
            result.setList(resultList);
        }

        return result;
    }


    @ApiOperation(value = " 消息详情-推送记录导出到CSV")
    @PostMapping("/detail/push/record/export/csv")
    @Log(businessType = BusinessType.EXPORT)
    public void exportPushRecord(@Validated @RequestBody SearchMessageDto searchMessage, HttpServletResponse response) throws IOException {
        List<MessageVo> list = messageRecordService.getPushRecordList(searchMessage);
        List<PushMessageVo> pushMessageVoList = convertMessageVo(list, searchMessage.getZone());
        String fileName = URLEncoder.encode("MessagePushRecord-" + new SimpleDateFormat("yyyyMMdd").format(new Date()),
                "UTF-8").replaceAll("\\+", "%20");
        ExportUtil.exportCsv(response, pushMessageVoList, fileName);
    }

    private List<PushMessageVo> convertMessageVo(List<MessageVo> list, int zone) {
        List<PushMessageVo> pushMessageVoList = Lists.newArrayList();
        if (CollectionUtil.isEmpty(list)) {
            pushMessageVoList.add(new PushMessageVo());
            return pushMessageVoList;
        }
        list.forEach(a -> {
            PushMessageVo convert = ConvertUtil.convert(a, PushMessageVo.class);
            convert.setMessageTypeStr(MessageTypeEnum.valueOfStatus(a.getMessageType()));
            convert.setCreateTime(DateTimeZoneUtil.format(a.getCreateTime(), zone));
            convert.setPushResult(a.getPushResult() ? "成功" : "失败");
            pushMessageVoList.add(convert);
        });
        return pushMessageVoList;
    }

//    @ApiOperation("mongodb消息迁移到mysql")
//    @PostMapping("/transfer")
//    public void messageTransfer(@RequestBody TransferMessageDto transferMessageDto) {
//        remoteMessageService.transferToMySqlData(transferMessageDto);
//    }
}
