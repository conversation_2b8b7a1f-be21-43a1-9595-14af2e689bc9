package com.chervon.operation.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.middle.api.dto.device.IotGroupDeviceDto;
import com.chervon.operation.api.RemoteGroupService;
import com.chervon.operation.api.domain.dto.PageGroupUserDto;
import com.chervon.operation.domain.dto.group.GroupDto;
import com.chervon.operation.domain.dto.group.ListGroupDto;
import com.chervon.operation.domain.dto.group.PageGroupDto;
import com.chervon.operation.domain.vo.group.GroupDeviceListVo;
import com.chervon.operation.domain.vo.group.GroupUserVo;
import com.chervon.operation.domain.vo.group.GroupVo;
import com.chervon.operation.service.GroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 分组接口
 * <AUTHOR>
 * @date 18:20 2022/8/8
 **/
@RestController
@RequestMapping("/device/group")
@Api(tags = "分组接口")
public class GroupController {

    @Autowired
    private GroupService groupService;

    @Resource
    @DubboReference
    RemoteGroupService remoteGroupService;

    /**
     * 添加分组
     * <AUTHOR>
     * @date 14:42 2022/7/13
     * @param groupDto:
     * @return com.chervon.common.core.domain.R
     **/
    @ApiOperation("添加分组")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Log(businessType = BusinessType.INSERT)
    public R<?> add(@Validated @RequestBody GroupDto groupDto) {
        groupService.add(groupDto);
        return R.ok();
    }

    /**
     * 判断分组名是否存在
     * <AUTHOR>
     * @date 14:42 2022/7/13
     * @param groupName: 分组名
     * @return com.chervon.common.core.domain.R
     **/
    @ApiOperation("判断分组名是否存在")
    @RequestMapping(value = "/check/name", method = RequestMethod.POST)
    public R<Boolean> checkGroupName(@Validated @RequestBody SingleInfoReq<String> groupName) {
        return R.ok(groupService.checkGroupName(groupName));
    }

    /**
     * 查看设备分组结果
     * <AUTHOR>
     * @date 14:51 2022/7/13
     * @param iotGroupDeviceDto:
     * @return com.chervon.common.core.domain.R<com.chervon.iot.middle.api.vo.device.IotDeviceQueryListVo>
     **/
    @ApiOperation("查看设备分组结果")
    @Log(businessType = BusinessType.VIEW)
    @RequestMapping(value = "/device/query", method = RequestMethod.POST)
    public R<GroupDeviceListVo> queryDevice(@RequestBody IotGroupDeviceDto iotGroupDeviceDto) {
        GroupDeviceListVo listVo = groupService.queryDevices(iotGroupDeviceDto);
        return R.ok(listVo);
    }

    /**
     * 查看用户分组结果
     * <AUTHOR>
     * @date 14:51 2022/7/13
     * @param pageRequest:
     * @return com.chervon.common.core.domain.R<com.chervon.iot.middle.api.vo.device.IotDeviceQueryListVo>
     **/
    @ApiOperation("查看用户分组结果")
    @Log(businessType = BusinessType.VIEW)
    @RequestMapping(value = "/user/query", method = RequestMethod.POST)
    public R<PageResult<GroupUserVo>> queryUser(@RequestBody PageGroupUserDto pageRequest) {
        PageResult<GroupUserVo> result = groupService.queryUsers(pageRequest);
        return R.ok(result);
    }

    /**
     * 修改分组
     * <AUTHOR>
     * @date 14:51 2022/7/13
     * @param groupDto:
     * @return com.chervon.common.core.domain.R
     **/
    @ApiOperation("修改分组")
    @Log(businessType = BusinessType.EDIT)
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public R<?> edit(@Validated @RequestBody GroupDto groupDto) {
        groupService.edit(groupDto);
        return R.ok();
    }

    /**
     * 删除分组
     * <AUTHOR>
     * @date 14:51 2022/7/13
     * @param groupName:
     * @return com.chervon.common.core.domain.R
     **/
    @ApiOperation("删除分组")
    @Log(businessType = BusinessType.DELETE)
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public R<List<String>> delete(@Validated @RequestBody SingleInfoReq<String> groupName) {
        return R.ok(groupService.delete(groupName));
    }

    /**
     * 分页获取分组列表
     * <AUTHOR>
     * @date 15:50 2022/7/19
     * @param pageGroupDto:
     * @return com.chervon.common.core.domain.R<?>
     **/
    @ApiOperation("分页获取分组列表")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public PageResult<GroupVo> page(@Validated @RequestBody PageGroupDto pageGroupDto) {
        return groupService.getPage(pageGroupDto);
    }

    /**
     * 获取分组列表
     * <AUTHOR>
     * @date 15:50 2022/7/19
     * @param listGroupDto:
     * @return com.chervon.common.core.domain.R<?>
     **/
    @ApiOperation("获取分组列表")
    @Log(businessType = BusinessType.VIEW)
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public List<GroupVo> listGroup(@Validated @RequestBody ListGroupDto listGroupDto) {
        return groupService.listGroup(listGroupDto);
    }

    /**
     * 根据用户id获取分组名称列表
     * <AUTHOR>
     * @date 15:50 2022/7/19
     * @param userId:
     * @return com.chervon.common.core.domain.R<?>
     **/
    @ApiOperation("根据用户id获取分组名称列表")
    @RequestMapping(value = "/groupNames", method = RequestMethod.POST)
    public List<String> listGroupNames(@Validated @RequestBody SingleInfoReq<Long> userId) {
        return remoteGroupService.listUserGroupName(userId.getReq());
    }

    /**
     * 根据用户id获取Redis中定时反查的分组名称列表,自测用接口
     *
     * @param req 用户ID
     * @return 分组名称列表
     */
    @ApiOperation("根据用户id获取Redis中定时反查的分组名称列表,自测用接口")
    @PostMapping("/group/names/redis")
    public List<String> listUserBelongedGroups(@RequestBody @Validated SingleInfoReq<String> req) {
        List<String> groupNames = RedisUtils.getCacheObject(RedisConstant.OPERATION_USER_GROUP + req);
        return CollectionUtils.isEmpty(groupNames) ? new ArrayList<>() : groupNames;
    }
}
