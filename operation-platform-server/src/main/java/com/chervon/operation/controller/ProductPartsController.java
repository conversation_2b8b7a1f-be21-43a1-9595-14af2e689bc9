package com.chervon.operation.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.operation.domain.dto.parts.product.ProductPartsAddDto;
import com.chervon.operation.domain.dto.parts.product.ProductPartsEditDto;
import com.chervon.operation.domain.dto.parts.product.ProductPartsPageDto;
import com.chervon.operation.domain.vo.parts.product.ProductPartsVo;
import com.chervon.operation.service.ProductPartsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-10
 */
@Api(tags = "产品下配件清单接口")
@RestController
@RequestMapping("/product/parts")
public class ProductPartsController {

    @Autowired
    private ProductPartsService productPartsService;

    @ApiOperation("分页")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<ProductPartsVo> page(@RequestBody ProductPartsPageDto req) {
        Assert.notNull(req.getProductId(), ErrorCode.PARAMETER_NOT_PROVIDED,"productId");
        return productPartsService.page(req);
    }

    @ApiOperation("添加配件")
    @PostMapping("add")
    @Log(businessType = BusinessType.INSERT)
    public void add(@RequestBody ProductPartsAddDto req) {
        productPartsService.add(req);
    }

    @ApiOperation("产品下配件详情---传入instanceId")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public ProductPartsVo detail(@RequestBody SingleInfoReq<Long> req) {
        return productPartsService.detail(req.getReq());
    }

    @ApiOperation("编辑产品下配件信息")
    @PostMapping("edit")
    @Log(businessType = BusinessType.EDIT)
    public void edit(@RequestBody ProductPartsEditDto req) {
        productPartsService.edit(req);
    }

    @ApiOperation("删除---传入instanceId")
    @PostMapping("delete")
    @Log(businessType = BusinessType.DELETE)
    public void delete(@RequestBody SingleInfoReq<Long> req) {
        productPartsService.delete(req.getReq());
    }

    @ApiOperation("查询产品下已经添加的配件id集合--传入productId")
    @PostMapping("findPartsIdList")
    public List<Long> findPartsIdList(@RequestBody SingleInfoReq<Long> req) {
        return productPartsService.findPartsIdList(req.getReq());
    }

    @ApiOperation("查询产品下所有配件（排序时使用）---传入productId")
    @PostMapping("list")
    @Log(businessType = BusinessType.VIEW)
    public List<ProductPartsVo> list(@RequestBody SingleInfoReq<Long> req) {
        return productPartsService.list(req.getReq());
    }

    @ApiOperation("排序--传入instanceId集合")
    @PostMapping("order")
    @Log(businessType = BusinessType.VIEW)
    public void order(@RequestBody List<Long> req) {
        productPartsService.order(req);
    }

}
