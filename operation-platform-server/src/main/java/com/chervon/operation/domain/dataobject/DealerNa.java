package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022-08-15 14:22
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dealer_na")
public class DealerNa extends BaseDo {

    /**
     * 名称
     */
    private String name;

    /**
     * 地址
     */
    private String address;

    /**
     * 城市
     */
    private String city;

    /**
     * 州
     */
    private String state;

    /**
     * 国家
     */
    private String country;

    /**
     * 邮编
     */
    private String zipcode;

    /**
     * 电话号码
     */
    private String telephone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 网址
     */
    private String website;

    /**
     * 工作时间
     */
    private String hours;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lng;

    /**
     * 分类，多个用英文逗号隔开
     */
    private String category;

    /**
     * 缩放级别
     */
    private String zoomLevel;

    /**
     * 颜色
     */
    private String color;

    /**
     * 字体类型
     */
    private String fontClass;

    /**
     * 是否激活
     */
    private String isActive;

    /**
     * 图片
     */
    private String image;

    /**
     * 存储定位Id
     */
    private String storeLocatorId;

    /**
     * 存储id
     */
    private String storeId;

}
