package com.chervon.operation.domain.vo.group;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.operation.api.enums.GroupConditionRule;
import com.chervon.operation.api.enums.GroupConditionTypeEnum;
import com.chervon.operation.api.enums.GroupTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @className GroupVo
 * @description
 * @date 2022/7/19 15:51
 */
@Data
public class GroupVo  implements Serializable {
    /**
     * 分组id
     **/
    @ApiModelProperty(value = "分组Id", required = true)
    private Long id;

    /**
     * 分组名称
     **/
    @ApiModelProperty(value = "分组名称", required = true)
    private String groupName;

    /**
     * 分组类型
     **/
    @ApiModelProperty(value = "分组类型 DEVICE_GROUP设备分组 USER_GROUP用户分组")
    private GroupTypeEnum groupType;

    /**
     * 分组条件
     **/
    @ApiModelProperty("分组条件")
    private List<String> conditionContents;

    /**
     * 判断规则
     **/
    @ApiModelProperty("判断规则")
    private List<GroupConditionRule> conditionRules;

    /**
     * 分组条件类型列表
     **/
    @ApiModelProperty("分组条件类型列表")
    private List<GroupConditionTypeEnum> conditionTypes;

    /**
     * 条件值
     **/
    @ApiModelProperty("条件值")
    private List<String> conditionValues;

    /**
     * 创建人
     **/
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 更新人
     **/
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    /**
     * 备注
     **/
    @ApiModelProperty("备注")
    private String comments;
}
