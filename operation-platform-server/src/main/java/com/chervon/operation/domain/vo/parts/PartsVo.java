package com.chervon.operation.domain.vo.parts;

import com.chervon.operation.domain.vo.faq.parts.PartsFaqVo;
import com.chervon.operation.domain.vo.link.parts.PartsLinkVo;
import com.chervon.operation.domain.vo.manual.parts.PartsManualVo;
import com.chervon.operation.domain.vo.operationguidance.parts.PartsOperationGuidanceVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-10
 */
@Data
@ApiModel(description = "配件对象")
public class PartsVo {

    @ApiModelProperty(value = "配件id")
    private Long partsId;

    @ApiModelProperty(value = "商品型号Model#")
    private String commodityModel;

    @ApiModelProperty(value = "配件名称")
    private String name;

    @ApiModelProperty(value = "配件名称多语言id")
    private Long nameLangId;

    @ApiModelProperty(value = "配件类型1")
    private String type1;

    @ApiModelProperty(value = "配件类型2")
    private String type2;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "备注多语言id")
    private Long remarkLangId;

    @ApiModelProperty(value = "产品短描述")
    private String shortDescription;

    @ApiModelProperty(value = "产品短描述多语言id")
    private Long shortDescriptionLangId;

    @ApiModelProperty(value = "产品长描述")
    private String longDescription;

    @ApiModelProperty(value = "产品长描述多语言id")
    private Long longDescriptionLangId;

    @ApiModelProperty(value = "产品技术规格")
    private String technicalSpecification;

    @ApiModelProperty(value = "产品技术规格多语言id")
    private Long technicalSpecificationLangId;

    @ApiModelProperty(value = "图片类型")
    private String iconType;

    @ApiModelProperty(value = "图片地址")
    private String iconUrl;

    @ApiModelProperty(value = "上传的图片名")
    private String uploadIconName;

    @ApiModelProperty(value = "购买链接列表")
    private List<PartsLinkVo> link;

    @ApiModelProperty(value = "用户手册列表")
    private List<PartsManualVo> manual;

    @ApiModelProperty(value = "操作指导列表")
    private List<PartsOperationGuidanceVo> guidance;

    @ApiModelProperty(value = "faq列表")
    private List<PartsFaqVo> faq;

}
