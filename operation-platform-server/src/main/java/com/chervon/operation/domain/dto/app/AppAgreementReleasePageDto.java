package com.chervon.operation.domain.dto.app;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/20 19:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "app协议发布管理分页查询条件")
public class AppAgreementReleasePageDto extends PageRequest implements Serializable {

    @ApiModelProperty(value = "协议名称，目前不支持")
    private String title;

    @ApiModelProperty(value = "协议版本号")
    private String version;

    @ApiModelProperty(value = "协议类型code")
    private String typeCode;

    @ApiModelProperty(value = "发布状态code")
    private String statusCode;

    @ApiModelProperty(value = "申请人")
    private String applyBy;

    @ApiModelProperty(value = "审批人")
    private String approveBy;

}
