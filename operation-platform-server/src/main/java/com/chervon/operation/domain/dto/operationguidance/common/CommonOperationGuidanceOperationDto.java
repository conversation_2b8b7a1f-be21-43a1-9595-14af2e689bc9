package com.chervon.operation.domain.dto.operationguidance.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/20 20:20
 */
@Data
@ApiModel(description = "通用操作指导操作对象")
@NoArgsConstructor
public class CommonOperationGuidanceOperationDto implements Serializable {

    @ApiModelProperty(value = "通用操作指导id")
    private Long commonOperationGuidanceId;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "操作类型" +
            "delete：删除\n" +
            "apply_release：申请发布\n" +
            "ensure_release：确认发布\n" +
            "refuse_release：发布驳回\n" +
            "apply_stop_release：停止申请发布\n" +
            "ensure_stop_release：确认停止发布\n" +
            "refuse_stop_release：停止发布驳回\n" +
            "ensure_test：确认测试\n" +
            "refuse_test：测试驳回\n" +
            "cancel_apply_release：取消发布申请\n" +
            "cancel_apply_stop_release：取消停止发布申请\n" +
            "view_refuse_test_reason：查看测试被驳回原因\n" +
            "view_refuse_stop_release_reason：查看测试被驳回原因\n" +
            "view_refuse_release_reason：查看发布被驳回原因")
    private String operation;

    public CommonOperationGuidanceOperationDto(Long commonOperationGuidanceId, String operation) {
        this.commonOperationGuidanceId = commonOperationGuidanceId;
        this.operation = operation;
    }

}
