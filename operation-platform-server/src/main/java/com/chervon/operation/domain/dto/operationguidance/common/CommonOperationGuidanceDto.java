package com.chervon.operation.domain.dto.operationguidance.common;

import com.chervon.operation.domain.dto.operationguidance.OperationGuidanceDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/27 15:58
 */
@Data
@ApiModel(description = "通用操作指导")
public class CommonOperationGuidanceDto {

    @ApiModelProperty(value = "操作指导实体")
    private OperationGuidanceDto operationGuidance;

    @ApiModelProperty(value = "通用操作指导id，新增不必填")
    private Long commonOperationGuidanceId;
}
