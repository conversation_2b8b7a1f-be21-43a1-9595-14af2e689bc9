package com.chervon.operation.domain.vo.androidversion;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/10 15:56
 */
@Data
@ApiModel(description = "安卓版本对象")
public class AndroidVersionVo {

    @ApiModelProperty(value = "安卓版本id")
    private Long androidVersionId;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "版本名称")
    private String name;

    @ApiModelProperty(value = "更新内容")
    private String updateContent;

    @ApiModelProperty(value = "更新内容多语言id")
    private Long updateContentLangId;

    @ApiModelProperty("适用app类型 1 ego 2 fleet")
    private Integer businessType;

}
