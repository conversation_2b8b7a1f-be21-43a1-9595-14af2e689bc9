package com.chervon.operation.domain.vo.operationguidance.common;

import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.chervon.operation.domain.vo.operationguidance.OperationGuidanceVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/10/27 15:45
 */
@Data
@ApiModel(description = "通用操作指导发布分页数据")
public class CommonOperationGuidanceReleasePageVo {

    @ApiModelProperty(value = "通用操作指导id")
    private Long commonOperationGuidanceId;

    @ApiModelProperty(value = "操作指导")
    private OperationGuidanceVo operationGuidance;

    @ApiModelProperty(value = "发布状态code")
    private String statusCode;

    @ApiModelProperty(value = "申请人")
    private String applyBy;

    @ApiModelProperty(value = "申请时间")
    @JsonSerialize(using = com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "审核人")
    private String approvedBy;

    @ApiModelProperty(value = "审核时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime approvedTime;

    @ApiModelProperty("能否查看发布详情")
    private boolean canView;

    @ApiModelProperty("确认发布")
    private boolean canEnsureRelease;

    @ApiModelProperty("发布驳回")
    private boolean canRefuseRelease;

    @ApiModelProperty("确认停止发布")
    private boolean canEnsureStopRelease;

    @ApiModelProperty("停止发布驳回")
    private boolean canRefuseStopRelease;

    @ApiModelProperty("测试通过")
    private boolean canEnsureTest;

    @ApiModelProperty("测试驳回")
    private boolean canRefuseTest;

    @ApiModelProperty("能否查看发布被驳回原因")
    private boolean canViewRefuseReleaseReason;

    @ApiModelProperty("能否查看停止发布被驳回原因")
    private boolean canViewRefuseStopReleaseReason;

    @ApiModelProperty("能否查看测试被驳回原因")
    private boolean canViewRefuseTestReason;
}
