package com.chervon.operation.domain.vo.manual.parts;

import com.chervon.operation.domain.vo.manual.ManualVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/22 15:16
 */
@Data
@ApiModel(description = "配件用户手册数据")
public class PartsManualVo {

    @ApiModelProperty(value = "用户手册")
    private ManualVo manual;

    @ApiModelProperty(value = "记录id-用于后续接口入参")
    private Long partsManualId;

}
