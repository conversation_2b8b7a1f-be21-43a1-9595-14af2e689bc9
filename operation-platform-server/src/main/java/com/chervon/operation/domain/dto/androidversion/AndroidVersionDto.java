package com.chervon.operation.domain.dto.androidversion;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/10 17:37
 */
@Data
@ApiModel(description = "安卓版本新增请求对象")
public class AndroidVersionDto {

    @ApiModelProperty(value = "安卓版本id，新增不必填")
    private String androidVersionId;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "版本名称")
    private String name;

    @ApiModelProperty(value = "更新内容")
    private String updateContent;

    @ApiModelProperty("适用app类型 1 ego 2 fleet")
    private Integer businessType;
}
