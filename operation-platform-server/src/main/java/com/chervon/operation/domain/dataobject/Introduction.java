package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022-08-23 11:24
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("introduction")
public class Introduction extends BaseDo {

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 引导页类型：0 扫码引导页 1 配网引导页 2 固件升级引导页
     */
    private Integer type;

    /**
     * 引导图片类型：0 图片上传 1 图片连接
     */
    private Integer iconType;

    /**
     * 引导图片，url地址
     */
    private String icon;

    /**
     * 文案
     */
    private String content;

    /**
     * 文案多语言id
     */
    private Long contentLangId;

    /**
     * 文案多语言code
     */
    private String contentLangCode;

}