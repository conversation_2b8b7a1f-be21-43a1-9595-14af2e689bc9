package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用操作指导
 *
 * <AUTHOR>
 * @date 2022/10/24 16:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_faq")
public class ProductFaq extends BaseDo {

    /**
     * faq id
     */
    private Long faqId;

    /**
     * 通用id
     */
    private Long commonId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 序号
     */
    private Integer sequence;

}
