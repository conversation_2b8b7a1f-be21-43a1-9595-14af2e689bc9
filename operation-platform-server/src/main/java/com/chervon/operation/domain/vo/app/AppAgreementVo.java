package com.chervon.operation.domain.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/20 20:17
 */
@Data
@ApiModel(description = "app协议出参对象")
public class AppAgreementVo {

    @ApiModelProperty(value = "具体版本协议id")
    private Long appAgreementContentId;

    @ApiModelProperty(value = "主app协议id")
    private Long appAgreementId;

    @ApiModelProperty(value = "app协议版本号")
    private String version;

    @ApiModelProperty(value = "协议类型code")
    private String typeCode;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "消息标题多语言id")
    private Long titleLangId;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "消息内容多语言id")
    private Long contentLangId;

    @ApiModelProperty(value = "生产分组名称集合")
    private List<String> prdGroup;

    @ApiModelProperty(value = "测试分组名称集合")
    private List<String> testGroup;

    @ApiModelProperty("适用app类型 1 ego 2 fleet")
    private Integer businessType;

}
