package com.chervon.operation.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/15 21:01
 */
@Data
@ApiModel(description = "经销商对象")
public class DealerEuVo implements Serializable {

    @ApiModelProperty(value = "经销商id")
    private Long dealerId;

    @ApiModelProperty(value = "国家")
    private String countryCode;

    @ApiModelProperty(value = "名称")
    private String title;

    @ApiModelProperty(value = "地址1")
    private String addressOne;

    @ApiModelProperty(value = "地址2")
    private String addressTwo;

    @ApiModelProperty(value = "Town")
    private String town;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "Region")
    private String region;

    @ApiModelProperty(value = "邮编")
    private String postcode;

    @ApiModelProperty(value = "电话号码")
    private String phoneNumber;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "经纬度")
    private String geolocation;

    @ApiModelProperty(value = "语言类型")
    private String langCode;

    @ApiModelProperty(value = "Premium")
    private String premium;

    @ApiModelProperty(value = "pro_x_range_seller")
    private String proXRangeSeller;

    @ApiModelProperty(value = "Ride_on_seller")
    private String rideOnSeller;

    @ApiModelProperty(value = "AD")
    private String ad;
}
