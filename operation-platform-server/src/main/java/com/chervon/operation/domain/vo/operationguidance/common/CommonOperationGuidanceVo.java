package com.chervon.operation.domain.vo.operationguidance.common;

import com.chervon.operation.domain.vo.operationguidance.OperationGuidanceVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/27 16:46
 */
@Data
@ApiModel(description = "通用操作指导数据")
public class CommonOperationGuidanceVo {

    @ApiModelProperty(value = "通用操作指导id")
    private Long commonOperationGuidanceId;

    @ApiModelProperty(value = "操作指导")
    private OperationGuidanceVo operationGuidance;

    @ApiModelProperty(value = "测试分组")
    private List<String> testGroup;
}
