package com.chervon.operation.domain.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/20 14:38
 */
@Data
@ApiModel(description = "app用户&设备关系信息")
public class AppUserDeviceVo implements Serializable {

    @ApiModelProperty(value = "记录Id")
    private String id;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "设备来源")
    private List<String> deviceSource = new ArrayList<>();

    @ApiModelProperty(value = "已绑定设备ID")
    private List<String> bindDeviceId = new ArrayList<>();

    @ApiModelProperty(value = "已绑定设备SN")
    private List<String> bindDeviceSn = new ArrayList<>();

    @ApiModelProperty(value = "已绑定设备品类id")
    private List<Long> bindDeviceCategoryId = new ArrayList<>();

    @ApiModelProperty(value = "已绑定设备品牌id")
    private List<Long> bindDeviceBrandId = new ArrayList<>();

    @ApiModelProperty(value = "已绑定设备产品型号code")
    private List<String> model = new ArrayList<>();

    @ApiModelProperty(value = "已绑定设备商品型号/Model #")
    private List<String> commodityModel = new ArrayList<>();

    @ApiModelProperty(value = "是否可被分享，0:老设备不可分享，1:主用户可分享，2:子用户不可再分享")
    private List<Integer> shareType = new ArrayList<>();
}
