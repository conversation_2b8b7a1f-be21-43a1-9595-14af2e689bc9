package com.chervon.operation.domain.dto.parts;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/11/22 14:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "配件管理-关联产品分页请求对象")
public class PartsProductPageDto extends PageRequest {

    @ApiModelProperty("配件id")
    private Long partsId;

    @ApiModelProperty(value = "产品id")
    private String productId;

    @ApiModelProperty(value = "品类id")
    private Long categoryId;

    @ApiModelProperty("产品型号")
    private String model;

    @ApiModelProperty("商品型号Model#")
    private String commodityModel;

    @ApiModelProperty("产品名称")
    private String productName;

}
