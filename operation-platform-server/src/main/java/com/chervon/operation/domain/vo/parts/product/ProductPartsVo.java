package com.chervon.operation.domain.vo.parts.product;

import com.chervon.operation.domain.vo.parts.PartsVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/23 11:18
 */
@Data
@ApiModel(description = "产品-配件-分页数据")
public class ProductPartsVo {

    @ApiModelProperty(value = "产品配件id，用于后续操作入参")
    private Long instanceId;

    @ApiModelProperty(value = "配件信息")
    private PartsVo parts;

    @ApiModelProperty(value = "维保类型 1 日期 2 工时")
    private Integer maintenanceType;

    @ApiModelProperty(value = "维保周期")
    private Integer maintenancePeriod;

    @ApiModelProperty(value = "维保提醒")
    private Integer maintenanceRemind;
}
