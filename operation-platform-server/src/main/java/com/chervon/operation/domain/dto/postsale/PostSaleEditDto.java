package com.chervon.operation.domain.dto.postsale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/29 15:04
 */
@Data
@ApiModel(description = "售后内容编辑对象")
public class PostSaleEditDto {

    @ApiModelProperty(value = "产品id")
    private Long productId;

    @ApiModelProperty(value = "编辑项内容，存放长短描述、技术规格内容")
    private String item;

    @ApiModelProperty(value = "key", hidden = true)
    private String key;
}
