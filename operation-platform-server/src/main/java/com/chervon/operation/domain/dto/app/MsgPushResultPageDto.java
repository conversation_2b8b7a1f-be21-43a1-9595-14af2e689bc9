package com.chervon.operation.domain.dto.app;

import com.chervon.common.core.domain.PageRequest;
import com.chervon.message.api.enums.PushMethodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/20 19:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "app消息推送结果分页查询条件")
public class MsgPushResultPageDto extends PageRequest implements Serializable {

    @ApiModelProperty(value = "消息id", required = true)
    @NotEmpty
    private String systemMessageId;

    @ApiModelProperty(value = "消息推送方式：0墓碑消息，1APP弹窗，2APP banner, 3消息管理, 4短信")
    private PushMethodEnum pushType;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "推送结果")
    private Boolean pushResult;
}
