package com.chervon.operation.domain.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/10/27 20:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "通用管理-关联产品分页参数")
public class CommonOperationProductPageDto extends PageRequest {

    @ApiModelProperty(value = "通用id")
    private Long commonId;

    @ApiModelProperty(value = "产品id")
    private String productId;

    @ApiModelProperty(value = "品类id")
    private Long categoryId;

    @ApiModelProperty("产品型号")
    private String model;

    @ApiModelProperty("商品型号Model#")
    private String commodityModel;

    @ApiModelProperty("产品名称")
    private String productName;

}
