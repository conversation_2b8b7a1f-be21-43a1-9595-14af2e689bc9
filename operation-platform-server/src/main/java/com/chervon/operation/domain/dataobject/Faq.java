package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * faq
 *
 * <AUTHOR>
 * @date 2022/8/19 15:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("faq")
public class Faq extends BaseDo {

    /**
     * 问题类型
     */
    private String typeCode;

    /**
     * 题目
     */
    private String title;

    /**
     * 题目多语言id
     */
    private Long titleLangId;

    /**
     * 题目多语言code
     */
    private String titleLangCode;

    /**
     * 答案
     */
    private String answer;

    /**
     * 答案多语言id
     */
    private Long answerLangId;

    /**
     * 答案多语言code
     */
    private String answerLangCode;

    /**
     * 实体id
     */
    private Long instanceId;

}
