package com.chervon.operation.domain.dto.parts;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/22 14:31
 */
@Data
@ApiModel(description = "配件管理请求对象")
public class PartsDto {

    @ApiModelProperty("配件id，新增不用传")
    private Long partsId;

    @ApiModelProperty("商品型号Model#")
    private String commodityModel;

    @ApiModelProperty("配件名称")
    private String name;

    @ApiModelProperty("配件类型1")
    private String type1;

    @ApiModelProperty("配件类型2")
    private String type2;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "配件图片类型code")
    private String iconType;

    @ApiModelProperty(value = "配件图片url")
    private String iconUrl;

    @ApiModelProperty(value = "上传的配件图片名")
    private String uploadIconName;
}
