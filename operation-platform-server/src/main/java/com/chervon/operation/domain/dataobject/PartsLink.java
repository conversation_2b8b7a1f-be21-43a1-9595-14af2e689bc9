package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 配件链接
 *
 * <AUTHOR>
 * @date 2022/10/24 16:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("parts_link")
public class PartsLink extends BaseDo {

    /**
     * 链接id
     */
    private Long linkId;

    /**
     * 配件Id
     */
    private Long partsId;

    @TableField(exist = false)
    private String flag;

}
