package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 通用操作指导
 *
 * <AUTHOR>
 * @date 2022/10/24 16:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("common_operation_guidance")
public class CommonOperationGuidance extends BaseDo {

    /**
     * 操作指导id
     */
    private Long operationGuidanceId;

    /**
     * 文件名称多语言id
     */
    private Long nameLangId;

}
