package com.chervon.operation.domain.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/20 20:17
 */
@Data
@ApiModel(description = "营销消息出参对象")
public class MarketingMsgVo {

    @ApiModelProperty(value = "营销消息id")
    private Long marketingMsgId;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "消息标题多语言id")
    private Long titleLangId;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "消息内容多语言id")
    private Long contentLangId;

    @ApiModelProperty(value = "路由地址")
    private String rutePath;

    @ApiModelProperty(value = "生产分组名称集合")
    private List<String> prdGroup;

    @ApiModelProperty(value = "测试分组名称集合")
    private List<String> testGroup;

    @ApiModelProperty(value = "推送类型code")
    private List<String> pushTypeCodes;

    @ApiModelProperty(value = "推送频率")
    private String pushRateCode;

    @ApiModelProperty(value = "开始类型 1 立即 2 定时")
    private Integer startType = 2;

    @ApiModelProperty(value = "开始时间时区")
    private String startZone;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束类型  1 永久 2 定时")
    private Integer endType = 2;

    @ApiModelProperty(value = "结束时间时区")
    private String endZone;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "发布状态")
    private String statusCode;

}
