package com.chervon.operation.domain.dto.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/20 20:06
 */
@Data
@ApiModel(description = "app协议入参对象")
public class AppAgreementDto {

    @ApiModelProperty(value = "具体版本协议id，新增为空，编辑不为空")
    private Long appAgreementContentId;

    @ApiModelProperty(value = "协议版本号")
    private String version;

    @ApiModelProperty(value = "协议类型code，添加新版本不传")
    private String typeCode;

    @ApiModelProperty(value = "协议名称，添加新版本不传")
    private String title;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "生产分组名称集合")
    private List<String> prdGroup;

    @ApiModelProperty(value = "测试分组名称集合")
    private List<String> testGroup;

    @ApiModelProperty(value = "在哪个具体协议下新增版本协议，就是列表里的appAgreementContentId")
    private Long fromId;

    @ApiModelProperty("适用app类型 1 ego 2 fleet")
    private Integer businessType;

}
