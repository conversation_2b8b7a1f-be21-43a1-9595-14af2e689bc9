package com.chervon.operation.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-08-18 10:03
 **/
@Data
public class DealerNaRead {

    @ExcelProperty(value = "name", index = 0)
    private String name;

    @ExcelProperty(value = "address", index = 1)
    private String address;

    @ExcelProperty(value = "city", index = 2)
    private String city;

    @ExcelProperty(value = "state", index = 3)
    private String state;

    @ExcelProperty(value = "country", index = 4)
    private String country;

    @ExcelProperty(value = "zipcode", index = 5)
    private String zipcode;

    @ExcelProperty(value = "telephone", index = 6)
    private String telephone;

    @ExcelProperty(value = "email", index = 7)
    private String email;

    @ExcelProperty(value = "website", index = 8)
    private String website;

    @ExcelProperty(value = "hours", index = 9)
    private String hours;

    @ExcelProperty(value = "lat", index = 10)
    private String lat;

    @ExcelProperty(value = "lng", index = 11)
    private String lng;

    @ExcelProperty(value = "category", index = 12)
    private String category;

    @ExcelProperty(value = "zoomLevel", index = 13)
    private String zoomLevel;

    @ExcelProperty(value = "color", index = 14)
    private String color;

    @ExcelProperty(value = "fontclass", index = 15)
    private String fontClass;

    @ExcelProperty(value = "is_active", index = 16)
    private String isActive;

    @ExcelProperty(value = "image", index = 17)
    private String image;

    @ExcelProperty(value = "storelocator_id", index = 18)
    private String storeLocatorId;

    public String getName() {
        return CsvUtil.unFormat(this.name);
    }

    public String getAddress() {
        return CsvUtil.unFormat(this.address);
    }

    public String getCity() {
        return CsvUtil.unFormat(this.city);
    }

    public String getState() {
        return CsvUtil.unFormat(this.state);
    }

    public String getCountry() {
        return CsvUtil.unFormat(this.country);
    }

    public String getZipcode() {
        return CsvUtil.unFormat(this.zipcode);
    }

    public String getTelephone() {
        return CsvUtil.unFormat(this.telephone);
    }

    public String getEmail() {
        return CsvUtil.unFormat(this.email);
    }

    public String getWebsite() {
        return CsvUtil.unFormat(this.website);
    }

    public String getHours() {
        return CsvUtil.unFormat(this.hours);
    }

    public String getLat() {
        return CsvUtil.unFormat(this.lat);
    }

    public String getLng() {
        return CsvUtil.unFormat(this.lng);
    }

    public String getCategory() {
        return CsvUtil.unFormat(this.category);
    }

    public String getZoomLevel() {
        return CsvUtil.unFormat(this.zoomLevel);
    }

    public String getColor() {
        return CsvUtil.unFormat(this.color);
    }

    public String getFontClass() {
        return CsvUtil.unFormat(this.fontClass);
    }

    public String getIsActive() {
        return CsvUtil.unFormat(this.isActive);
    }

    public String getImage() {
        return CsvUtil.unFormat(this.image);
    }

    public String getStoreLocatorId() {
        return CsvUtil.unFormat(this.storeLocatorId);
    }
}
