package com.chervon.operation.domain.vo.app;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/20 14:38
 */
@Data
public class AppUserExcel implements Serializable {

    @Alias("邮箱")
    private String email;

    @Alias("用户ID")
    private String userId;

    @Alias("APP在线状态")
    private String appPresence;

    @Alias("最后登录时间")
    private String lastLoginTime;

    @Alias("IP地址")
    private String ip;

    @Alias("手机型号")
    private String phoneModel;

    @Alias("系统版本号")
    private String phoneOsVersion;

    @Alias("APP类型")
    private String appType;

    @Alias("APP版本号")
    private String appVersion;

    @Alias("用户类型")
    private String userType;

    @Alias("用户来源")
    private String userSource;

    @Alias("注册时间")
    private String registerTime;

    public String getEmail() {
        return CsvUtil.format(this.email);
    }

    public String getUserId() {
        return CsvUtil.format(this.userId);
    }

    public String getAppPresence() {
        return CsvUtil.format(this.appPresence);
    }

    public String getLastLoginTime() {
        return CsvUtil.format(this.lastLoginTime);
    }

    public String getIp() {
        return CsvUtil.format(this.ip);
    }

    public String getPhoneModel() {
        return CsvUtil.format(this.phoneModel);
    }

    public String getPhoneOsVersion() {
        return CsvUtil.format(this.phoneOsVersion);
    }

    public String getAppType() {
        return CsvUtil.format(this.appType);
    }

    public String getAppVersion() {
        return CsvUtil.format(this.appVersion);
    }

    public String getUserType() {
        return CsvUtil.format(this.userType);
    }

    public String getUserSource() {
        return CsvUtil.format(this.userSource);
    }

    public String getRegisterTime() {
        return CsvUtil.format(this.registerTime);
    }
}
