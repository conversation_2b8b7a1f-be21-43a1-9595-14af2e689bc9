package com.chervon.operation.domain.dto.parts.product;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/11/23 11:15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "产品-配件-分页参数")
public class ProductPartsPageDto extends PageRequest {

    @ApiModelProperty(value = "产品id")
    private Long productId;

    @ApiModelProperty("配件ID")
    private String partsId;
}
