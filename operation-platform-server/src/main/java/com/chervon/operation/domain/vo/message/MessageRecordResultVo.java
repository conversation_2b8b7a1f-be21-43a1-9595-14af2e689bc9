package com.chervon.operation.domain.vo.message;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022年12月18日
 **/
@Data
public class MessageRecordResultVo implements Serializable {

	@ApiModelProperty(value = "系统消息id")
	private Long msgId;

	@ApiModelProperty(value = "消息类型，0系统消息，1营销消息, 2设备消息")
	private Integer messageType;

	@ApiModelProperty(value = "消息标题")
	private String title;

	@ApiModelProperty(value = "消息内容")
	private String content;


	@ApiModelProperty(value = "推送类型code")
	private List<String> pushTypeCodes;

	@ApiModelProperty(value = "推送条数")
	private Integer pushAllNum;

	@ApiModelProperty(value = "推送成功条数")
	private Integer pushSuccessNum;

	@ApiModelProperty(value = "失败条数")
	private Integer pushFailNum;

	/**
	 * 产品id
	 */
	@ApiModelProperty("产品id")
	private Long productId;
}
