package com.chervon.operation.domain.dto.postsale;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-11-29 10:14
 **/
@Data
public class PostSaleFaqListDto implements Serializable {

    @ApiModelProperty(value = "产品id")
    private Long productId;

    @ApiModelProperty(value = "问题id")
    private String instanceId;

    @ApiModelProperty(value = "问题类型")
    private String typeCode;

    @ApiModelProperty(value = "问题题目")
    private String title;

    @ApiModelProperty(value = "题目多语言id")
    private String titleLangId;
}
