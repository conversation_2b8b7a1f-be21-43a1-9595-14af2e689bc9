package com.chervon.operation.domain.vo.app;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/8/20 19:44
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "app营销消息管理分页数据")
public class MarketingMsgManagePageVo extends MarketingMsgVo {

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    @ApiModelProperty("能否查看")
    private boolean canView;

    @ApiModelProperty("能否修改")
    private boolean canUpdate;

    @ApiModelProperty("能否删除")
    private boolean canDelete;

    @ApiModelProperty("能否复制")
    private boolean canCopy;

    @ApiModelProperty("能否申请发布")
    private boolean canApplyRelease;

    @ApiModelProperty("能否取消发布申请")
    private boolean canCancelApplyRelease;

    @ApiModelProperty("能否申请停止发布")
    private boolean canApplyStopRelease;

    @ApiModelProperty("能否取消停止发布申请")
    private boolean canCancelApplyStopRelease;

    @ApiModelProperty("能否查看发布被驳回原因")
    private boolean canViewRefuseReleaseReason;

    @ApiModelProperty("能否查看停止发布被驳回原因")
    private boolean canViewRefuseStopReleaseReason;

    @ApiModelProperty("能否查看测试被驳回原因")
    private boolean canViewRefuseTestReason;
}
