package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/8/24 15:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_list_order")
public class AppListOrder extends BaseDo {

    /**
     * 类别名称code， 1 品类  2 商品型号/Model，实际是产品id
     */
    private String itemCode;


}
