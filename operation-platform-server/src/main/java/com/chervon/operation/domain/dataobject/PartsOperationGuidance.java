package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用操作指导
 *
 * <AUTHOR>
 * @date 2022/10/24 16:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("parts_operation_guidance")
public class PartsOperationGuidance extends BaseDo {

    /**
     * 操作指导id
     */
    private Long operationGuidanceId;

    /**
     * 配件Id
     */
    private Long partsId;

    /**
     * 序号，正序
     */
    private Integer sequence;

}
