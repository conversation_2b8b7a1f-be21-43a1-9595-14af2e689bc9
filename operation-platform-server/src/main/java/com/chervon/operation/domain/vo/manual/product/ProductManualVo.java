package com.chervon.operation.domain.vo.manual.product;

import com.chervon.operation.domain.vo.manual.ManualVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/29 14:57
 */
@Data
@ApiModel(description = "产品用户手册数据")
public class ProductManualVo {

    @ApiModelProperty(value = "用户手册")
    private ManualVo manual;

    @ApiModelProperty(value = "记录id-用于后续接口入参")
    private Long productManualId;

}
