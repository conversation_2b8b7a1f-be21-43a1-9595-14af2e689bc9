package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 品牌信息表
 *
 * <AUTHOR>
 * @date 2022-04-25 19:00:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("brand")
public class Brand extends BaseDo {
    /**
     * 品牌名字
     */
    private String brandName;
    /**
     * 品牌图标
     */
    private String brandIcon;
    /**
     * 品牌描述
     */
    private String description;

    @ApiModelProperty("图片类型：0图片上传，1图片链接")
    private Integer iconType;

}
