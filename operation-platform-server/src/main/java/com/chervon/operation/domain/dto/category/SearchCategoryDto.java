package com.chervon.operation.domain.dto.category;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-09-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SearchCategoryDto extends PageRequest implements Serializable {
    /**
     * 品类主键ID
     */
    @ApiModelProperty("品类主键Id")
    private String id;
    /**
     * 品类名称(模糊搜索)
     */
    @ApiModelProperty("品类名称(模糊搜索)")
    private String name;
    /**
     * 品类名称多语言ID
     */
    @ApiModelProperty("品类名称多语言ID")
    private String langId;
}
