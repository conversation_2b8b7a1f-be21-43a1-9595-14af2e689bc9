package com.chervon.operation.domain.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-07-26
 */
@Data
public class EditProductDto implements Serializable {

    /**
     * 产品的Pid
     */
    @ApiModelProperty("产品Pid")
    @NotNull
    private Long pId;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    @NotEmpty
    private String productName;

    /**
     * 商品型号Model#
     */
    @ApiModelProperty("商品型号Model#")
    @NotEmpty
    private String commodityModel;

    @ApiModelProperty("产品SNCode")
    @NotEmpty
    private String productSnCode;

    /**
     * 产品图标
     */
    @ApiModelProperty("产品图标")
    @NotEmpty
    private String productIcon;

    /**
     * 品牌Id
     */
    @ApiModelProperty("品牌Id")
    @NotNull
    private Long brandId;


    /**
     * 运营平台的备注
     */
    @ApiModelProperty("运营平台Id")
    private String operationRemark;
}
