package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/8/19 15:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("post_sale")
public class PostSale extends BaseDo {

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品短描述
     */
    private String shortDescription;

    /**
     * 产品短描述多语言id
     */
    private Long shortDescriptionLangId;

    /**
     * 产品短描述多语言code
     */
    private String shortDescriptionLangCode;

    /**
     * 产品长描述
     */
    private String longDescription;

    /**
     * 产品长描述多语言id
     */
    private Long longDescriptionLangId;

    /**
     * 产品长描述多语言code
     */
    private String longDescriptionLangCode;

    /**
     * 产品技术规格
     */
    private String technicalSpecification;

    /**
     * 产品技术规格多语言id
     */
    private Long technicalSpecificationLangId;

    /**
     * 产品技术规格多语言code
     */
    private String technicalSpecificationLangCode;


    @TableField(exist = false)
    private String shortFlag;

    @TableField(exist = false)
    private String longFlag;

    @TableField(exist = false)
    private String techFlag;

}
