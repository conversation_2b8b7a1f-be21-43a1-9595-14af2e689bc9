package com.chervon.operation.domain.dto.category;

import com.chervon.common.core.domain.MultiLanguageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 20220425
 */
@Data
public class CategoryEditDto implements Serializable {

    @NotNull
    @ApiModelProperty("品类Id")
    private Long id;

    /**
     * 品类图标url
     */
    @ApiModelProperty("品类图标url")
    private String categoryIcon;

    /**
     * 品类名称
     */
    @NotNull
    @ApiModelProperty("品类名称多语言")
    private MultiLanguageVo categoryName;

    /**
     * 品类描述
     */
    @ApiModelProperty("品类描述")
    private String description;

    @ApiModelProperty("图片类型：0图片上传，1图片链接")
    @NotNull
    private Integer iconType;
}
