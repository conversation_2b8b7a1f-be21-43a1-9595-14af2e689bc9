package com.chervon.operation.domain.dto.operationguidance.product;

import com.chervon.operation.domain.dto.operationguidance.OperationGuidanceDto;
import com.chervon.operation.domain.dto.postsale.PostSaleBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/10/27 15:58
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "产品操作指导")
public class ProductOperationGuidanceDto extends PostSaleBaseDto {

    @ApiModelProperty(value = "操作指导实体")
    private OperationGuidanceDto operationGuidance;

}
