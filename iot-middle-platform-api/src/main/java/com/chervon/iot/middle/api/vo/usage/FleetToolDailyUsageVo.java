package com.chervon.iot.middle.api.vo.usage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * 设备当日使用时长
 * 机器使用总时长：1016 （秒）
 * 机器使用总次数：1018 （次）
 * 设备当天放电总量(单位：kwh)设备总功耗：1025 （设备放电量）
 * <AUTHOR>
 * @since 2024-05-29 10:52
 **/
@Data
public class FleetToolDailyUsageVo implements Serializable {
    /**
     * 设备Id
     */
    @ApiModelProperty("设备Id")
    private String deviceId;
    /**
     * 一级分类编码
     */
    @ApiModelProperty("一级分类编码")
    private String firstCategoryCode;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private Long companyId;
    /**
     * 当日日期：2024/01/10
     */
    @ApiModelProperty("当日日期：2024/01/10")
    private String date;
    /**
     * 当日总使用时长（单位：秒 不受绑定解绑影响） *
     * 机器使用总时长：1016 （秒） *
     */
    private Integer min1016;
    /**
     * 当日总使用时长（单位：秒 不受绑定解绑影响） *
     * 机器使用总时长：1016 （秒） *
     */
    private Integer max1016;
    /**
     * 当日开机次数（不受绑定解绑影响）
     * 机器使用总次数：1018 （次） *
     */
    private Integer min1018;
    /**
     * 当日开机次数（不受绑定解绑影响）
     * 机器使用总次数：1018 （次） *
     */
    private Integer max1018;
    /**
     * 设备当天放电总量(单位：kwh)
     * 设备总功耗：1025 （设备放电量）
     */
    private Integer min1025;
    /**
     * 设备当天放电总量(单位：kwh)
     * 设备总功耗：1025 （设备放电量）
     */
    private Integer max1025;

}