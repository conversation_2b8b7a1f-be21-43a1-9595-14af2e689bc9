package com.chervon.iot.middle.api.dto.device;

import java.io.Serializable;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 * @className PublishVO
 * @description
 * @date 2022/2/14 15:55
 */
@Data
public class IotPublishDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Length(min = 1, max = 255)
    String topic;

    Object payLoad;

    @Range(min = 0, max = 2)
    Integer qos = 1;
}
