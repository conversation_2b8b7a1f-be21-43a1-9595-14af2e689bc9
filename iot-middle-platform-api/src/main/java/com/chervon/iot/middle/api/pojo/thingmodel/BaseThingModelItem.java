package com.chervon.iot.middle.api.pojo.thingmodel;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @className BaseThingModelItem
 * @description 物模型项目基本类
 * @date 2022/5/10 16:35
 */
@Data
public class BaseThingModelItem implements Serializable {
    /**
     * 物模型项目ID
     **/
    @Length(min = 1, max = 128)
    private String identifier;

    /**
     * 物模型项目名称
     **/
    @Length(min = 1, max = 128)
    private String name;

    /**
     * 创建时间
     **/
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    private LocalDateTime createTime ;

    /**
     * 修改时间
     **/
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;

    /**
     * 创建人
     **/
    private String createBy;

    /**
     * 修改人
     **/
    private String updateBy;
}
