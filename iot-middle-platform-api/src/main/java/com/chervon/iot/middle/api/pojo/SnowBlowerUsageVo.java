package com.chervon.iot.middle.api.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * 扫雪机上次设备使用情况记录
 * product_1717837495206232066
 */
@Data
@ApiModel(value = "设备使用数据", description = "扫雪机上次设备使用情况记录")
public class SnowBlowerUsageVo implements Serializable {
    private static final long serialVersionUID = 1L;

    public SnowBlowerUsageVo(String deviceId) {
        this.deviceId = deviceId;
        this.workingTimeStart = 0L;
        this.workingTimeEnd = 0L;
        this.workingTimeDuration = 0L;
        this.powerConsumed = 0;
    }

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("上报的时间戳：毫秒")
    private Long ts;

    @ApiModelProperty("1032:设备开始工作时间戳：秒")
    private Long workingTimeStart;

    @ApiModelProperty("1033：设备停止工作时间戳：秒")
    private Long workingTimeEnd;

    @ApiModelProperty("工作时长间隔：秒")
    private Long workingTimeDuration;

    @ApiModelProperty("1034：当前工作的总耗电量")
    private Integer powerConsumed;

}
