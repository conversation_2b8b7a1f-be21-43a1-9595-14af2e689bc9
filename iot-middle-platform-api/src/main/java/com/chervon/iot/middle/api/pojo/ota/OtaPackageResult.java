package com.chervon.iot.middle.api.pojo.ota;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @className OtaPackageResult
 * @description
 * @date 2022/7/11 17:45
 */
@ApiModel(description = "固件升级结果")
public class OtaPackageResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("总成零件号")
    private String componentNo;

    @ApiModelProperty("总成零件版本")
    private String componentName;

    @ApiModelProperty("更新前版本")
    private String oldVersion;

    @ApiModelProperty("更新后版本")
    private Integer newVersion;

    @ApiModelProperty("升级结果")
    private String result;

}
