package com.chervon.iot.middle.api.pojo.thingmodel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
public class StructDto implements Serializable {
    /**
     * 物模型项目唯一标识符
     **/
    @ApiModelProperty("物模型项目唯一标识符")
    private String identifier;
    /**
     * 物模型项目名称
     **/
    @ApiModelProperty("参数名称多语言Id")
    private String name;
    /**
     * 描述
     */
    @ApiModelProperty("参数描述")
    private String desc;
    /**
     * 数据类型: int(原生)，float(原生)，bool(0或1的int类型)，enum(int类型)，String
     */
    @NotEmpty
    @ApiModelProperty("数据类型: int(原生)，float(原生)，bool(0或1的int类型)，enum(int类型)，String")
    private String dataType;
    /**
     * 属性规范
     * 其中bool和enum类型中，key是数值本身，value是多语言Id
     * 数值性的，key如下所示：单位类型unitType,单位名称unitName,数值范围numberRange,步长step，精度accuracy
     * 其中bool和enum类型的描述value为多语言Id
     **/
    @ApiModelProperty("属性规范")
    private Map<Object, Object> specs;
}
