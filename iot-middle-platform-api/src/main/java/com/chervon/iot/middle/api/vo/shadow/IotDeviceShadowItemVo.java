package com.chervon.iot.middle.api.vo.shadow;

import com.chervon.iot.middle.api.pojo.thingmodel.DataType;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @className IotDeviceShadowItemVo
 * @description 设备影子内容项vo
 * @date 2022/2/14 15:55
 */
@Data
public class IotDeviceShadowItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物模型项目ID
     **/
    @Length(min = 1, max = 128)
    private String identifier;

    /**
     * 物模型最后上报值
     **/
    private Object reported;

    /**
     * 物模型最后下发值
     **/
    private Object desired;

    /**
     * 物模型项目名称
     **/
    @Length(min = 1, max = 128)
    private String name;

    /**
     * 最后一次上报时间
     **/
    private Long reportedTime;

    /**
     * 最后一次下发时间
     **/
    private Long desiredTime;

    /**
     * 数据类型
     **/
    @NotNull(message = "dataType不能为空")
    private DataType dataType;

    /**
     * 传输类型 r上报/w下发/rw上报&下发
     **/
    @Length(min = 1, max = 128)
    private String accessMode;

    /**
     * 事件类型(info信息,warn告警,error故障)
     * @link  DeviceEventType
     **/
    @Length(min = 1, max = 128)
    private String type;

    /**
     * 服务类型 async（异步调用）或sync（同步调用）
     **/
    @Length(min = 1, max = 128)
    private String callType;
}
