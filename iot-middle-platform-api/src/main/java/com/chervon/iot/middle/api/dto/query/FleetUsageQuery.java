package com.chervon.iot.middle.api.dto.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备使用情况查询
 * product_1717837495206232066
 */
@Data
@ApiModel(value = "设备使用数据查询对象", description = "设备使用情况查询")
public class FleetUsageQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备id")
    private String deviceId;
    /**
     * 一级分类编码
     */
    @ApiModelProperty("一级分类编码")
    private String firstCategoryCode;
    /**
     * 二级分类编码
     */
    @ApiModelProperty("二级分类编码")
    private String secondCategoryCode;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private Long companyId;

    @ApiModelProperty("查询日期")
    private String date;

    @ApiModelProperty("开始工作时间")
    private Long timeStart;

    @ApiModelProperty("停止工作时间")
    private Long timeEnd;

}


