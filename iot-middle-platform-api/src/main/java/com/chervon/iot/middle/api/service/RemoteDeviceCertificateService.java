package com.chervon.iot.middle.api.service;

import com.chervon.iot.middle.api.vo.device.DeviceCertificateVo;
import java.util.List;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @since 2024-01-09 10:50:24
 * @description 设备证书关系
 */
public interface RemoteDeviceCertificateService {

    /**
     * 同步申请证书并返回证书列表信息
     * @param listDeviceId
     * @return
     */
    List<DeviceCertificateVo> applyForCertificate(List<String> listDeviceId);

    /**
     * 异步申请证书
     * @param listDeviceId 设备id
     */
    void asyncApplyForCertificate(List<String> listDeviceId);
}
