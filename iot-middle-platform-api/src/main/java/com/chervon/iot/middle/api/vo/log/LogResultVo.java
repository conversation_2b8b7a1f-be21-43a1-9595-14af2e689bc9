package com.chervon.iot.middle.api.vo.log;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 日志结果vo
 * <AUTHOR>
 * @since 2024-11-04 14:26
 **/
@Data
public class LogResultVo implements Serializable {

    private static final long serialVersionUID = 7967439622329136678L;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;
    /**
     * 设备Id
     */
    @ApiModelProperty("设备Id")
    private String deviceId;
    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;
    /**
     * 日志模板id
     */
    @ApiModelProperty("日志模板id")
    private Long logTemplateId;
    /**
     * 日志标题
     */
    @ApiModelProperty("日志标题(支持国际化语言)")
    private String title;
    /**
     * 日志原报文主键时间戳：ts，可依据此值查到原报文内容
     */
    private Long ts;
    /**
     * 日志创建时间(便于前端根据各地时区格式转换)
     */
    private Long createTime;
}
