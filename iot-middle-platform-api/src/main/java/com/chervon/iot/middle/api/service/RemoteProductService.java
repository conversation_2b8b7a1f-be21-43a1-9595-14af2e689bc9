package com.chervon.iot.middle.api.service;

import com.chervon.iot.middle.api.vo.product.IotProductListVo;
import com.chervon.iot.middle.api.vo.product.IotProductVo;

/**
 * <p>
 * 设备物模型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-18
 */
public interface RemoteProductService {

    /**
     * 创建产品
     *
     * @param iotProductVO:
     * <AUTHOR>
     * @date 18:23 2022/2/18
     * @return: com.chervon.common.domain.Result
     **/
    void createAwsProduct(IotProductVo iotProductVO);

    /**
     * 删除产品
     *
     * @param productKey:
     * <AUTHOR>
     * @date 15:37 2022/2/25
     * @return: java.lang.Boolean
     **/
    void deleteProduct(String productKey);

    /**
     * 获取产品列表
     *
     * <AUTHOR>
     * @date 15:45 2022/2/25
     * @return: java.util.List<com.chervon.common.domain.vo.iot.ProductVo>
     **/
    IotProductListVo listProducts();

    /**
     * 废弃产品
     *
     * @param productKey:
     * <AUTHOR>
     * @date 16:59 2022/2/25
     * @return: void
     **/
    void deprecateProduct(String productKey);
}
