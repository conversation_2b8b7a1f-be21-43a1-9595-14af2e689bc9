package com.chervon.iot.middle.api.dto.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * 设备使用情况查询
 * product_1717837495206232066
 * <AUTHOR>
 */
@Data
@ApiModel(value = "设备使用数据查询对象", description = "设备使用情况查询")
public class UsageShadowQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("开始工作时间")
    private Long timeStart;

    @ApiModelProperty("停止工作时间")
    private Long timeEnd;

    @ApiModelProperty("查询动态sql")
    private String sql;

    @ApiModelProperty("查询where")
    private String where;

    @ApiModelProperty("查询日期")
    private String date;

    @ApiModelProperty("工作状态")
    private Map<String, String> workState;

    @ApiModelProperty("查询工况指标集")
    private Set<String> indexList;

}
