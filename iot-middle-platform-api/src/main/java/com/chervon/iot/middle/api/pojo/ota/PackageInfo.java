package com.chervon.iot.middle.api.pojo.ota;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @className packageInfo
 * @description
 * @date 2022/7/11 17:45
 */
@ApiModel(description = "固件包信息")
@Data
public class PackageInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 本次升级内容
     **/
    @ApiModelProperty("固件包id")
    private String packageId;

    /**
     * 固件包名称
     **/
    @ApiModelProperty("固件包名称")
    private String packageName;

    /**
     * 固件包版本
     **/
    @ApiModelProperty("固件包版本")
    private String packageVersion;


    @ApiModelProperty("显示版本号")
    private String displayVersion;

    /**
     * 总成零件号
     **/
    @ApiModelProperty("总成零件号")
    private String componentNo;

    /**
     * 总成零件版本
     **/
    @ApiModelProperty("总成零件版本")
    private String componentName;

    /**
     * 最低兼容固件版本号
     **/
    @ApiModelProperty("最低兼容固件版本号")
    private String compatibleVersion;

    /**
     * 包类型：  0：全包  1：差分包
     **/
    @ApiModelProperty("包类型：  0：全包  1：差分包")
    private Integer packageType;

    /**
     * 总成零件类型
     **/
    @ApiModelProperty("总成零件类型")
    private String componentType;

    /**
     * 文件体积，单位byte
     **/
    @ApiModelProperty("文件体积，单位byte")
    private Long size;

    /**
     * 固件包预签名下载地址，有效期60s，若收到升级消息不立马下载，则需要在下载的时候通过packageKey获取url
     **/
    @ApiModelProperty("固件包预签名下载地址，有效期60s，若收到升级消息不立马下载，则需要在下载的时候通过packageKey获取url")
    private String packageUrl;

    /**
     * 固件包标识，用于获取最新的预签名url
     **/
    @ApiModelProperty("固件包标识，用于获取最新的预签名url")
    private String packageKey;

    /**
     * 固件包hash值，sha1算法，用于校验固件包完整性
     **/
    @ApiModelProperty("固件包hash值，sha1算法，用于校验固件包完整性")
    private String hash;

}
