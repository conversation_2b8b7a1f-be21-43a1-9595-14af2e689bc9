package com.chervon.iot.middle.api.vo.shadow;

import com.chervon.iot.middle.api.pojo.thingmodel.ParamData;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @className IotDeviceShadowItemVo
 * @description 设备影子属性vo
 * @date 2022/2/14 15:55
 */
@Data
public class IotDeviceShadowServiceVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物模型项目ID
     **/
    @Length(min = 1, max = 128)
    private String identifier;

    /**
     * 物模型最后上报值
     **/
    private Object reported;

    /**
     * 物模型最后下发值
     **/
    private Object desired;

    /**
     * 物模型项目名称
     **/
    @Length(min = 1, max = 128)
    private String name;

    /**
     * 最后一次上报时间
     **/
    private Long reportedTime;

    /**
     * 最后一次下发时间
     **/
    private Long desiredTime;

    /**
     * 入参列表
     **/
    private List<ParamData> inputData;

    /**
     * 出参列表
     **/
    private List<ParamData> outputData;

    /**
     * 服务类型 async（异步调用）或sync（同步调用）
     **/
    @Length(min = 1, max = 128)
    private String callType;
}
