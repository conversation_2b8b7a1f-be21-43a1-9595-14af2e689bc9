package com.chervon.iot.middle.api.vo.usage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备当日使用时长
 * 设备当天总充电时长：2023
 * 设备总充电能量：2027
 * <AUTHOR>
 * @since 2024-05-29 10:52
 **/
@Data
public class FleetChargerDailyUsageVo implements Serializable {
    /**
     * 设备Id
     */
    @ApiModelProperty("设备Id")
    private String deviceId;
    /**
     * 一级分类编码
     */
    @ApiModelProperty("一级分类编码")
    private String firstCategoryCode;
    /**
     * 二级分类编码
     */
    @ApiModelProperty("二级分类编码")
    private String secondCategoryCode;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private Long companyId;
    /**
     * 当日日期：2024/01/10
     */
    @ApiModelProperty("当日日期：2024/01/10")
    private String date;
    /**
     * 设备当天总充电时长：2023/2004
     */
    private Integer minChargeTime;
    /**
     * 设备当天总充电时长：2023
     */
    private Integer maxChargeTime;
    /**
     * 设备当天充电总量(单位：kwh)  设备总充电能量：2027
     */
    private Integer min2027;
    /**
     * 设备当天充电总量(单位：kwh)  设备总充电能量：2027
     */
    private Integer max2027;

}