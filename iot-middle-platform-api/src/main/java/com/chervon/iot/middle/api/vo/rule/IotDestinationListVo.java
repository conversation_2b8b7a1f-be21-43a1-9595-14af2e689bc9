package com.chervon.iot.middle.api.vo.rule;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @className RuleListVo
 * @description 规则引擎VO
 * @date/3/4 10:31
 */
@Data
public class IotDestinationListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则引擎列表")
    private List<IotDestinationListItem> iotDestinationListItems;

    @ApiModelProperty(value = "用于获取下一组结果的标记，如果没有其他结果，则为null")
    private String nextToken;
}
