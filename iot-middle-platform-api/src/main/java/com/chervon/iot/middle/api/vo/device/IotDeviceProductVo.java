package com.chervon.iot.middle.api.vo.device;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @className DeviceGroupVo
 * @description
 * @date 2022/2/14 15:55
 */
@Data
public class IotDeviceProductVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Length(max = 128)
    @NotNull
    private String productKey;

    @Length(max = 128)
    private String deviceId;
}
