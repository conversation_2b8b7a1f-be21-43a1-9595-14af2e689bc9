package com.chervon.iot.middle.api.enums;

import com.google.common.collect.Maps;

import java.util.Map;
import java.util.Optional;

/**
 * 设备物模型事件类型
 *
 * <AUTHOR>
 * @date 2022年11月15日
 **/
public enum DeviceEventType {

	INFO("info", "信息"),

	WARN("warn", "告警"),

	ERROR("error", "故障");


	private String type;

	private String zh;

	DeviceEventType(String type, String zh) {
		this.type = type;
		this.zh = zh;
	}

	private static final Map<String, String> map = Maps.newHashMap();

	static {
		for (DeviceEventType value : DeviceEventType.values()) {
			map.put(value.type, value.zh);
		}
	}

	public static String getZhByType(String type) {
		return Optional.of(map.get(type)).orElse(type);
	}
}
