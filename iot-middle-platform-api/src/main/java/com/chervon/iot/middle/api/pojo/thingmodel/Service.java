package com.chervon.iot.middle.api.pojo.thingmodel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @className Service
 * @description 物模型事件
 * @date 2022/5/9 13:59
 */
@Data
public class Service extends BaseThingModelItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务描述
     **/
    private String comments;

    /**
     * 服务类型 async（异步调用）或sync（同步调用）
     **/
    private String callType;

    /**
     * 入参列表
     **/
    private List<ParamData> inputData;

    /**
     * 出参列表
     **/
    private List<ParamData> outputData;

    @ApiModelProperty("是否数据同步")
    private Boolean ifCallType;
}
