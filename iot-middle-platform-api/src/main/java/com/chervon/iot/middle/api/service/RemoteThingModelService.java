package com.chervon.iot.middle.api.service;

import com.chervon.common.core.domain.PageRequest;
import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.middle.api.dto.model.EventDto;
import com.chervon.iot.middle.api.dto.model.ServiceDto;
import com.chervon.iot.middle.api.pojo.thingmodel.*;
import com.chervon.iot.middle.api.vo.product.IotThingModelDto;
import com.chervon.iot.middle.api.dto.model.PropertyDto;

import java.util.List;

/**
 * <p>
 * 设备物模型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
public interface RemoteThingModelService {


    /**
     * 添加物模型属性
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param propertyDto:
     * @return: java.lang.Boolean
     **/
    Boolean addProperty(PropertyDto propertyDto);

    /**
     * 获取物模型属性详情
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型属性id
     * @return: java.lang.Boolean
     **/
    Property getProperty(String productKey, String identifier);

    /**
     * 编辑物模型属性
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param propertyDto:
     * @return: java.lang.Boolean
     **/
    Boolean editProperty(PropertyDto propertyDto);

    /**
     * 删除物模型属性
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型属性id
     * @return: java.lang.Boolean
     **/
    Boolean deleteProperty(String productKey, String identifier);

    /**
     * 分页获取功能属性列表
     * <AUTHOR>
     * @date 17:47 2022/7/12
     * @param pageRequest:
     * @param productKey:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.product.PropertyVo>
     **/
    PageResult<Property> pageProperties(PageRequest pageRequest,
        String productKey);

    /**
     * 属性列表
     * @param productKey
     * @return
     */
    List<Property> listProperties(String productKey);

    /**
     * 添加物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param serviceDto:
     * @return: java.lang.Boolean
     **/
    Boolean addService(ServiceDto serviceDto);

    /**
     * 添加物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型服务id
     * @return: java.lang.Boolean
     **/
    Service getService(String productKey, String identifier);

    /**
     * 编辑物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param serviceDto:
     * @return: java.lang.Boolean
     **/
    Boolean editService(ServiceDto serviceDto);

    /**
     * 删除物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型服务id
     * @return: java.lang.Boolean
     **/
    Boolean deleteService(String productKey, String identifier);

    /**
     * 分页获取功能事件列表
     * <AUTHOR>
     * @date 17:47 2022/7/12
     * @param pageRequest:
     * @param productKey:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.product.ServiceVo>
     **/
    PageResult<Service> pageServices(PageRequest pageRequest, String productKey);

    /**
     * 功能事件列表
     * @param productKey:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.product.ServiceVo>
     */
    List<Service> listServices(String productKey);
    /**
     * 添加物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param serviceDto:
     * @return: java.lang.Boolean
     **/
    Boolean addEvent(EventDto serviceDto);

    /**
     * 添加物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型服务id
     * @return: java.lang.Boolean
     **/
    Event getEvent(String productKey, String identifier);

    /**
     * 获取事件类型等简单信息
     * @param productKey
     * @param identifier
     * @return
     */
    Event getSimpleEvent(String productKey, String identifier);

    /**
     * 编辑物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param serviceDto:
     * @return: java.lang.Boolean
     **/
    Boolean editEvent(EventDto serviceDto);

    /**
     * 删除物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型服务id
     * @return: java.lang.Boolean
     **/
    Boolean deleteEvent(String productKey, String identifier);

    /**
     * 分页获取功能事件列表
     * <AUTHOR>
     * @date 17:47 2022/7/12
     * @param pageRequest:
     * @param productKey:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.product.EventVo>
     **/
    PageResult<Event> pageEvents(PageRequest pageRequest, String productKey);

    /**
     * 功能事件列表
     * @param productKey
     * @return
     */
    List<Event> listEvents(String productKey);

    /**
     * 批量添加物模型属性（导入）
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param iotThingModelDto:
     * @return: java.lang.Boolean
     **/
    Boolean addThingModel(IotThingModelDto iotThingModelDto);

    /**
     * 查看物模型（导出物模型）
     * <AUTHOR>
     * @date 10:28 2022/5/10
     * @param productKey: 产品标识
     * @return: com.chervon.iot.api.vo.product.IotThingModel
     **/
    IotThingModel getThingModel(String productKey);

    /**
     * 根据名称模糊查询物模型
     * @param productId
     * @param name
     * @return
     */
    List<BaseThingModelItem> listThingModelIdentifiersLikeName(Long productId, String name);

    /**
     * 根据id获取物模型
     * @param productId
     * @param identifier
     * @return
     */
    BaseThingModelItem getThingModelByIdentifier(Long productId, String identifier);
}
