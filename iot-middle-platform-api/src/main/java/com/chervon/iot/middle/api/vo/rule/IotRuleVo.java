package com.chervon.iot.middle.api.vo.rule;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Map;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @className RuleVo
 * @description 规则引擎VO
 * @date/3/4 10:31
 */
@Data
public class IotRuleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则引擎名称", required = true)
    @Length(min = 1, max = 128)
    private String ruleName;

    @ApiModelProperty(value = "查询topic的查询语句", required = true)
    private String sql;

    @ApiModelProperty(value = "端点url", required = true)
    private String url;

    @ApiModelProperty(value = "AWS IoT发送确认消息的URL。")
    private String confirmationUrl;

    @ApiModelProperty(value = "http请求头")
    private Map<String, String> headers;

}
