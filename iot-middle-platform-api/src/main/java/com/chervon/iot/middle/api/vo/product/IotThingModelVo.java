package com.chervon.iot.middle.api.vo.product;

import com.chervon.iot.middle.api.pojo.thingmodel.Event;
import com.chervon.iot.middle.api.pojo.thingmodel.Property;
import com.chervon.iot.middle.api.pojo.thingmodel.Service;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @className IotThingModelVo
 * @description
 * @date 2022/5/10 10:11
 */
@Data
public class IotThingModelVo  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品标识
     **/
    @Length(min = 1, max = 128)
    private String productKey;

    /**
     * 物模型版本号
     **/
    private Long version;

    /**
     * 物模型属性列表
     **/
    private List<Property> properties;

    /**
     * 物模型事件列表
     **/
    private List<Event> events;

    /**
     * 物模型服务列表
     **/
    private List<Service> services;

    /**
     * 发布日期
     **/
    private LocalDateTime publishDate;

    /**
     * 下架日期
     **/
    private LocalDateTime disableDate;
}
