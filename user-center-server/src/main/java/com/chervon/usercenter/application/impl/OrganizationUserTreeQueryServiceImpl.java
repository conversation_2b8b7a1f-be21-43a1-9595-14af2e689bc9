package com.chervon.usercenter.application.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.usercenter.api.service.OrganizationUserTreeQueryService;
import com.chervon.usercenter.domain.model.organization.user.tree.OrganizationUserTreeRepository;
import com.chervon.usercenter.infrastructure.entity.OrganizationUserTreeDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> yimin.du
 * @since 2023/1/15 13:22
 */
@DubboService
@Slf4j
@Service
public class OrganizationUserTreeQueryServiceImpl implements OrganizationUserTreeQueryService {
    @Autowired
    private OrganizationUserTreeRepository organizationUserTreeRepository;

    @Override
    public String getLatest() {
        LambdaQueryWrapper<OrganizationUserTreeDo> wrapper = new LambdaQueryWrapper<OrganizationUserTreeDo>()
                .orderByDesc(OrganizationUserTreeDo::getCreateTime).last("limit 1");
        OrganizationUserTreeDo organizationUserTreeDo = organizationUserTreeRepository.getOne(wrapper);
        if (null == organizationUserTreeDo || StringUtils.isEmpty(organizationUserTreeDo.getS3key())) {
            return null;
        }
        return organizationUserTreeDo.getS3key();
    }
}
