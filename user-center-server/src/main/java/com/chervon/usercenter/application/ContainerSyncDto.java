package com.chervon.usercenter.application;

import lombok.Data;
import org.springframework.ldap.odm.annotations.Attribute;
import org.springframework.ldap.odm.annotations.Entry;
import org.springframework.ldap.odm.annotations.Id;

import javax.naming.Name;

/**
 * objectClass为container的组织节点
 * <AUTHOR>
 * @since 2022-06-16 17:11
 **/
@Data
@Entry(base = "DC=cn,DC=chervongroup,DC=net", objectClasses = "container")
public class ContainerSyncDto {
    /**
     * Id
     */
    @Id
    private Name id;
    /**
     * 组织名
     */
    @Attribute(name = "name")
    private String orgName;
    /**
     * 组织OU
     * 注意从LDAP拿过来是CN，入库是OU
     */
    @Attribute(name = "cn")
    private String ou;
    /**
     * 组织GUID
     */
    @Attribute(name = "objectGUID")
    private String ldapOrgGuid;
    /**
     * 父组织名
     */
    private String parentOrgName;
    /**
     * 父组织GUID
     */
    private String ldapParentOrgGuid;
    /**
     * distinguishedName
     */
    @Attribute(name = "distinguishedName")
    private String distinguishedName;
}
