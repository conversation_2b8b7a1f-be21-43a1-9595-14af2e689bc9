package com.chervon.usercenter.application.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.api.service.SysUserService;
import com.chervon.usercenter.config.ExceptionMessageUtil;
import com.chervon.usercenter.domain.model.sysuser.SysUser;
import com.chervon.usercenter.domain.model.sysuser.SysUserRepository;
import com.chervon.usercenter.infrastructure.entity.SysUserDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-06-14
 */
@DubboService
@Slf4j
@Service
public class SysUserServiceImpl implements SysUserService {

    @Autowired
    SysUserRepository sysUserRepository;

    @Override
    public List<String> getUserOrganizationIds(String userGuid) {
        SysUser sysUser = sysUserRepository.getByGuid(userGuid);
        if (sysUser == null) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SYS_USER_NOT_SYNCHRONIZED, userGuid);
        }
        return sysUserRepository.getUserOrganizationGuids(userGuid);
    }

    @Override
    public String getUserGuidByEmployeeNumber(String employeeNumber) {
        if (StringUtils.isBlank(employeeNumber)) {
            return null;
        }
        SysUserDo one = sysUserRepository.getOne(new LambdaQueryWrapper<SysUserDo>().eq(SysUserDo::getEmployeeNumber, employeeNumber));
        if (one == null) {
            return null;
        }
        return one.getLdapUserGuid();
    }
}
