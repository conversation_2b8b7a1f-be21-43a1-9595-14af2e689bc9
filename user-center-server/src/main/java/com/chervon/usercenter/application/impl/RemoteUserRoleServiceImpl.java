package com.chervon.usercenter.application.impl;

import com.chervon.usercenter.api.service.RemoteUserRoleService;
import com.chervon.usercenter.api.vo.OrgUserNodeVo;
import com.chervon.usercenter.api.vo.UserRoleAddVo;
import com.chervon.usercenter.domain.model.organization.OrganizationRepository;
import com.chervon.usercenter.domain.model.sysuser.SysUserRepository;
import com.chervon.usercenter.infrastructure.entity.BooleanWrapper;
import com.chervon.usercenter.infrastructure.entity.OrganizationDo;
import com.chervon.usercenter.infrastructure.entity.SysUserDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/2 15:34
 */
@Slf4j
@DubboService
@Service
public class RemoteUserRoleServiceImpl implements RemoteUserRoleService {

    private final OrganizationRepository organizationRepository;

    private final SysUserRepository sysUserRepository;

    public RemoteUserRoleServiceImpl(OrganizationRepository organizationRepository, SysUserRepository sysUserRepository) {
        this.organizationRepository = organizationRepository;
        this.sysUserRepository = sysUserRepository;
    }

    @Override
    public List<UserRoleAddVo> userRoleAdd(String lang, List<String> guids) {
        List<UserRoleAddVo> res = new ArrayList<>();
        LocaleContextHolder.setLocale(new Locale(lang));
        // 查出所有组织
        List<OrganizationDo> orgList = organizationRepository.list();
        orgList.forEach(organizationDo -> {
            if (organizationDo.getLdapParentOrgGuid() == null) {
                organizationDo.setLdapParentOrgGuid("0");
            }
        });
        // 查出所有员工
        List<SysUserDo> userList = sysUserRepository.list();
        Map<String, List<OrganizationDo>> orgGuidMap = orgList.stream().collect(Collectors.groupingBy(OrganizationDo::getLdapParentOrgGuid));
        Map<String, List<SysUserDo>> sysUserMap = userList.stream().collect(Collectors.groupingBy(SysUserDo::getOrganizationGuid));
        // 构建虚拟的root节点
        OrgUserNodeVo root = new OrgUserNodeVo();
        root.setNodeType(1);
        root.setNodeGuid("root");
        root.setNodeName("root");
        root.setSelected(false);
        OrgUserNodeVo tree = handleAllTree(root, orgGuidMap, sysUserMap, guids);
        // 后序遍历树
        BooleanWrapper bw = new BooleanWrapper();
        tree.getChildren().forEach(e -> postOrderNT(e, bw));
        while (bw.isFlag()) {
            bw.setFlag(false);
            tree.getChildren().forEach(e -> postOrderNT(e, bw));
        }
        tree.getChildren().forEach(e -> postOrderMiddle(e, res));
        return res;
    }

    // 后序遍历
    private void postOrderNT(OrgUserNodeVo node, BooleanWrapper bw) {
        if (!node.isSelected()) {
            if (node.isHasChild()) {
                boolean b = node.getChildren().stream().allMatch(OrgUserNodeVo::isSelected);
                if (b) {
                    node.setSelected(true);
                    bw.setFlag(true);
                } else {
                    for (OrgUserNodeVo e : node.getChildren()) {
                        postOrderNT(e, bw);
                    }
                }
            }
        }
    }

    // 中序遍历取选中的值
    private void postOrderMiddle(OrgUserNodeVo node, List<UserRoleAddVo> res) {
        if (node.isSelected()) {
            UserRoleAddVo vo = new UserRoleAddVo();
            vo.setType(node.getNodeType());
            vo.setGuid(node.getNodeGuid());
            res.add(vo);
        } else if (node.isHasChild()) {
            node.getChildren().forEach(e -> postOrderMiddle(e, res));
        }
    }


    private OrgUserNodeVo handleAllTree(OrgUserNodeVo current, Map<String, List<OrganizationDo>> orgGuidMap, Map<String, List<SysUserDo>> sysUserMap, List<String> guids) {
        if ("root".equals(current.getNodeGuid())) {
            // 将这个组织下的组织查出来
            List<OrganizationDo> organizationDos = orgGuidMap.get("0");
            if (!CollectionUtils.isEmpty(organizationDos)) {
                organizationDos.forEach(e -> {
                    OrgUserNodeVo node = new OrgUserNodeVo();
                    node.setNodeType(1);
                    node.setNodeGuid(e.getLdapOrgGuid());
                    node.setSelected(guids.contains(e.getLdapOrgGuid()));
                    current.getChildren().add(handleAllTree(node, orgGuidMap, sysUserMap, guids));
                });
            }
            return current;
        }
        // 将这个组织下的人也查询出来
        List<SysUserDo> sysUserDos = sysUserMap.get(current.getNodeGuid());
        if (!CollectionUtils.isEmpty(sysUserDos)) {
            sysUserDos.forEach(i -> {
                OrgUserNodeVo userNode = new OrgUserNodeVo();
                userNode.setNodeType(0);
                userNode.setNodeGuid(i.getLdapUserGuid());
                userNode.setSelected(guids.contains(i.getLdapUserGuid()));
                current.getChildren().add(userNode);
            });
        }
        // 将这个组织下的组织查出来
        List<OrganizationDo> organizationDos = orgGuidMap.get(current.getNodeGuid());
        if (!CollectionUtils.isEmpty(organizationDos)) {
            organizationDos.forEach(e -> {
                OrgUserNodeVo node = new OrgUserNodeVo();
                node.setNodeType(1);
                node.setNodeGuid(e.getLdapOrgGuid());
                node.setSelected(guids.contains(e.getLdapOrgGuid()));
                current.getChildren().add(handleAllTree(node, orgGuidMap, sysUserMap, guids));
            });
        }
        return current;
    }
}
