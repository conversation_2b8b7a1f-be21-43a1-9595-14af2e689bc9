package com.chervon.usercenter.application.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.domain.PageResult;
import com.chervon.usercenter.api.dto.SysUserPageDto;

import com.chervon.usercenter.api.service.RemoteSysUserService;
import com.chervon.usercenter.api.vo.SysUserVo;
import com.chervon.usercenter.domain.model.sysuser.SysUserRepository;
import com.chervon.usercenter.infrastructure.entity.SysUserDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/27 11:54
 */
@DubboService
@Slf4j
@Service
public class RemoteSysUserServiceImpl implements RemoteSysUserService {

    @Autowired
    private SysUserRepository sysUserRepository;

    @Override
    public PageResult<SysUserVo> sysUserPage(SysUserPageDto req) {
        PageResult<SysUserVo> res = new PageResult<>(req.getPageNum(), req.getPageSize());
        if (StringUtils.isBlank(req.getUsername()) && StringUtils.isBlank(req.getEmployeeNumber())) {
            return res;
        }
        Page<SysUserDo> page = sysUserRepository.page(new Page<>(req.getPageNum(), req.getPageSize()), new LambdaQueryWrapper<SysUserDo>()
                .like(StringUtils.isNotBlank(req.getEmployeeNumber()), SysUserDo::getEmployeeNumber, req.getEmployeeNumber())
                .like(StringUtils.isNotBlank(req.getUsername()), SysUserDo::getUserName, req.getUsername()));
        res.setTotal(page.getTotal());
        res.setList(page.getRecords().stream().map(e -> {
            SysUserVo vo = new SysUserVo();
            vo.setId(e.getId());
            vo.setUserName(e.getUserName());
            vo.setLdapUserGuid(e.getLdapUserGuid());
            vo.setEmployeeNumber(e.getEmployeeNumber());
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }
}
