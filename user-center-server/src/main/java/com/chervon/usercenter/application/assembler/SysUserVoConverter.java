package com.chervon.usercenter.application.assembler;

import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.usercenter.api.vo.SysUserVo;
import com.chervon.usercenter.domain.model.sysuser.SysUser;

/**
 * <AUTHOR>
 * @since 2022-04-28 15:37
 **/

public class SysUserVoConverter {
    public static SysUserVo userToUserVo(SysUser user) {
        SysUserVo sysUserVo = ConvertUtil.convert(user, SysUserVo.class);
        if(sysUserVo!=null) {
            sysUserVo.setId(user.getSysUserId());
        }
        return sysUserVo;
    }
}
