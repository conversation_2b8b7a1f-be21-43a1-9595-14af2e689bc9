package com.chervon.usercenter.infrastructure.converter;

import com.chervon.common.core.domain.LoginSysUser;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.usercenter.application.SysUserSyncDto;
import com.chervon.usercenter.domain.model.sysuser.SysUser;
import com.chervon.usercenter.infrastructure.entity.SysUserDo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-04-27 19:34:17
 */
public class SysUserConverter {
    public static SysUserDo sysUserConvertSysUserDo(SysUser sysUser) {
        SysUserDo target = ConvertUtil.convert(sysUser, SysUserDo.class);
        target.setId(sysUser.getSysUserId());
        return target;
    }

    public static SysUser sysUserDoConvertSysUser(SysUserDo sysUserDo) {
        SysUser target = ConvertUtil.convert(sysUserDo, SysUser.class);
        if(target!=null) {
            target.setSysUserId(sysUserDo.getId());
        }
        return target;
    }

    public static List<SysUser> sysUserDtosConvertSysUsers(List<SysUserSyncDto> sysUserSyncDtos) {
        List<SysUser> target = new ArrayList<>();
        for (SysUserSyncDto sysUserSyncDto : sysUserSyncDtos) {
            target.add(ConvertUtil.convert(sysUserSyncDto, SysUser.class));
        }
        return target;
    }

    public static List<SysUser> sysUserDosConvertSysUsers(List<SysUserDo> sysUserDoList) {
        List<SysUser> target = new ArrayList<>();
        for (SysUserDo sysUserDo : sysUserDoList) {
            target.add(sysUserDoConvertSysUser(sysUserDo));
        }
        return target;
    }

    public static List<SysUserDo> sysUserConvertSysUserDoList(List<SysUser> sysUserList) {
        List<SysUserDo> target = new ArrayList<>();
        for (SysUser sysUser : sysUserList) {
            target.add(sysUserConvertSysUserDo(sysUser));
        }
        return target;
    }

    public static LoginSysUser sysUserConvertLoginSysUser(SysUser sysUser) {
        LoginSysUser target = ConvertUtil.convert(sysUser, LoginSysUser.class);
        target.setId(sysUser.getSysUserId());
        return target;
    }
}
