package com.chervon.usercenter.infrastructure.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.usercenter.domain.model.organization.user.tree.OrganizationUserTreeRepository;
import com.chervon.usercenter.infrastructure.entity.OrganizationUserTreeDo;
import com.chervon.usercenter.infrastructure.mapper.OrganizationUserTreeMapper;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2022-06-07 17:08
 **/
@Repository
@Slf4j
public class OrganizationUserTreeRepositoryImpl extends ServiceImpl<OrganizationUserTreeMapper, OrganizationUserTreeDo>
        implements OrganizationUserTreeRepository {

    @Override
    public void addNewKeyRemoveOld(String key) {
        this.remove(Wrappers.emptyWrapper());
        OrganizationUserTreeDo organizationUserTreeDo = new OrganizationUserTreeDo();
        organizationUserTreeDo.setS3key(key);
        this.save(organizationUserTreeDo);
    }
}
