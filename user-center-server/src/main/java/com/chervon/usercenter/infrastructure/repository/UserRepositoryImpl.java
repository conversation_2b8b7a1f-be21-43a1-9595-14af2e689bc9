package com.chervon.usercenter.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.usercenter.domain.model.user.Email;
import com.chervon.usercenter.domain.model.user.Phone;
import com.chervon.usercenter.domain.model.user.User;
import com.chervon.usercenter.domain.model.user.UserRepository;
import com.chervon.usercenter.infrastructure.converter.UserConverter;
import com.chervon.usercenter.infrastructure.entity.UserDo;
import com.chervon.usercenter.infrastructure.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户-Repository实现类
 *
 * <AUTHOR>
 * @date 2022-06-14
 **/
//@Repository
@Service
@Slf4j
public class UserRepositoryImpl extends ServiceImpl<UserMapper, UserDo> implements UserRepository {

    @Override
    public User find(Long userId) {
        LambdaQueryWrapper<UserDo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserDo::getId, userId);
        UserDo userDo = this.getOne(queryWrapper);
        if (userDo == null) {
            return null;
        }
        return UserConverter.toUser(userDo);
    }

    @Override
    public List<User> find(Phone phone) {
        LambdaQueryWrapper<UserDo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserDo::getPhone, phone.getPhone());
        List<UserDo> userDoList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(userDoList)) {
            return null;
        }
        List<User> users = new ArrayList<>();
        for (UserDo userDo : userDoList) {
            User user = UserConverter.toUser(userDo);
            users.add(user);
        }
        return users;
    }

    @Override
    public User find(Email email) {
        LambdaQueryWrapper<UserDo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserDo::getEmail, email.getEmail());
        UserDo userDo = this.getOne(queryWrapper);
        if (userDo == null) {
            return null;
        }
        return UserConverter.toUser(userDo);
    }

    @Override
    public Long store(User user) {
        UserDo userDo = UserConverter.getUserDO(user);
        Long userId = null;
        if (userDo != null) {
            userDo.setUserTypeCode("old".equals(user.getUserTypeCode()) ? user.getUserTypeCode() : "new");
            userDo.setUserSourceCode("sf".equals(user.getUserSourceCode()) ? user.getUserSourceCode() : "ego");
            userDo.setLastSyncTime(LocalDateTime.now());
            if (userDo.getId() != null) {
                this.updateById(userDo);
            } else {
                this.save(userDo);
            }
            userId = userDo.getId();
        }
        return userId;
    }

    @Override
    public void delete(Long userId) {
        this.removeById(userId);
    }

    @Override
    public boolean updateSfUserIdByEmail(Email email, String sfUserId) {
        if (email == null || StringUtils.isEmpty(email.getEmail())) {
            log.error("UserRepositoryImpl#updateSfUserIdByEmail -> 要更新sfUserId的邮箱不存在");
            return false;
        }
        UserDo userDo = this.getOne(new LambdaQueryWrapper<UserDo>().eq(UserDo::getEmail, email.getEmail()).last("limit 1"));
        if (userDo == null) {
            log.error("UserRepositoryImpl#updateSfUserIdByEmail -> 要更新sfUserId的用户不存在");
            return false;
        }
        userDo.setSfUserId(sfUserId);
        return this.updateById(userDo);
    }

    @Override
    @Async
    public void updateAesPasswordAndSaltWhileLogin(Long userId, String aesPassword, String aesSalt, String appPresenceCode) {
        if (userId == null || StringUtils.isEmpty(aesPassword) || StringUtils.isEmpty(aesSalt)) {
            log.error("UserCommandServiceImpl#updateAesPasswordAndSaltWhileLogin -> 入参为空");
            return;
        }
        this.update(new LambdaUpdateWrapper<UserDo>()
                .eq(UserDo::getId, userId)
                .set(UserDo::getLastLoginTime, LocalDateTime.now())
                .set(UserDo::getAesPassword, aesPassword)
                .set(UserDo::getAesSalt, aesSalt)
                .set(UserDo::getAppPresenceCode, appPresenceCode));
    }

    @Override
    public void updateUserPhone(User user) {
        UserDo userDo = BeanCopyUtils.copy(user, UserDo.class);
        userDo.setId(user.getUserId());
        this.updateById(userDo);
    }
}
