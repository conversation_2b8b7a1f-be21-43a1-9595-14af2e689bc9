package com.chervon.usercenter.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.usercenter.api.vo.SysUserVo;
import com.chervon.usercenter.infrastructure.entity.SysUserDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-04-27 19:26:36
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUserDo> {

    /**
     * 根据搜索条件搜素用户列表
     *
     * @param employeeNumber 用户名或工号
     * @param status         用户状态
     * @param organization   组织
     * @return 用户Vo列表
     */
    List<SysUserVo> list(@Param("page") Page<SysUserVo> page, @Param("employeeNumber") String employeeNumber, @Param("status") Integer status,
                         @Param("organization") String organization);
}
