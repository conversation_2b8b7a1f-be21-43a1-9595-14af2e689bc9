package com.chervon.usercenter.infrastructure.utils;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;

/**
 * SM4 加密工具类 
 * 
 * <AUTHOR>
 * @since 2022-11-20
 */
public class Sm4Util {

	/**
	 * 将字符串进行 SM4 加密
	 */
	public static String encrypt(String str, String key) {
		// 校验秘钥 
		notIsNull(key, "请配置 SM4 算法秘钥");

		// SM4 加密 
		try {
			SymmetricCrypto sm4 = SmUtil.sm4(key.getBytes());
			return sm4.encryptHex(str);
		} catch (Exception e) {
			throw new RuntimeException("SM4加密失败：" + e.getMessage(), e);
		}
	}

	/**
	 * 将字符串按照 SM4 解密
	 */
	public static String decrypt(String str, String key) {
		// 校验秘钥
		notIsNull(key, "请配置 SM4 算法秘钥");

		// SM4 加密
		try {
			SymmetricCrypto sm4 = SmUtil.sm4(key.getBytes());
			return sm4.decryptStr(str, CharsetUtil.CHARSET_UTF_8);
		} catch (Exception e) {
			throw new RuntimeException("SM4解密失败：" + e.getMessage(), e);
		}
	}

	/**
	 * 如果给定的数据为null，则抛出异常
	 */
	public static void notIsNull(Object value, String errorMsg) {
		if(value == null || value.equals("")) {
			throw new RuntimeException(errorMsg);
		}
	}

}
