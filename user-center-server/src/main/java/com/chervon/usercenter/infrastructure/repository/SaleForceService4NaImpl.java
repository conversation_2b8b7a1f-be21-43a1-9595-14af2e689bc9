package com.chervon.usercenter.infrastructure.repository;

import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.api.vo.sf.SfQueryVo;
import com.chervon.usercenter.api.vo.sf.SfTokenVo;
import com.chervon.usercenter.api.vo.sf.SfWarrantyRecord;
import com.chervon.usercenter.domain.constant.UserCenterConstant;
import com.chervon.usercenter.infrastructure.config.SfUserConfig;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * SF平台同步操作，北美方向
 * <AUTHOR>
 * @date 2024/6/19
 *
 */
@Service(UserCenterConstant.NA)
@DubboService(group = UserCenterConstant.NA)
@Slf4j
public class SaleForceService4NaImpl extends SaleForceServiceImpl implements SaleForceService {

    @Autowired
    private SfUserConfig sfUserConfig;

    @Override
    public List<SfWarrantyRecord> listSfWarrantyLastUpdated(Long startTime) {
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        String timeQueryStr = DateFormatUtils.format(startTime, SF_DATETIME_FORMAT, TimeZone.getTimeZone("GMT+00:00"));
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/query?q=" +
                "SELECT Id,Purchase_Date__c,Place_of_Purchase_picklist__c,Place_of_Purchase__c,Product_Use_Type2__c," +
                "AccountCustomer__c,LastModifiedDate,Image_of_Receipt__c,Lost_Receipt__c,Pending__c,Gift__c," +
                "(SELECT Id,Serial_Number__c FROM Warranty_Items__r),Master_Product__c" +
                "+FROM+Warranty__c+WHERE+LastModifiedDate+>=+"+timeQueryStr;
        ParameterizedTypeReference<SfQueryVo<Warranty>> responseBodyType = new ParameterizedTypeReference<SfQueryVo<Warranty>>() {
        };
        ResponseEntity<SfQueryVo<Warranty>> response = restTemplate.exchange(url,
                HttpMethod.GET,
                new HttpEntity<String>(headers),
                responseBodyType);
        if(null == response.getBody()) {
            return Collections.emptyList();
        }

        List<Warranty> records = new ArrayList<>();
        if(null != response.getBody().getRecords()) {
            records = response.getBody().getRecords();
        }
        while(!response.getBody().getDone()) {
            String nextRecordsUrl = sfTokenVo.getInstance_url() + response.getBody().getNextRecordsUrl();
            response = restTemplate.exchange(nextRecordsUrl,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    responseBodyType);
            records.addAll(Objects.requireNonNull(response.getBody()).getRecords());
        }

        return convert(records);
    }

    private List<SfWarrantyRecord> convert(List<Warranty> records) {
        List<SfWarrantyRecord> list = new ArrayList<>();
        for(Warranty warranty : records) {
            if(warranty.getWarrantyItems() == null || warranty.getWarrantyItems().getTotalSize() <= 0) {
                continue;
            }
            SfWarrantyRecord sfWarrantyRecord = ConvertUtil.convert(warranty, SfWarrantyRecord.class);
            sfWarrantyRecord.setReceiptStatus(warranty.getGift()?"GIFT":"");
            List<WarrantyItems> warrantyItems = warranty.getWarrantyItems().getRecords();
            sfWarrantyRecord.setSn(warrantyItems.get(0).getSn());
            sfWarrantyRecord.setId(warrantyItems.get(0).getId());
            list.add(sfWarrantyRecord);
            if(warrantyItems.size() > 1) {
                for(int i = 1; i < warrantyItems.size(); i++) {
                    SfWarrantyRecord sfWarrantyRecord2 = new SfWarrantyRecord();
                    BeanUtils.copyProperties(sfWarrantyRecord, sfWarrantyRecord2);
                    sfWarrantyRecord2.setSn(warrantyItems.get(i).getSn());
                    sfWarrantyRecord2.setId(warrantyItems.get(i).getId());
                    list.add(sfWarrantyRecord2);
                }
            }
        }
        return list;
    }

    @Override
    public SfQueryVo<SfWarrantyRecord> batchSfWarranty(String queryUrl) {
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        if(Strings.isNullOrEmpty(queryUrl)) {
            queryUrl =  "/services/data/" + sfUserConfig.getApiVersion() + "/query?q=" +
                    "SELECT+Id,Purchase_Date__c,Place_of_Purchase_picklist__c,Place_of_Purchase__c,Product_Use_Type2__c," +
                    "AccountCustomer__c,LastModifiedDate,Image_of_Receipt__c,Lost_Receipt__c,Pending__c,Gift__c," +
                    "(SELECT Id,Serial_Number__c FROM Warranty_Items__r),Master_Product__c" +
                    "+FROM+Warranty__c+WHERE+AccountCustomer__c+!=+NULL";
        }
        String url = sfTokenVo.getInstance_url() + queryUrl;
        try {
            ResponseEntity<SfQueryVo<Warranty>> response = restTemplate.exchange(url,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    new ParameterizedTypeReference<SfQueryVo<Warranty>>(){});
            SfQueryVo<Warranty> body = response.getBody();
            SfQueryVo<SfWarrantyRecord> result = new SfQueryVo<>();
            result.setDone(true);
            result.setTotalSize(0);
            result.setRecords(Collections.emptyList());
            if(null == body) {
                return result;
            }

            result.setDone(body.getDone());
            result.setTotalSize(body.getTotalSize());
            result.setNextRecordsUrl(body.getNextRecordsUrl());
            result.setErrorCode(body.getErrorCode());
            if(body.getTotalSize() <= 0) {
                return result;
            }
            result.setRecords(convert(body.getRecords()));
            return result;
        } catch (Exception e) {
            log.error("listSfWarranty, queryUrl:{}, error: ", queryUrl, e);
            return null;
        }
    }

    @Override
    public List<SfWarrantyRecord> getWarrantyBySn(String sn) {
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/query?" +
                "q=SELECT Id,Expiration_Date__c,Purchase_Date__c,Place_of_Purchase__c,Place_of_Purchase_picklist__c," +
                "AccountCustomer__c,Image_of_Receipt__c,Lost_Receipt__c,Pending__c,Gift__c," +
                "LastModifiedDate,(SELECT Id,Serial_Number__c FROM Warranty_Items__r)," +
                "Product_Use_Type__c,Product_Use_Type2__c FROM Warranty__c WHERE " +
                "Id IN (SELECT Warranty__c FROM Warranty_Item__c WHERE Serial_Number__c = '" + sn + "')";
        // 请求头设置,x-www-form-urlencoded格式的数据
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        ParameterizedTypeReference<SfQueryVo<Warranty>> responseBodyType = new ParameterizedTypeReference<SfQueryVo<Warranty>>() {
        };
        ResponseEntity<SfQueryVo<Warranty>> response = restTemplate.exchange(url,
                HttpMethod.GET,
                new HttpEntity<String>(headers),
                responseBodyType);
        if(null == response.getBody()) {
            return null;
        }
        List<Warranty> records = response.getBody().getRecords();
        if(records == null || records.isEmpty()) {
            return null;
        }
        List<SfWarrantyRecord> convert = convert(records);
        if(convert.isEmpty()) {
            return null;
        }

        //过滤掉同一个用户重复的质保，保留最新
        Map<String, SfWarrantyRecord> map = new HashMap<>(convert.size());
        for(SfWarrantyRecord sfWarrantyRecord : convert) {
            SfWarrantyRecord cacheRecord = map.get(sfWarrantyRecord.getSfUserId());
            if(cacheRecord == null) {
                map.put(sfWarrantyRecord.getSfUserId(), sfWarrantyRecord);
                continue;
            }
            if(sfWarrantyRecord.getLastModifiedDate().compareTo(cacheRecord.getLastModifiedDate()) > 0) {
                map.put(sfWarrantyRecord.getSfUserId(), sfWarrantyRecord);
            }
        }
        return new ArrayList<>(map.values());
    }

    @Override
    public List<SfWarrantyRecord> getWarrantyByUser(String sfUserId) {
        if(StringUtils.isEmpty(sfUserId)) {
            return null;
        }
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/query?" +
                "q=SELECT Id,Expiration_Date__c,Purchase_Date__c,Place_of_Purchase__c,Place_of_Purchase_picklist__c," +
                "AccountCustomer__c,Image_of_Receipt__c,Lost_Receipt__c,Pending__c,Gift__c," +
                "LastModifiedDate,(SELECT Id,Serial_Number__c FROM Warranty_Items__r)," +
                "Product_Use_Type__c,Product_Use_Type2__c FROM Warranty__c WHERE " +
                "AccountCustomer__c='" + sfUserId + "'";
        // 请求头设置,x-www-form-urlencoded格式的数据
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        ParameterizedTypeReference<SfQueryVo<Warranty>> responseBodyType = new ParameterizedTypeReference<SfQueryVo<Warranty>>() {
        };
        ResponseEntity<SfQueryVo<Warranty>> response = restTemplate.exchange(url,
                HttpMethod.GET,
                new HttpEntity<String>(headers),
                responseBodyType);
        if(null == response.getBody()) {
            return null;
        }
        List<Warranty> records = response.getBody().getRecords();
        if(records == null || records.isEmpty()) {
            return null;
        }
        return convert(records);
    }

    @Data
    private static class Warranty {
        @JsonProperty("Id")
        private String id;

        @JsonProperty("AccountCustomer__c")
        private String sfUserId;

        /**
         * 字符串格式传入：yyyy-mm-dd
         */
        @JsonProperty("Purchase_Date__c")
        private String purchaseDate;

        /**
         * 选填：
         * Industrial/Professional/Commercial
         * Residential
         * Rental
         */
        @JsonProperty("Product_Use_Type2__c")
        private String useType;

        /**
         * 购买地址
         */
        @JsonProperty("Place_of_Purchase_picklist__c")
        private String purchasePlace;

        /**
         * 其他购买地址
         */
        @JsonProperty("Place_of_Purchase__c")
        private String purchasePlaceOther;

        /**
         * 发票状态。三选一
         */
        @JsonProperty("Lost_Receipt__c")
        private Boolean lost;

        /**
         * 发票状态。三选一
         */
        @JsonProperty("Pending__c")
        private Boolean pending;

        /**
         * 请查看本文档中的‘WarrantyItems’
         * 部分信息
         */
        @JsonProperty("Gift__c")
        private Boolean gift;

        /**
         * 收据
         */
        @JsonProperty("Image_of_Receipt__c")
        private String receiptUrl;

        /**
         * SF平台质保更新时间
         */
        @JsonProperty("LastModifiedDate")
        private String lastModifiedDate;

        /**
         * 要注册质保的物品(设备)列表
         */
        @JsonProperty("Warranty_Items__r")
        private SfQueryVo<WarrantyItems> warrantyItems;
    }

    @Data
    private static class WarrantyItems {
        @JsonProperty("Serial_Number__c")
        private String sn;
        @JsonProperty("Id")
        private String id;
    }

}
