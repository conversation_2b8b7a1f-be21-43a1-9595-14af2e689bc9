package com.chervon.usercenter.domain.model.user;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.ValueObject;
import com.chervon.common.core.utils.CommonUtil;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.config.ExceptionMessageUtil;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 密码
 *
 * <AUTHOR>
 * @date 2022-06-14
 **/
@Slf4j
@Getter
@ToString
public class Password implements ValueObject<Password> {

    /**
     * 密码(MD5加密后密码)
     */
    private final String password;

    /**
     * 密码盐(MD5盐)
     */
    private final String salt;

    /**
     * 对称加密密码
     */
    private final String aesPassword;
    /**
     * 对称加密密码盐
     */
    private final String aesSalt;

    /**
     * 有效性正则
     */
    private static final Pattern VALID_PATTERN = Pattern.compile(CommonConstant.PASSWORD_REGEX);

    public Password(String password, String salt, String aesPassword, String aesSalt) {
        if (StringUtils.isEmpty(password) || StringUtils.isEmpty(salt)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_PASSWORD_INVALID);
        }
        this.password = password;
        this.salt = salt;
        this.aesPassword = aesPassword;
        this.aesSalt = aesSalt;
    }

    /**
     * 密码类构造方法
     * md5(source+salt)
     *
     * @param passwordStr 明文密码
     * @param salt        MD5盐
     * @return 密码子类
     */
    public static Password create(String passwordStr, String salt) {
        return create(passwordStr, salt, null, null);
    }

    /**
     * 密码构造 不校验密码规则
     * @param salt
     * @return
     */
    public static Password createNotCheckValid(String passwordStr, String salt) {
        String sourceStr = passwordStr + salt;
        String password = CommonUtil.encrypt3ToMD5(sourceStr);
        return new Password(password, salt, null, null);
    }

    /**
     * 密码类构造方法(包括AES密码秘钥)
     * md5(source+salt)
     *
     * @param passwordStr 明文密码
     * @param salt        MD5盐
     * @param aesPassword AES加密后密码
     * @param aesSalt     AES盐
     * @return 密码子类
     */
    public static Password create(String passwordStr, String salt, String aesPassword, String aesSalt) {
        // 校验原始密码为大于8
        if (passwordStr.length() < CommonConstant.EIGHT) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_PASSWORD_INVALID);
        }
        // 校验密码复杂度：数字和字母
        if (!VALID_PATTERN.matcher(passwordStr).matches()) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_PASSWORD_INVALID);
        }
        String sourceStr = passwordStr + salt;
        String password = CommonUtil.encrypt3ToMD5(sourceStr);
        return new Password(password, salt, aesPassword, aesSalt);
    }


    @Override
    public boolean sameValueAs(Password other) {
        return other != null && this.password.equals(other.password) && this.salt.equals(other.getSalt());
    }

}
