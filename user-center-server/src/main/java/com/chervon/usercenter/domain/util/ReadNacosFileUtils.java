package com.chervon.usercenter.domain.util;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.config.ExceptionMessageUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2022-06-28
 */
@Slf4j
public class ReadNacosFileUtils {

    /**
     * 读取nacos配置文件内容
     *
     * @param serverAddr nacos地址
     * @param nameSpace  namespace
     * @param group      group
     * @param dataId     dataId
     * @return 文件内容
     */
    public static String readNacosFiles(String serverAddr, String nameSpace, String group, String dataId) {

        try {
            Properties properties = new Properties();
            properties.put(PropertyKeyConst.SERVER_ADDR, Objects.toString(serverAddr, ""));
            properties.put(PropertyKeyConst.NAMESPACE, Objects.toString(nameSpace, ""));
            ConfigService configService = NacosFactory.createConfigService(properties);
            String content = configService.getConfig(dataId, group, 5000);
            log.info("readNacosFiles:{}", content);
            return content;
        } catch (Exception exception) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_READ_NACOS_FAILED, group, dataId);
        }
    }
}
