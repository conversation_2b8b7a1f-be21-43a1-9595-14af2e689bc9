package com.chervon.usercenter.domain.model.user;

import com.chervon.common.core.domain.ValueObject;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户姓名
 *
 * <AUTHOR>
 * @date 2022-06-14
 **/
@Getter
@ToString
public class UserName implements ValueObject<UserName> {

    /**
     * 名称
     */
    private final String firstName;

    /**
     * 姓氏
     */
    private final String lastName;

    public UserName(String firstName, String lastName) {
        this.firstName = firstName;
        this.lastName = lastName;
    }


    @Override
    public boolean sameValueAs(UserName other) {
        return other != null && this.firstName.equals(other.getFirstName())
                && this.lastName.equals(other.getLastName());
    }

}
