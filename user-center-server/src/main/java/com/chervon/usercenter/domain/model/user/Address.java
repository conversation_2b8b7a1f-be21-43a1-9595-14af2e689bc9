package com.chervon.usercenter.domain.model.user;

import com.chervon.common.core.domain.ValueObject;
import lombok.Data;
import lombok.ToString;

/**
 * 用户姓名
 *
 * <AUTHOR>
 * @date 2022-06-14
 **/
@Data
@ToString
public class Address implements ValueObject<Address> {

    /**
     * 所属国家
     */
    private String country;

    /**
     * 地址1
     */
    private String addressLine;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 县
     */
    private String county;

    /**
     * 街道
     */
    private String street;


    public Address() {

    }

    public Address(String country, String addressLine, String postCode) {
        this.country = country;
        this.addressLine = addressLine;
        this.postCode = postCode;
    }

    @Override
    public boolean sameValueAs(Address other) {
        return other != null && other.equals(this);
    }

}
