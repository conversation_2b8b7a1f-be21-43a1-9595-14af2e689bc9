<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
        namespace="com.chervon.usercenter.infrastructure.mapper.OrganizationMapper">

    <resultMap id="organizationDo" type="com.chervon.usercenter.infrastructure.entity.OrganizationDo">
        <result property="id" column="id"/>
        <result property="orgName" column="org_name"/>
        <result property="ou" column="email"/>
        <result property="ldapOrgGuid" column="ldap_org_guid"/>
        <result property="ldapParentOrgGuid" column="ldap_parent_org_guid"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="syncTime" column="sync_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getChildOrgs" resultType="com.chervon.usercenter.api.vo.OrganizationDetailVo">
        select p.id, p.ldap_org_guid, p.org_name, p.ou, p.ldap_org_guid,
        p.ldap_parent_org_guid, p.create_by,
        p.create_time, p.update_by,p.update_time,
        (SELECT
        count(id)
        from user_center.organization
        where ldap_parent_org_guid = p.ldap_org_guid)
        as hasChild
        FROM user_center.organization p
        where p.is_deleted = 0 and
        <choose>
            <when test="ldapOrgGuid !=null and ldapOrgGuid != ''">
                p.ldap_parent_org_guid= #{ldapOrgGuid}
            </when>
            <otherwise>
                p.ldap_parent_org_guid is null
            </otherwise>
        </choose>
    </select>

</mapper>
